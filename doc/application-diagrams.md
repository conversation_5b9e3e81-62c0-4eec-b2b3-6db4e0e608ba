# `assignment-manager` Kafka Topology Diagrams

> This file was automatically generated from Kafka Streams topology metadata. Do not edit it manually.

## Notes

Since these diagrams are auto-generated from
[Kafka Streams topology metadata](https://ruter-as.gitlab.io/rdp/rdp-pipeline/user/features/metadata/kafka/), the
diagrams may not include external dependencies not part of the Kafka Streams topology, unless these have been
explicitly been included in the Kafka topology description file provided by the application during the build
process.


## Application Diagram

Basic application context diagram showing input / output topics:

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryTextColor': '#000000'}}}%%
graph LR
    classDef appClass fill:#cfe9fb,stroke-width:1px;
    classDef topicClass fill:#e1d5e7,stroke-width:1px;

    64998d7fc48cee5ee200f6824304021b("assignment-manager")

    63ead6e070358b9501de13c8abe01417["private.assignment.12.vehicle.assignment-attempt"]

    class 64998d7fc48cee5ee200f6824304021b appClass
    class 63ead6e070358b9501de13c8abe01417 topicClass

    63ead6e070358b9501de13c8abe01417 --> 64998d7fc48cee5ee200f6824304021b
```

## Kafka Topology Diagram

Kafka Streams topology diagram, showing the internal processing steps, input / output topics and state stores used by
the application's Kafka Streams processing logic:

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryTextColor': '#000000'}}}%%
graph TD
    classDef storeClass fill:#e7f2f8,stroke-width:1px;
    classDef topicClass fill:#e1d5e7,stroke-width:1px;
    classDef sinkClass fill:#7ab1f5,stroke-width:1px;
    classDef sourceClass fill:#7ab1f5,stroke-width:1px;
    classDef processorClass fill:#7ab1f5,stroke-width:1px;

    63ead6e070358b9501de13c8abe01417["private.assignment.12.vehicle.assignment-attempt"]

    subgraph cfcd208495d565ef66e7dff9f98764da ["Sub-topology 0 (local)"]
        f9f635786c7242ef17e9f5b2cada1a2d("input<br>attempt<br>stream")
        dc6eabac5819557cc449943d969d25ec("input<br>attempt<br>peek<br>record<br>received<br>metrics")
        12df695746f95f56e6b0592857307b6f("input<br>attempt<br>map<br>null<br>key<br>to<br>none")
        55aee70c0dcdcf30a91cd0942f824f13("input<br>attempt<br>filter<br>not<br>null<br>request")
        60bc635543eb62eaa548419925974b0f("input<br>attempt<br>process<br>attempt")

        class f9f635786c7242ef17e9f5b2cada1a2d sourceClass
        class dc6eabac5819557cc449943d969d25ec processorClass
        class 12df695746f95f56e6b0592857307b6f processorClass
        class 55aee70c0dcdcf30a91cd0942f824f13 processorClass
        class 60bc635543eb62eaa548419925974b0f processorClass
    end

    63ead6e070358b9501de13c8abe01417 --> f9f635786c7242ef17e9f5b2cada1a2d
    f9f635786c7242ef17e9f5b2cada1a2d --> dc6eabac5819557cc449943d969d25ec
    dc6eabac5819557cc449943d969d25ec --> 12df695746f95f56e6b0592857307b6f
    12df695746f95f56e6b0592857307b6f --> 55aee70c0dcdcf30a91cd0942f824f13
    55aee70c0dcdcf30a91cd0942f824f13 --> 60bc635543eb62eaa548419925974b0f

    class 63ead6e070358b9501de13c8abe01417 topicClass
```
