package no.ruter.tranop.gradle

import org.gradle.api.artifacts.dsl.RepositoryHandler
import org.gradle.api.artifacts.repositories.MavenArtifactRepository
import org.gradle.api.credentials.HttpHeaderCredentials
import org.gradle.api.provider.ProviderFactory
import org.gradle.authentication.http.HttpHeaderAuthentication
import org.gradle.kotlin.dsl.create

class RepositoryUtils {
    enum class TokenType(
        val privateTokenName: String,
        val jobTokenName: String,
    ) {
        CLONE(
            jobTokenName = "rdp-pipeline-token",
            privateTokenName = "gitlab-private-token",
        ),
        MAVEN(
            jobTokenName = "Job-Token",
            privateTokenName = "Private-Token",
        ),
    }

    companion object {
        private fun getToken(
            providers: ProviderFactory,
            type: TokenType,
        ): String =
            when (type) {
                TokenType.MAVEN -> System.getenv("CI_JOB_TOKEN")
                TokenType.CLONE -> System.getenv("TRAN_RDP_CI_TOKEN")
            } ?: getPrivateToken(providers) ?: throw IllegalStateException("No token configured for $type")

        fun getPrivateToken(providers: ProviderFactory) = providers.gradleProperty("gitLabPrivateToken").orNull

        fun gitlabMavenUrl(groupId: String) = "https://gitlab.com/api/v4/groups/$groupId/-/packages/maven"

        fun gitlabGroupName(groupName: String) = "gitLabGroup${groupName}Maven"

        fun gitLabUserName(type: TokenType) = System.getenv("CI_JOB_TOKEN") ?.let { type.jobTokenName } ?: type.privateTokenName

        fun getCredentials(
            providers: ProviderFactory,
            type: TokenType = TokenType.MAVEN,
        ): Pair<String, String> {
            val token = getToken(providers = providers, type = type)
            val username = gitLabUserName(type)
            return Pair(username, token)
        }

        @JvmStatic
        fun RepositoryHandler.gitlabGroup(
            providers: ProviderFactory,
            groupId: String,
            groupName: String,
        ): MavenArtifactRepository =
            maven {
                name = gitlabGroupName(groupName)
                setUrl(gitlabMavenUrl(groupId))
                val (username, token) = getCredentials(providers)
                credentials(HttpHeaderCredentials::class.java) {
                    name = username
                    value = token
                }
                authentication {
                    create<HttpHeaderAuthentication>("header")
                }
            }
    }
}
