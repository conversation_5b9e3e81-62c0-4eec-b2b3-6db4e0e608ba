package no.ruter.tranop.gradle

import org.gradle.api.Project
import org.gradle.api.artifacts.dsl.RepositoryHandler
import org.gradle.api.credentials.HttpHeaderCredentials
import org.gradle.authentication.http.HttpHeaderAuthentication
import org.gradle.kotlin.dsl.create

class GradleExtensions private constructor() {
    companion object {
        @JvmStatic
        fun RepositoryHandler.gitlabGroup(
            project: Project,
            groupId: String,
            groupName: String,
        ) {
            maven {
                name = "gitLabGroup${groupName}Maven"
                setUrl("https://gitlab.com/api/v4/groups/$groupId/-/packages/maven")
                credentials(HttpHeaderCredentials::class.java) {
                    val jobToken = System.getenv("CI_JOB_TOKEN")
                    name = jobToken?.let { "Job-Token" } ?: "Private-Token"
                    value = jobToken ?: project.properties["gitLabPrivateToken"] as String? ?: error(
                        "Missing required Gradle property (~/.gradle/gradle.properties): gitLabPrivateToken",
                    )
                }
                content {
                    includeGroupByRegex("no\\.ruter\\..*")
                }
                authentication {
                    create<HttpHeaderAuthentication>("header")
                }
            }
        }
    }
}
