package no.ruter.tranop.gradle.codegen

import nu.studer.gradle.jooq.JooqExtension
import org.gradle.api.Project

class JooqUtils private constructor() {
    companion object {
        @JvmStatic
        fun JooqExtension.configureCodegen(
            proj: Project,
            jooqVersion: String,
        ) {
            this.version.set(jooqVersion)
            this.configurations.apply() {
                create("main") {
                    jooqConfiguration.apply {
                        logging = org.jooq.meta.jaxb.Logging.WARN

                        // https://www.jooq.org/doc/latest/manual/code-generation/
                        // https://www.jooq.org/doc/latest/manual/code-generation/codegen-advanced/
                        // https://www.jooq.org/doc/latest/manual/code-generation/codegen-configuration/
                        generator.apply {
                            database.apply {
                                // https://www.jooq.org/doc/latest/manual/code-generation/codegen-ddl/
                                name = "org.jooq.meta.extensions.ddl.DDLDatabase"
                                recordVersionFields = "revision"
                                properties.addAll(
                                    listOf(
                                        property("sort", "flyway"),
                                        property("scripts", "src/main/resources/db/migration/*.sql"),
                                        property("defaultNameCase", "lower"),
                                        property("unqualifiedSchema", "none"),
                                    ),
                                )
                            }
                            strategy.name = "no.ruter.tranop.assignment.jooq.JooqGeneratorStrategy"
                            generate.apply {
                                isGeneratedAnnotation = true
                                isGeneratedAnnotationDate = true
                                generatedAnnotationType = org.jooq.meta.jaxb.GeneratedAnnotationType.DETECT_FROM_JDK
                            }
                            target.apply {
                                packageName = "no.ruter.tranop.assignmentmanager.db.sql"
                                directory = "${proj.layout.buildDirectory.get()}/codegen/jooq"
                            }
                        }
                    }
                }
            }
        }

        private fun property(
            key: String,
            value: String,
        ) = org.jooq.meta.jaxb.Property().apply {
            this.key = key
            this.value = value
        }
    }
}
