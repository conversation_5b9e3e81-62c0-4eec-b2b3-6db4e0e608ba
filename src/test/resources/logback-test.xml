<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator"/>

    <springProfile name="test">
        <appender class="ch.qos.logback.core.ConsoleAppender" name="CONSOLE">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <Pattern>
                    %date [%t] %highlight(%-5level) %cyan(%logger{36}) %mdc - %msg%n
                </Pattern>
            </layout>
        </appender>
    </springProfile>

    <springProfile name="!test">
        <include resource="custom-console-appender-json.xml"/>
    </springProfile>

    <logger additivity="false" level="DEBUG" name="no.ruter">
        <appender-ref ref="CONSOLE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>
