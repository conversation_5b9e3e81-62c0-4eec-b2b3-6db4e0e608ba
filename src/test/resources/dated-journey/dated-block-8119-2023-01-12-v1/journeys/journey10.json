{"ref": "jr3c945cab7e964c119632b26474a88f72", "header": {"messageTimestamp": "2023-01-11T11:48:50.151396Z", "receivedTimestamp": "2023-01-11T13:37+01:00", "publishedTimestamp": "2023-01-11T13:37:03+01:00", "expiresTimestamp": "2023-02-10T13:37:03+01:00", "traceId": "b4c046f3-bda7-4981-986e-30fb7d4d86b0", "ownerId": "RUTER", "originId": "bifrost", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "block/planned/bifrost", "timestamp": "2023-01-11T13:37+01:00", "description": "The journey was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "cancelled": false, "type": "DeadRun", "status": "ORIGINAL", "name": "Tårnåsen-Rosenholm garasje", "vehicleTask": "8119", "line": {"lineRef": "RUT:Line:0", "legacyTransportMode": "UNKNOWN", "legacyTransportSubMode": "UNKNOWN", "name": "", "publicCode": "0", "privateCode": "0", "backgroundColour": "", "textColour": ""}, "journeyReferences": {"vehicleJourneyId": "916887", "vehicleTaskRef": "8119", "blockRef": "8119-2023-01-12", "datedBlockRef": "RUT:DatedBlock:173533-8119-202301120539000", "journeyRef": "RUT:DeadRun:002040-002392-161500", "legacyJourneyRef": "8119-2023-01-12T16:15:00+01:00", "legacyDatedJourneyRef": "8d585a5a1b56bd30c5fb33162a1edabf", "journeyPatternRef": "RUT:DeadRunJourneyPattern:0-830203-6264-301", "legacyJourneyPatternRefs": ["RUT:DeadRunJourneyPattern:0-830203-6264-301"], "externalJourneyRef": "RUT:DeadRun:0-173533-25537425", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:jr3c945cab7e964c119632b26474a88f72"}, "operators": [{"operatorRef": "RUT:Operator:130", "name": "Connect Bus AS"}], "operatorContracts": [{"operatorContractRef": "RUT:OperatorContract:2311", "name": "Oslo_Sør", "operator": {"operatorRef": "RUT:Operator:130", "name": "Connect Bus AS"}}], "lineage": [{"value": "RUT:DeadRunJourneyPattern:0-830203-6264-301", "type": "JOURNEY_PATTERN_REF", "source": "HASTUS"}, {"value": "8119-2023-01-12T16:15:00+01:00", "type": "JOURNEY_REF", "source": "HASTUS"}, {"value": "RUT:JourneyPattern:002040", "type": "JOURNEY_PATTERN_REF", "source": "BIFROST"}, {"value": "RUT:DeadRun:002040-002392-161500", "type": "JOURNEY_REF", "source": "BIFROST"}], "vehicles": [], "operatingDate": "2023-01-12", "journeyDate": "2023-01-12", "plan": {"calls": [{"ref": "internal-planned-call-ref-406", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "sr425b30f6db0e4eaabd5c6a7dd14e2f75", "order": 1, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-830203-1", "destination": {"text": "Rosenholm garasje"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-12T15:15:00Z", "plannedDeparture": "2023-01-12T15:15:00Z", "nextCallStopPointLinkRef": "lrd2b83364368947efb08c388d5c77bae3"}, {"ref": "internal-planned-call-ref-407", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "srcb9ebd863a5a4129b68c335ee3dfa10b", "order": 2, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-830203-2", "destination": {"text": "Rosenholm garasje"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-12T15:25:00Z", "plannedDeparture": "2023-01-12T15:25:00Z"}], "stops": [{"ref": "sr425b30f6db0e4eaabd5c6a7dd14e2f75", "status": "ORIGINAL", "legacyQuayRef": "217016002", "quayRef": "NSR:Quay:6264", "stopPlaceRef": "NSR:StopPlace:3524", "geoPoint": {"detectionPointRef": "12760", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.816558, 59.817403]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tariffZones": ["RUT:TariffZone:2S"]}, {"ref": "srcb9ebd863a5a4129b68c335ee3dfa10b", "status": "ORIGINAL", "legacyQuayRef": "821720202", "quayRef": "NBU:Quay:301", "stopPlaceRef": "NBU:StopPlace:301", "geoPoint": {"detectionPointRef": "33872", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.796381053313798, 59.820964351467644]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Rosenholm garasje", "tariffZones": []}], "links": [{"ref": "lrd2b83364368947efb08c388d5c77bae3", "serviceLinkRef": "RUT:ServiceLink:6264-301", "fromStopPointRef": "sr425b30f6db0e4eaabd5c6a7dd14e2f75", "fromQuayRef": "NSR:Quay:6264", "toStopPointRef": "srcb9ebd863a5a4129b68c335ee3dfa10b", "toQuayRef": "NBU:Quay:301", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.815668, 59.817525], [10.796085, 59.821791]]}, "properties": {}}, "trackLineType": "DETAILED", "calculatedLength": "1197.0655753401168", "measuredLength": "3385.0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "0", "order": 10}