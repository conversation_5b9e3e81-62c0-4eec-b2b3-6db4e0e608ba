{"ref": "jred9c2c3230694fb4abc846129f901c6d", "header": {"messageTimestamp": "2023-01-11T11:48:50.151396Z", "receivedTimestamp": "2023-01-11T13:37+01:00", "publishedTimestamp": "2023-01-11T13:37:03+01:00", "expiresTimestamp": "2023-02-10T13:37:03+01:00", "traceId": "b4c046f3-bda7-4981-986e-30fb7d4d86b0", "ownerId": "RUTER", "originId": "bifrost", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "block/planned/bifrost", "timestamp": "2023-01-11T13:37+01:00", "description": "The journey was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "cancelled": false, "type": "DeadRun", "status": "ORIGINAL", "name": "Rosenholm garasje-Åsbråten", "vehicleTask": "8119", "line": {"lineRef": "RUT:Line:0", "transportMode": "BUS", "legacyTransportMode": "UNKNOWN", "legacyTransportSubMode": "UNKNOWN", "name": "", "publicCode": "0", "privateCode": "0", "backgroundColour": "", "textColour": ""}, "journeyReferences": {"vehicleJourneyId": "161137", "vehicleTaskRef": "8119", "blockRef": "8119-2023-01-12", "datedBlockRef": "RUT:DatedBlock:173533-8119-202301120539000", "journeyRef": "RUT:DeadRun:000196-000244-053900", "legacyJourneyRef": "8119-2023-01-12T05:39:00+01:00", "legacyDatedJourneyRef": "911e601994d4a20050d532c13b53350c", "journeyPatternRef": "RUT:DeadRunJourneyPattern:0-19N046-301-12462", "legacyJourneyPatternRefs": ["RUT:DeadRunJourneyPattern:0-19N046-301-12462", "RUT:DeadRunJourneyPattern:0-790236-301-12462", "RUT:DeadRunJourneyPattern:0-80E0110-301-12462"], "externalJourneyRef": "RUT:DeadRun:0-173533-25537424", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:jred9c2c3230694fb4abc846129f901c6d"}, "operators": [{"operatorRef": "RUT:Operator:130", "name": "Connect Bus AS"}], "operatorContracts": [{"operatorContractRef": "RUT:OperatorContract:2311", "name": "Oslo_Sør", "operator": {"operatorRef": "RUT:Operator:130", "name": "Connect Bus AS"}}], "lineage": [{"value": "RUT:DeadRunJourneyPattern:0-19N046-301-12462", "type": "JOURNEY_PATTERN_REF", "source": "HASTUS"}, {"value": "8119-2023-01-12T05:39:00+01:00", "type": "JOURNEY_REF", "source": "HASTUS"}, {"value": "RUT:JourneyPattern:000196", "type": "JOURNEY_PATTERN_REF", "source": "BIFROST"}, {"value": "RUT:DeadRun:000196-000244-053900", "type": "JOURNEY_REF", "source": "BIFROST"}], "vehicles": [], "operatingDate": "2023-01-12", "journeyDate": "2023-01-12", "plan": {"calls": [{"ref": "internal-planned-call-ref-001", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "srcb9ebd863a5a4129b68c335ee3dfa10b", "order": 1, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-19N046-1", "destination": {"text": "Åsbråten"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-12T04:39:00Z", "plannedDeparture": "2023-01-12T04:39:00Z", "nextCallStopPointLinkRef": "lrfa42fb67ff644ca380fb434cc10f0825"}, {"ref": "internal-planned-call-ref-002", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "srf5940741f6b84210ab5b6d59109658ba", "order": 2, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-19N046-2", "destination": {"text": "Åsbråten"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-12T04:46:00Z", "plannedDeparture": "2023-01-12T04:46:00Z"}], "stops": [{"ref": "srcb9ebd863a5a4129b68c335ee3dfa10b", "status": "ORIGINAL", "legacyQuayRef": "821720202", "quayRef": "NBU:Quay:301", "stopPlaceRef": "NBU:StopPlace:301", "geoPoint": {"detectionPointRef": "33872", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.796381053313798, 59.820964351467644]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Rosenholm garasje", "tariffZones": []}, {"ref": "srf5940741f6b84210ab5b6d59109658ba", "status": "ORIGINAL", "legacyQuayRef": "301093702", "quayRef": "NSR:Quay:12462", "stopPlaceRef": "NSR:StopPlace:6739", "geoPoint": {"detectionPointRef": "1253", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.782255, 59.826132]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Åsbråten", "tariffZones": ["OST:TariffZone:227", "RUT:TariffZone:1"]}], "links": [{"ref": "lrfa42fb67ff644ca380fb434cc10f0825", "serviceLinkRef": "RUT:ServiceLink:301-12462", "fromStopPointRef": "srcb9ebd863a5a4129b68c335ee3dfa10b", "fromQuayRef": "NBU:Quay:301", "toStopPointRef": "srf5940741f6b84210ab5b6d59109658ba", "toQuayRef": "NSR:Quay:12462", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.796085, 59.821791], [10.782337, 59.826119]]}, "properties": {}}, "trackLineType": "DETAILED", "calculatedLength": "909.53975091605", "measuredLength": "2611.0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "0", "order": 1}