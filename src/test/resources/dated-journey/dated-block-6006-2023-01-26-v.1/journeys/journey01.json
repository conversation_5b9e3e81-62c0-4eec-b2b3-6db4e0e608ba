{"ref": "jr351fb5c392c2428c896291fc98efd630", "header": {"messageTimestamp": "2023-01-25T13:32:03.342587Z", "receivedTimestamp": "2023-01-25T13:37+01:00", "publishedTimestamp": "2023-01-25T13:37:03+01:00", "expiresTimestamp": "2023-02-24T13:37:03+01:00", "traceId": "bdad9d8b-807d-4a8e-8c11-efa2dba3339f", "ownerId": "RUTER", "originId": "bifrost", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "block/planned/bifrost", "timestamp": "2023-01-25T13:37+01:00", "description": "The journey was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "cancelled": false, "type": "DeadRun", "status": "ORIGINAL", "name": "Persveien garasje-Tonsenhagen", "vehicleTask": "6006", "line": {"lineRef": "RUT:Line:0", "legacyTransportMode": "UNKNOWN", "legacyTransportSubMode": "UNKNOWN", "name": "", "publicCode": "0", "privateCode": "0", "backgroundColour": "", "textColour": ""}, "journeyReferences": {"vehicleJourneyId": "748011", "vehicleTaskRef": "6006", "blockRef": "6006-2023-01-26", "datedBlockRef": "RUT:DatedBlock:178180-6006-202301260529000", "journeyRef": "RUT:DeadRun:001898-002291-052900", "legacyJourneyRef": "6006-2023-01-26T05:29:00+01:00", "legacyDatedJourneyRef": "3cbc085c32a310151d5c3f6ad93c7717", "journeyPatternRef": "RUT:DeadRunJourneyPattern:0-60038-HaraGa-11024", "runtimePatternRef": "RUT:RunTimePattern:002291", "legacyJourneyPatternRefs": ["RUT:DeadRunJourneyPattern:0-60038-HaraGa-11024"], "externalJourneyRef": "RUT:DeadRun:0-178180-26031107", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:jr351fb5c392c2428c896291fc98efd630"}, "operators": [{"operatorRef": "RUT:Operator:130", "name": "Connect Bus AS"}], "operatorContracts": [{"operatorContractRef": "RUT:OperatorContract:0911", "name": "Oslo_Nord-Øst", "operator": {"operatorRef": "RUT:Operator:130", "name": "Connect Bus AS"}}], "lineage": [{"value": "RUT:DeadRunJourneyPattern:0-60038-HaraGa-11024", "type": "JOURNEY_PATTERN_REF", "source": "HASTUS"}, {"value": "RUT:RunTimePattern:002291", "type": "RUNTIME_PATTERN_REF", "source": "HASTUS"}, {"value": "6006-2023-01-26T05:29:00+01:00", "type": "JOURNEY_REF", "source": "HASTUS"}, {"value": "RUT:JourneyPattern:001898", "type": "JOURNEY_PATTERN_REF", "source": "BIFROST"}, {"value": "RUT:DeadRun:001898-002291-052900", "type": "JOURNEY_REF", "source": "BIFROST"}], "vehicles": [], "operatingDate": "2023-01-26", "journeyDate": "2023-01-26", "plan": {"calls": [{"ref": "internal-planned-call-ref-001", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "sr0bb1e849a30d4bb5a6020103e4232a4a", "order": 1, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-60038-1", "destination": {"text": "Tonsenhagen"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-26T04:29:00Z", "plannedDeparture": "2023-01-26T04:29:00Z", "nextCallStopPointLinkRef": "lr93afdb7563b94a02a699254a12fd7eca"}, {"ref": "internal-planned-call-ref-002", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "srd3c2d8b2b4534b9aac364fc0addc3bc3", "order": 2, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-60038-2", "destination": {"text": "Tonsenhagen"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-26T04:40:00Z", "plannedDeparture": "2023-01-26T04:40:00Z"}], "stops": [{"ref": "sr0bb1e849a30d4bb5a6020103e4232a4a", "status": "ORIGINAL", "legacyQuayRef": "830120701", "quayRef": "RUT:Quay:HaraGa", "stopPlaceRef": "RUT:StopPlace:HaraGa", "geoPoint": {"detectionPointRef": "30601", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.82316465542472, 59.924193492552]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Persveien garasje", "tariffZones": []}, {"ref": "srd3c2d8b2b4534b9aac364fc0addc3bc3", "status": "ORIGINAL", "legacyQuayRef": "301207001", "quayRef": "NSR:Quay:11024", "stopPlaceRef": "NSR:StopPlace:6008", "geoPoint": {"detectionPointRef": "1813", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.828322, 59.946015]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Tonsenhagen", "tariffZones": ["INN:TariffZone:2101", "RUT:TariffZone:1"]}], "links": [{"ref": "lr93afdb7563b94a02a699254a12fd7eca", "serviceLinkRef": "RUT:ServiceLink:HaraGa-11024", "fromStopPointRef": "sr0bb1e849a30d4bb5a6020103e4232a4a", "fromQuayRef": "RUT:Quay:HaraGa", "toStopPointRef": "srd3c2d8b2b4534b9aac364fc0addc3bc3", "toQuayRef": "NSR:Quay:11024", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.823234, 59.924214], [10.828294, 59.946061]]}, "properties": {}}, "trackLineType": "DETAILED", "calculatedLength": "2450.3855617012687", "measuredLength": "4500.0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "0", "order": 1}