{"ref": "jr4747e31d347049758fde352d00de4944", "header": {"messageTimestamp": "2023-01-19T10:52:59.137561Z", "receivedTimestamp": "2023-01-20T13:37+01:00", "publishedTimestamp": "2023-01-20T13:37:03+01:00", "expiresTimestamp": "2023-02-19T13:37:03+01:00", "traceId": "bc2cc1d0-c9e0-4424-b1ab-91e005f89d3a", "ownerId": "RUTER", "originId": "bifrost", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "block/planned/bifrost", "timestamp": "2023-01-20T13:37+01:00", "description": "The journey was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "cancelled": false, "type": "DeadRun", "status": "ORIGINAL", "name": "Lørenskog stasjon-Jernkroken garasje", "vehicleTask": "2506", "line": {"lineRef": "RUT:Line:0", "legacyTransportMode": "UNKNOWN", "legacyTransportSubMode": "UNKNOWN", "name": "", "publicCode": "0", "privateCode": "0", "backgroundColour": "", "textColour": ""}, "journeyReferences": {"vehicleJourneyId": "144620", "vehicleTaskRef": "2506", "blockRef": "2506-2023-01-21", "datedBlockRef": "RUT:DatedBlock:171020-2506-202301211037000", "journeyRef": "RUT:DeadRun:000474-000600-233300", "legacyJourneyRef": "2506-2023-01-21T23:33:00+01:00", "legacyDatedJourneyRef": "858b656c52a643a8ed217a26ea8deb73", "journeyPatternRef": "RUT:DeadRunJourneyPattern:0-250115-10054-GrGa", "runtimePatternRef": "RUT:RunTimePattern:000600", "legacyJourneyPatternRefs": ["RUT:DeadRunJourneyPattern:0-250115-10054-GrGa"], "externalJourneyRef": "RUT:DeadRun:0-171020-25744958", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:jr4747e31d347049758fde352d00de4944"}, "operators": [{"operatorRef": "RUT:Operator:120", "name": "Nobina AS"}], "operatorContracts": [{"operatorContractRef": "RUT:OperatorContract:1612", "name": "Oslo_Linje 25", "operator": {"operatorRef": "RUT:Operator:120", "name": "Nobina AS"}}], "lineage": [{"value": "RUT:DeadRunJourneyPattern:0-250115-10054-GrGa", "type": "JOURNEY_PATTERN_REF", "source": "HASTUS"}, {"value": "RUT:RunTimePattern:000600", "type": "RUNTIME_PATTERN_REF", "source": "HASTUS"}, {"value": "2506-2023-01-21T23:33:00+01:00", "type": "JOURNEY_REF", "source": "HASTUS"}, {"value": "RUT:JourneyPattern:000474", "type": "JOURNEY_PATTERN_REF", "source": "BIFROST"}, {"value": "RUT:DeadRun:000474-000600-233300", "type": "JOURNEY_REF", "source": "BIFROST"}], "vehicles": [], "operatingDate": "2023-01-21", "journeyDate": "2023-01-21", "plan": {"calls": [{"ref": "internal-planned-call-ref-515", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "src34f60295e744ff9a2783ee2f4281264", "order": 1, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-250115-1", "destination": {"text": "Jernkroken garasje"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-21T22:33:00Z", "plannedDeparture": "2023-01-21T22:33:00Z", "nextCallStopPointLinkRef": "lrf9f91612f0954a2a92dffe2da8c54e64"}, {"ref": "internal-planned-call-ref-516", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "sr59d01ac01dbe4795acdca09ce63c3f6d", "order": 2, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-250115-2", "destination": {"text": "Jernkroken garasje"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-21T22:41:00Z", "plannedDeparture": "2023-01-21T22:41:00Z"}], "stops": [{"ref": "src34f60295e744ff9a2783ee2f4281264", "status": "ORIGINAL", "legacyQuayRef": "230177101", "quayRef": "NSR:Quay:10054", "stopPlaceRef": "NSR:StopPlace:5497", "stopAreaRef": "NSR:StopPlace:58857", "geoPoint": {"detectionPointRef": "13289", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.944918, 59.944553]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Lørenskog stasjon", "tariffZones": ["RUT:TariffZone:1"]}, {"ref": "sr59d01ac01dbe4795acdca09ce63c3f6d", "status": "ORIGINAL", "legacyQuayRef": "830120301", "quayRef": "RUT:Quay:GrGa", "stopPlaceRef": "RUT:StopPlace:GrGa", "geoPoint": {"detectionPointRef": "30600", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.889285301145284, 59.95270487414999]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Jernkroken garasje", "tariffZones": []}], "links": [{"ref": "lrf9f91612f0954a2a92dffe2da8c54e64", "serviceLinkRef": "RUT:ServiceLink:10054-GrGa", "fromStopPointRef": "src34f60295e744ff9a2783ee2f4281264", "fromQuayRef": "NSR:Quay:10054", "toStopPointRef": "sr59d01ac01dbe4795acdca09ce63c3f6d", "toQuayRef": "RUT:Quay:GrGa", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.944844, 59.944516], [10.889304, 59.952743]]}, "properties": {}}, "trackLineType": "DETAILED", "calculatedLength": "3236.4388836166013", "measuredLength": "4594.0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "0", "order": 27}