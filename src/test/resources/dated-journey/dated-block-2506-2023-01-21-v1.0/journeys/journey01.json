{"ref": "jr91e9c3a5546145c7bc64cc1eb55989b9", "header": {"messageTimestamp": "2023-01-19T10:52:59.137561Z", "receivedTimestamp": "2023-01-20T13:37+01:00", "publishedTimestamp": "2023-01-20T13:37:03+01:00", "expiresTimestamp": "2023-02-19T13:37:03+01:00", "traceId": "bc2cc1d0-c9e0-4424-b1ab-91e005f89d3a", "ownerId": "RUTER", "originId": "bifrost", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "block/planned/bifrost", "timestamp": "2023-01-20T13:37+01:00", "description": "The journey was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "cancelled": false, "type": "DeadRun", "status": "ORIGINAL", "name": "Jernkroken garasje-Majorstuen", "vehicleTask": "2506", "line": {"lineRef": "RUT:Line:0", "legacyTransportMode": "UNKNOWN", "legacyTransportSubMode": "UNKNOWN", "name": "", "publicCode": "0", "privateCode": "0", "backgroundColour": "", "textColour": ""}, "journeyReferences": {"vehicleJourneyId": "969066", "vehicleTaskRef": "2506", "blockRef": "2506-2023-01-21", "datedBlockRef": "RUT:DatedBlock:171020-2506-202301211037000", "journeyRef": "RUT:DeadRun:000476-000608-103700", "legacyJourneyRef": "2506-2023-01-21T10:37:00+01:00", "legacyDatedJourneyRef": "4985e002db4d7c708817a607604945cf", "journeyPatternRef": "RUT:DeadRunJourneyPattern:0-250117-GrGa-8079", "runtimePatternRef": "RUT:RunTimePattern:000608", "legacyJourneyPatternRefs": ["RUT:DeadRunJourneyPattern:0-250117-GrGa-8079"], "externalJourneyRef": "RUT:DeadRun:0-171020-25744989", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:jr91e9c3a5546145c7bc64cc1eb55989b9"}, "operators": [{"operatorRef": "RUT:Operator:120", "name": "Nobina AS"}], "operatorContracts": [{"operatorContractRef": "RUT:OperatorContract:1612", "name": "Oslo_Linje 25", "operator": {"operatorRef": "RUT:Operator:120", "name": "Nobina AS"}}], "lineage": [{"value": "RUT:DeadRunJourneyPattern:0-250117-GrGa-8079", "type": "JOURNEY_PATTERN_REF", "source": "HASTUS"}, {"value": "RUT:RunTimePattern:000608", "type": "RUNTIME_PATTERN_REF", "source": "HASTUS"}, {"value": "2506-2023-01-21T10:37:00+01:00", "type": "JOURNEY_REF", "source": "HASTUS"}, {"value": "RUT:JourneyPattern:000476", "type": "JOURNEY_PATTERN_REF", "source": "BIFROST"}, {"value": "RUT:DeadRun:000476-000608-103700", "type": "JOURNEY_REF", "source": "BIFROST"}], "vehicles": [], "operatingDate": "2023-01-21", "journeyDate": "2023-01-21", "plan": {"calls": [{"ref": "internal-planned-call-ref-001", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "sr59d01ac01dbe4795acdca09ce63c3f6d", "order": 1, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-250117-1", "destination": {"text": "Majorstuen"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-21T09:37:00Z", "plannedDeparture": "2023-01-21T09:37:00Z", "nextCallStopPointLinkRef": "lr78feac17861541769f0fdfb56792aaf4"}, {"ref": "internal-planned-call-ref-002", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "sr81d31ef8863544a7bc35898e9f10f034", "order": 2, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-250117-2", "destination": {"text": "Majorstuen"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-01-21T09:59:00Z", "plannedDeparture": "2023-01-21T09:59:00Z"}], "stops": [{"ref": "sr59d01ac01dbe4795acdca09ce63c3f6d", "status": "ORIGINAL", "legacyQuayRef": "830120301", "quayRef": "RUT:Quay:GrGa", "stopPlaceRef": "RUT:StopPlace:GrGa", "geoPoint": {"detectionPointRef": "30600", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.889285301145284, 59.95270487414999]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Jernkroken garasje", "tariffZones": []}, {"ref": "sr81d31ef8863544a7bc35898e9f10f034", "status": "ORIGINAL", "legacyQuayRef": "301020301", "quayRef": "NSR:Quay:8079", "stopPlaceRef": "NSR:StopPlace:4483", "stopAreaRef": "NSR:StopPlace:58381", "geoPoint": {"detectionPointRef": "1338", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.716088, 59.929528]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Majorstuen", "publicCode": "F", "description": "i Valkyriegata", "tariffZones": ["RUT:TariffZone:1"]}], "links": [{"ref": "lr78feac17861541769f0fdfb56792aaf4", "serviceLinkRef": "RUT:ServiceLink:GrGa-8079", "fromStopPointRef": "sr59d01ac01dbe4795acdca09ce63c3f6d", "fromQuayRef": "RUT:Quay:GrGa", "toStopPointRef": "sr81d31ef8863544a7bc35898e9f10f034", "toQuayRef": "NSR:Quay:8079", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.889304, 59.952743], [10.716277, 59.929511]]}, "properties": {}}, "trackLineType": "DETAILED", "calculatedLength": "10012.388976631713", "measuredLength": "16054.0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "0", "order": 1}