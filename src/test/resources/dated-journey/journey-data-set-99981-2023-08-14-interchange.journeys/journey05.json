{"ref": "djj-d0a4b5008369f677f034c9bef469669b", "header": {"messageTimestamp": "2023-08-12T21:00+02:00", "receivedTimestamp": "2023-08-12T21:00+02:00", "publishedTimestamp": "2023-08-12T19:00:03Z", "expiresTimestamp": "2023-08-16T00:00Z", "traceId": "dated-journey-manager/journey/f8d9841d2ef84436bab2969811c5e31a", "ownerId": "RUTER", "originId": "dated-journey-manager", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "journey/planned/bifrost", "timestamp": "2023-08-12T19:00Z", "description": "The journey was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "lifeCycleInfo": {"revision": 1, "created": "2023-08-12T21:00+02:00", "modified": "2023-08-12T21:00+02:00", "dataSource": "journey/planned/bifrost"}, "cancelled": false, "deleted": false, "type": "DeadRun", "name": "Grorud T-Test Stop", "vehicleTask": "99981", "journeyReferences": {"vehicleTaskRef": "99981", "blockRef": "99981-2023-08-14", "datedBlockRef": "RUT:DatedBlock:159882-99981-202308140001000", "journeyRef": "RUT:DeadRun:002351-002837-235800", "vehicleJourneyId": "98", "legacyJourneyRef": "99981-2023-08-14T23:58:00+02:00", "legacyDatedJourneyRef": "4dc48a8229f4fd39943ff3395bdb813a", "journeyPatternRef": "RUT:JourneyPattern:002351", "runtimePatternRef": "RUT:RunTimePattern:002837", "legacyJourneyPatternRefs": ["RUT:DeadRunJourneyPattern:0-999802-10726-99999999"], "externalJourneyRef": "RUT:DeadRun:0-159882-24236418", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:djj-d0a4b5008369f677f034c9bef469669b"}, "operators": [{"operatorRef": "RUT:Operator:998", "name": "Operatør for Testbruk"}], "operatorContracts": [{"operatorContractRef": "RUT:OperatorContract:9998", "name": "<PERSON><PERSON><PERSON><PERSON> for test", "operator": {"operatorRef": "RUT:Operator:998", "name": "Operatør for Testbruk"}}], "lineage": [], "vehicles": [], "operatingDate": "2023-08-14", "plan": {"firstDepartureDateTime": "2023-08-14T23:58+02:00", "lastArrivalDateTime": "2023-08-15T00:00+02:00", "calls": [{"ref": "djc-758a4e4ea9c5fdf6c99e1d4e50133d1f", "events": [], "cancelled": false, "stopPointRef": "djs-58159907ca7ca98d2287b8cd6ad80f27", "order": 1, "journeyPatternPointRef": "RUT:JourneyPatternPoint:002351-1", "destination": {"text": "<PERSON><PERSON> for TAAS test"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedDeparture": "2023-08-14T23:58+02:00", "nextCallStopPointLinkRef": "djl-11675487da4eb22a93175106f66ebaf9"}, {"ref": "djc-55d2bf023902b78498effb3409a70258", "events": [], "cancelled": false, "stopPointRef": "djs-00d1d6d94293a825dbe115de3d659eb8", "order": 2, "journeyPatternPointRef": "RUT:JourneyPatternPoint:002351-2", "destination": {"text": "<PERSON><PERSON> for TAAS test"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2023-08-15T00:00+02:00"}], "stops": [{"ref": "djs-58159907ca7ca98d2287b8cd6ad80f27", "lifeCycleInfo": {"revision": 1, "created": "2023-08-12T21:00+02:00", "modified": "2023-08-12T21:00+02:00", "dataSource": "journey/planned/bifrost"}, "legacyQuayRef": "301194103", "quayRef": "NSR:Quay:10726", "stopPlaceRef": "NSR:StopPlace:5850", "stopAreaRef": "NSR:StopPlace:58216", "geoPoint": {"ref": "642", "detectionPointRef": "642", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.882871, 59.960944]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "entryDirectionWindow": 360}, "name": "<PERSON><PERSON><PERSON>", "publicCode": "3", "stopPlaceDescription": "", "tariffZones": ["INN:TariffZone:2102", "RUT:TariffZone:1"]}, {"ref": "djs-00d1d6d94293a825dbe115de3d659eb8", "lifeCycleInfo": {"revision": 1, "created": "2023-08-12T21:00+02:00", "modified": "2023-08-12T21:00+02:00", "dataSource": "journey/planned/bifrost"}, "legacyQuayRef": "99999999", "quayRef": "RUT:Quay:99999999", "stopPlaceRef": "RUT:StopPlace:999999", "geoPoint": {"ref": "31183", "detectionPointRef": "31183", "location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.883613, 59.960892]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "entryDirectionWindow": 360}, "name": "<PERSON><PERSON> for TAAS test", "tariffZones": []}], "links": [{"ref": "djl-11675487da4eb22a93175106f66ebaf9", "lifeCycleInfo": {"revision": 1, "created": "2023-08-12T21:00+02:00", "modified": "2023-08-12T21:00+02:00", "dataSource": "journey/planned/bifrost"}, "serviceLinkRef": "RUT:ServiceLink:10726-99999999", "fromStopPointRef": "djs-58159907ca7ca98d2287b8cd6ad80f27", "fromQuayRef": "NSR:Quay:10726", "toStopPointRef": "djs-00d1d6d94293a825dbe115de3d659eb8", "toQuayRef": "RUT:Quay:99999999", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.882871, 59.960944], [10.883613, 59.960892]]}, "properties": {}}, "trackLineType": "SIMPLE", "calculatedLength": "41.8", "measuredLength": "0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "0", "order": 97}