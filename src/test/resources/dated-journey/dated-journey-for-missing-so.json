{"ref": "djj-0ea3a8c1f1c272570877c66b16d009be", "line": {"name": "Holmlia stasjon - Brenna", "lineRef": "RUT:Line:73", "publicCode": "73", "textColour": "FFFFFF", "privateCode": "73", "transportMode": "BUS", "backgroundColour": "E60000", "legacyTransportMode": "BUS", "legacyTransportSubMode": "LOCAL_BUS", "transportModeProperties": ["LOCAL"]}, "name": "Mortensrud T-Pasop", "plan": {"calls": [{"ref": "djc-b35efec685102a4e0fe01ca8c11b867e", "order": 1, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-ac9fd9d9dd9e9d3f0df045d7b0f71f7b", "plannedArrival": "2024-11-29T00:27:00Z", "plannedDeparture": "2024-11-29T00:27:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-1", "stopPointBehaviourType": "FOR_BOARDING_ONLY", "nextCallStopPointLinkRef": "djl-e07c91c360d355f58d09cf2e147a3c1f", "originalStopPointBehaviourType": "FOR_BOARDING_ONLY"}, {"ref": "djc-25a327dda126bed31132d85537eb7715", "order": 2, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-787ac94e6a1f1509c5ec3edda19ab51e", "plannedArrival": "2024-11-29T00:29:00Z", "plannedDeparture": "2024-11-29T00:29:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-2", "stopPointBehaviourType": "FULL_SERVICE", "nextCallStopPointLinkRef": "djl-3c8d7568c6cee4d746fe2ced27d9e376", "previousCallStopPointLinkRef": "djl-e07c91c360d355f58d09cf2e147a3c1f", "originalStopPointBehaviourType": "FULL_SERVICE"}, {"ref": "djc-8e27b036dc46e029564938c995b2eb1a", "order": 3, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-e4405407ab0087d5fd5bbfc38c45d141", "plannedArrival": "2024-11-29T00:29:00Z", "plannedDeparture": "2024-11-29T00:29:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-3", "stopPointBehaviourType": "FULL_SERVICE", "nextCallStopPointLinkRef": "djl-6fa9d1a43961ade56dfba4bdc31ec8c2", "previousCallStopPointLinkRef": "djl-3c8d7568c6cee4d746fe2ced27d9e376", "originalStopPointBehaviourType": "FULL_SERVICE"}, {"ref": "djc-ba8a272da5fcc18a32924148cec85e93", "order": 4, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-b8a84d6df007d5e14447a34848844e3d", "plannedArrival": "2024-11-29T00:30:00Z", "plannedDeparture": "2024-11-29T00:30:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-4", "stopPointBehaviourType": "FULL_SERVICE", "nextCallStopPointLinkRef": "djl-ecf7a6b3d3bc3c2d90ed8aea112d8863", "previousCallStopPointLinkRef": "djl-6fa9d1a43961ade56dfba4bdc31ec8c2", "originalStopPointBehaviourType": "FULL_SERVICE"}, {"ref": "djc-79964af79e77c6e39acd1fd0e6512036", "order": 5, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-e71e9e07d1ee5e3ced0965feab017f49", "plannedArrival": "2024-11-29T00:31:00Z", "plannedDeparture": "2024-11-29T00:31:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-5", "stopPointBehaviourType": "FULL_SERVICE", "nextCallStopPointLinkRef": "djl-2007ff0c1c1887c2d5a83d09f83a9ddf", "previousCallStopPointLinkRef": "djl-ecf7a6b3d3bc3c2d90ed8aea112d8863", "originalStopPointBehaviourType": "FULL_SERVICE"}, {"ref": "djc-c08faa3e8cdcffe031e9c5b539e2af4d", "order": 6, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-82c5f6544e0371fa09669277740a42df", "plannedArrival": "2024-11-29T00:32:00Z", "plannedDeparture": "2024-11-29T00:32:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-6", "stopPointBehaviourType": "FULL_SERVICE", "nextCallStopPointLinkRef": "djl-fabe35c88301e24d03ac886606071385", "previousCallStopPointLinkRef": "djl-2007ff0c1c1887c2d5a83d09f83a9ddf", "originalStopPointBehaviourType": "FULL_SERVICE"}, {"ref": "djc-a81ed4c0580e2cabbe7d46193524274f", "order": 7, "events": [], "omitted": false, "cancelled": false, "destination": {"text": "Brenna"}, "stopPointRef": "djs-d58dfde19d2cc1b06583231227a2cdc5", "plannedArrival": "2024-11-29T00:33:00Z", "plannedDeparture": "2024-11-29T00:33:00Z", "journeyPatternPointRef": "RUT:JourneyPatternPoint:008235-7", "stopPointBehaviourType": "FOR_ALIGHTING_ONLY", "previousCallStopPointLinkRef": "djl-fabe35c88301e24d03ac886606071385", "originalStopPointBehaviourType": "FOR_ALIGHTING_ONLY"}], "links": [{"ref": "djl-e07c91c360d355f58d09cf2e147a3c1f", "events": [], "toQuayRef": "NSR:Quay:10481", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.829338, 59.849277], [10.829289, 59.849331], [10.829246, 59.849381], [10.829148, 59.849482], [10.829089, 59.849545], [10.829004, 59.84963], [10.828916, 59.849716], [10.828833, 59.849791], [10.828631, 59.84996], [10.828549, 59.850026], [10.828339, 59.850189], [10.828215, 59.85028], [10.828157, 59.850321], [10.828118, 59.850339], [10.828075, 59.850354], [10.828025, 59.850366], [10.827969, 59.850374], [10.827917, 59.850377], [10.827856, 59.850374], [10.827806, 59.850367], [10.827751, 59.850354], [10.827706, 59.850339], [10.827669, 59.850321], [10.827643, 59.850299], [10.827631, 59.850275], [10.827617, 59.850243], [10.827559, 59.850194], [10.827537, 59.850154], [10.827508, 59.850106], [10.827481, 59.850051], [10.827454, 59.850042], [10.827432, 59.850033], [10.827414, 59.850021], [10.827397, 59.850004], [10.827388, 59.849986], [10.827389, 59.849967], [10.827397, 59.849949], [10.827414, 59.849932], [10.82743, 59.849921], [10.827449, 59.849912], [10.827482, 59.849902], [10.827521, 59.849896], [10.82756, 59.849895], [10.827598, 59.849898], [10.827634, 59.849905], [10.827665, 59.849917], [10.827796, 59.849921], [10.82789, 59.849916], [10.827933, 59.849914], [10.828076, 59.849921], [10.828191, 59.849911], [10.828293, 59.849902], [10.828385, 59.849896], [10.82854, 59.849887], [10.828757, 59.849877], [10.82895, 59.849872], [10.829121, 59.849864], [10.829376, 59.849859], [10.829629, 59.849859], [10.82986, 59.849871], [10.830116, 59.849881], [10.830327, 59.849889], [10.830411, 59.849896], [10.830583, 59.849909], [10.830867, 59.849937], [10.831264, 59.849974], [10.831617, 59.850007], [10.831874, 59.850033], [10.832064, 59.850059], [10.83216, 59.850074], [10.832282, 59.850095], [10.832385, 59.850118], [10.832487, 59.850147], [10.8326, 59.850183], [10.832689, 59.850216], [10.832977, 59.85032], [10.833062, 59.850352], [10.833268, 59.850421], [10.833558, 59.85027], [10.833745, 59.850175], [10.833933, 59.850083], [10.834104, 59.850001], [10.834184, 59.849965], [10.834243, 59.84994], [10.834301, 59.849918], [10.834378, 59.849896], [10.834475, 59.84987], [10.834514, 59.849862], [10.83456, 59.849854], [10.834645, 59.849841], [10.834772, 59.849825], [10.834899, 59.849818], [10.83502, 59.849814], [10.835125, 59.849817], [10.835233, 59.849825], [10.835337, 59.849836], [10.835436, 59.84985], [10.835553, 59.849871], [10.835665, 59.849899], [10.835769, 59.849931], [10.835849, 59.849957], [10.835902, 59.849977]]}, "properties": {}}, "fromQuayRef": "NSR:Quay:10431", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2023-10-16T10:53:40.383186Z", "revision": 2, "dataSource": "hastus"}, "trackLineType": "DETAILED", "measuredLength": "733.7", "serviceLinkRef": "RUT:ServiceLink:10431-10481", "toStopPointRef": "djs-787ac94e6a1f1509c5ec3edda19ab51e", "calculatedLength": "733.5", "fromStopPointRef": "djs-ac9fd9d9dd9e9d3f0df045d7b0f71f7b", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}, {"ref": "djl-3c8d7568c6cee4d746fe2ced27d9e376", "events": [], "toQuayRef": "NSR:Quay:10604", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.835902, 59.849977], [10.83594, 59.849992], [10.835997, 59.850019], [10.83605, 59.850046], [10.836102, 59.850077], [10.836146, 59.850107], [10.836189, 59.850137], [10.836228, 59.850171], [10.836266, 59.850212], [10.836293, 59.850252], [10.836321, 59.850296], [10.836352, 59.850345], [10.83647, 59.850563], [10.836515, 59.850641], [10.836559, 59.850701], [10.836617, 59.850771], [10.836668, 59.850829], [10.836719, 59.850877], [10.836773, 59.85092], [10.836836, 59.850965], [10.836943, 59.850931], [10.837018, 59.850902], [10.837088, 59.85087], [10.837123, 59.85085], [10.837159, 59.850823], [10.837201, 59.850782], [10.837243, 59.850733], [10.837273, 59.850692], [10.837299, 59.850645], [10.837313, 59.850613], [10.837325, 59.850578], [10.837355, 59.850492], [10.837426, 59.850264]]}, "properties": {}}, "fromQuayRef": "NSR:Quay:10481", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-11-14T06:23:57.602753Z", "revision": 4, "dataSource": "hastus"}, "trackLineType": "DETAILED", "measuredLength": "212.7", "serviceLinkRef": "RUT:ServiceLink:10481-10604", "toStopPointRef": "djs-e4405407ab0087d5fd5bbfc38c45d141", "calculatedLength": "212.5", "fromStopPointRef": "djs-787ac94e6a1f1509c5ec3edda19ab51e", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}, {"ref": "djl-6fa9d1a43961ade56dfba4bdc31ec8c2", "events": [], "toQuayRef": "NSR:Quay:10611", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.837426, 59.850264], [10.83743, 59.850248], [10.837462, 59.850134], [10.837603, 59.849626], [10.837809, 59.848893], [10.837905, 59.84855], [10.837918, 59.848501], [10.837937, 59.84845], [10.837964, 59.848402], [10.837994, 59.848367], [10.838035, 59.848321], [10.838074, 59.848288], [10.838132, 59.848243], [10.83818, 59.848213], [10.838252, 59.848175], [10.838325, 59.84814], [10.838408, 59.848107], [10.838502, 59.848079], [10.838595, 59.848057], [10.838685, 59.848039], [10.838782, 59.848026], [10.838877, 59.848016], [10.838971, 59.848009], [10.839069, 59.848007], [10.839171, 59.848009], [10.839273, 59.848015], [10.839364, 59.848026], [10.839465, 59.84804], [10.83957, 59.84806], [10.839678, 59.848087], [10.839773, 59.848111], [10.840083, 59.848199], [10.840253, 59.848241], [10.840327, 59.848258], [10.840412, 59.848278], [10.840489, 59.848293], [10.840545, 59.848301], [10.840606, 59.848308], [10.840639, 59.84831]]}, "properties": {}}, "fromQuayRef": "NSR:Quay:10604", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-11-14T06:23:57.602753Z", "revision": 4, "dataSource": "hastus"}, "trackLineType": "DETAILED", "measuredLength": "385.7", "serviceLinkRef": "RUT:ServiceLink:10604-10611", "toStopPointRef": "djs-b8a84d6df007d5e14447a34848844e3d", "calculatedLength": "385.9", "fromStopPointRef": "djs-e4405407ab0087d5fd5bbfc38c45d141", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}, {"ref": "djl-ecf7a6b3d3bc3c2d90ed8aea112d8863", "events": [], "toQuayRef": "NSR:Quay:10613", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.840639, 59.84831], [10.840694, 59.848314], [10.840791, 59.848317], [10.840878, 59.848314], [10.840953, 59.848308], [10.841036, 59.848298], [10.841114, 59.848283], [10.841205, 59.848262], [10.841279, 59.848242], [10.841356, 59.848215], [10.841417, 59.84819], [10.84149, 59.848155], [10.841542, 59.848122], [10.841599, 59.848079], [10.84164, 59.848045], [10.841672, 59.848012], [10.841703, 59.847969], [10.841726, 59.847929], [10.84174, 59.847893], [10.841747, 59.847855], [10.841748, 59.847815], [10.841742, 59.84777], [10.841731, 59.847728], [10.841708, 59.847669], [10.841569, 59.847411], [10.841533, 59.847345], [10.841508, 59.847284], [10.841493, 59.847231], [10.841485, 59.84718], [10.84148, 59.847127], [10.841479, 59.847081], [10.841489, 59.847029], [10.841506, 59.846981], [10.841526, 59.846933], [10.841561, 59.846876], [10.841602, 59.846821], [10.841643, 59.846775], [10.841685, 59.846734], [10.841743, 59.846684], [10.841801, 59.846642], [10.841858, 59.846606], [10.841917, 59.846574], [10.841969, 59.846546], [10.842039, 59.846519], [10.842064, 59.846508], [10.842132, 59.846482], [10.842223, 59.84645], [10.842298, 59.846422]]}, "properties": {}}, "fromQuayRef": "NSR:Quay:10611", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2023-10-16T10:53:40.383186Z", "revision": 2, "dataSource": "hastus"}, "trackLineType": "DETAILED", "measuredLength": "267.9", "serviceLinkRef": "RUT:ServiceLink:10611-10613", "toStopPointRef": "djs-e71e9e07d1ee5e3ced0965feab017f49", "calculatedLength": "267.9", "fromStopPointRef": "djs-b8a84d6df007d5e14447a34848844e3d", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}, {"ref": "djl-2007ff0c1c1887c2d5a83d09f83a9ddf", "events": [], "toQuayRef": "NSR:Quay:10617", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.842298, 59.846422], [10.842314, 59.846417], [10.842615, 59.846311], [10.843401, 59.846039], [10.843555, 59.845986], [10.84376, 59.845914], [10.843904, 59.845859], [10.843965, 59.845834], [10.844017, 59.84581], [10.844047, 59.845796], [10.844078, 59.845779], [10.844119, 59.845756], [10.844155, 59.845731], [10.844193, 59.8457], [10.844225, 59.845665], [10.844257, 59.845623], [10.844283, 59.845584], [10.844304, 59.845545], [10.844316, 59.84551], [10.844323, 59.845471], [10.844328, 59.845428], [10.844322, 59.845385], [10.844309, 59.845342], [10.844292, 59.845299], [10.844263, 59.845251], [10.844226, 59.845208], [10.844185, 59.84517], [10.844127, 59.845125], [10.844061, 59.845085], [10.843994, 59.845047], [10.843943, 59.845017], [10.843895, 59.844987], [10.843852, 59.844954], [10.843814, 59.844918], [10.843783, 59.844877], [10.843756, 59.844834], [10.843724, 59.844776], [10.8437, 59.844728], [10.843685, 59.844679], [10.843669, 59.844626], [10.843664, 59.844591], [10.843658, 59.844543], [10.84366, 59.844487], [10.843668, 59.844436], [10.843678, 59.844391], [10.843697, 59.844343], [10.843704, 59.844319], [10.84372, 59.844288], [10.843751, 59.844235], [10.843773, 59.844203], [10.843794, 59.844175]]}, "properties": {}}, "fromQuayRef": "NSR:Quay:10613", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2023-10-16T10:53:40.383186Z", "revision": 2, "dataSource": "hastus"}, "trackLineType": "DETAILED", "measuredLength": "315.5", "serviceLinkRef": "RUT:ServiceLink:10613-10617", "toStopPointRef": "djs-82c5f6544e0371fa09669277740a42df", "calculatedLength": "315.4", "fromStopPointRef": "djs-e71e9e07d1ee5e3ced0965feab017f49", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}, {"ref": "djl-fabe35c88301e24d03ac886606071385", "events": [], "toQuayRef": "NSR:Quay:10623", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.843794, 59.844175], [10.843811, 59.844153], [10.843853, 59.84411], [10.843903, 59.84407], [10.84404, 59.843977], [10.844089, 59.843944], [10.844132, 59.843912], [10.84416, 59.843883], [10.844189, 59.84385], [10.84421, 59.843817], [10.844224, 59.84378], [10.844236, 59.843732], [10.844239, 59.843687], [10.844238, 59.843615], [10.844242, 59.843579], [10.844245, 59.843545], [10.844253, 59.84352], [10.844265, 59.843489], [10.844285, 59.843461], [10.844312, 59.843432], [10.844351, 59.843401], [10.844398, 59.843368], [10.844457, 59.843334], [10.844519, 59.843301], [10.844608, 59.843258], [10.84469, 59.843223], [10.844752, 59.843197], [10.844826, 59.843172], [10.844893, 59.84315], [10.844984, 59.843124], [10.845093, 59.843097], [10.845312, 59.843049], [10.845402, 59.843027], [10.845502, 59.842998], [10.845558, 59.842979], [10.845624, 59.842952], [10.845685, 59.842924], [10.845754, 59.842887], [10.84581, 59.842852], [10.845861, 59.84281], [10.845913, 59.842759], [10.845957, 59.842711], [10.846076, 59.842564], [10.846102, 59.842533], [10.846133, 59.842502], [10.846162, 59.842475], [10.846184, 59.842458], [10.84621, 59.842439], [10.846185, 59.842388], [10.846183, 59.842375]]}, "properties": {}}, "fromQuayRef": "NSR:Quay:10617", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2023-10-16T10:53:40.383186Z", "revision": 2, "dataSource": "hastus"}, "trackLineType": "DETAILED", "measuredLength": "256.2", "serviceLinkRef": "RUT:ServiceLink:10617-10623", "toStopPointRef": "djs-d58dfde19d2cc1b06583231227a2cdc5", "calculatedLength": "256.3", "fromStopPointRef": "djs-82c5f6544e0371fa09669277740a42df", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}], "stops": [{"ref": "djs-ac9fd9d9dd9e9d3f0df045d7b0f71f7b", "name": "<PERSON><PERSON><PERSON><PERSON>", "events": [], "quayRef": "NSR:Quay:10431", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.829449, 59.849305]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1433", "entryDirectionWindow": 360}, "publicCode": "B", "stopAreaRef": "NSR:StopPlace:58228", "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5690", "legacyQuayRef": "301095110", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-01-18T07:02:56.179506Z", "revision": 4, "dataSource": "hastus"}}, {"ref": "djs-787ac94e6a1f1509c5ec3edda19ab51e", "name": "<PERSON><PERSON><PERSON><PERSON>", "events": [], "quayRef": "NSR:Quay:10481", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.835953, 59.849922]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1646", "entryDirectionWindow": 360}, "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5716", "legacyQuayRef": "301096201", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-01-18T07:02:57.114432Z", "revision": 4, "dataSource": "hastus"}}, {"ref": "djs-e4405407ab0087d5fd5bbfc38c45d141", "name": "Dalssvingen", "events": [], "quayRef": "NSR:Quay:10604", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.837353, 59.850255]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1648", "entryDirectionWindow": 360}, "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5781", "legacyQuayRef": "301099301", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-04-11T07:09:21.678900Z", "revision": 5, "dataSource": "hastus"}}, {"ref": "djs-b8a84d6df007d5e14447a34848844e3d", "name": "<PERSON><PERSON><PERSON>", "events": [], "quayRef": "NSR:Quay:10611", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.840625, 59.848275]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1652", "entryDirectionWindow": 360}, "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5786", "legacyQuayRef": "301099401", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-01-18T07:02:57.114432Z", "revision": 4, "dataSource": "hastus"}}, {"ref": "djs-e71e9e07d1ee5e3ced0965feab017f49", "name": "Brenna", "events": [], "quayRef": "NSR:Quay:10613", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.84224, 59.846399]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1657", "entryDirectionWindow": 360}, "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5788", "legacyQuayRef": "301099501", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-01-18T07:02:57.114432Z", "revision": 4, "dataSource": "hastus"}}, {"ref": "djs-82c5f6544e0371fa09669277740a42df", "name": "<PERSON><PERSON><PERSON>", "events": [], "quayRef": "NSR:Quay:10617", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.843659, 59.844275]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1659", "entryDirectionWindow": 360}, "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5790", "legacyQuayRef": "301099601", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-01-18T07:02:57.114432Z", "revision": 4, "dataSource": "hastus"}}, {"ref": "djs-d58dfde19d2cc1b06583231227a2cdc5", "name": "Pasop", "events": [], "quayRef": "NSR:Quay:10623", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.846116, 59.842368]}, "properties": {}}, "circleRadius": 25, "detectionType": "CIRCLE", "entryDirection": 0, "detectionPointRef": "1654", "entryDirectionWindow": 360}, "tariffZones": ["RUT:FareZone:4", "RUT:TariffZone:1"], "stopPlaceRef": "NSR:StopPlace:5794", "legacyQuayRef": "301099701", "lifeCycleInfo": {"created": "2023-03-31T11:45:53.093268Z", "modified": "2024-01-18T07:02:57.114432Z", "revision": 4, "dataSource": "hastus"}}], "linkRefs": [], "lastArrivalDateTime": "2024-11-29T00:33Z", "firstDepartureDateTime": "2024-11-29T00:27Z"}, "type": "ServiceJourney", "order": 32, "events": [{"id": "jev-00e18e6b6bc068555164d1802afbf289", "type": "IMPORTED", "source": "journey-data-set-lifecycle/ruter/hastus/bifrost", "traceId": "trace-7ef9fda8-63f2-46f7-abf4-1a1c4008620a", "metadata": [], "timestamp": "2024-11-14T08:36:07.365630583Z", "description": "The journey was initially imported: Dated Journey imported by journey-manager."}, {"id": "jev-4d74736917a0e84dda0e4964f4ee3757", "type": "VEHICLE_ASSIGNED", "source": "assignment/internal", "traceId": "2f39493b-224b-47d5-9007-5a8879a095c7", "metadata": [{"key": "VEHICLE_REF", "value": "XNL401E500D041817"}, {"key": "ASSIGNMENT_REF", "value": "3233801b3e8e4d67b8165870ffbb8e4b"}, {"key": "ASSIGNMENT_CODE", "value": "PLANNED"}, {"key": "FIRST_DEPARTURE_DATE_TIME", "value": "2024-11-29T00:27:00Z"}, {"key": "LAST_ARRIVAL_DATE_TIME", "value": "2024-11-29T00:33:00Z"}], "timestamp": "2024-11-28T13:06:02.139Z", "description": "[VEHICLE_ASSIGNED] A vehicle was assigned to the journey OK: Attempt processed without errors"}, {"id": "jev-a811b5dbfae7cc7a17365eb09bad35cd", "type": "VEHICLE_UNASSIGNED", "source": "assignment/internal", "traceId": "a1ff607c-9cf3-4536-aa58-d0c9771e412f", "metadata": [{"key": "VEHICLE_REF", "value": "XNL401E500D041817"}, {"key": "ASSIGNMENT_REF", "value": "3233801b3e8e4d67b8165870ffbb8e4b"}, {"key": "ASSIGNMENT_CODE", "value": "CANCELLED"}], "timestamp": "2024-11-29T00:33:21.459Z", "description": "[VEHICLE_UNASSIGNED] A vehicle was unassigned from the journey OK: Sign-off initiated by vehicle [XNL401E500D041817]: signoff/cancelled"}, {"id": "jev-cd293f37e10bb3e78c925728772958b2", "type": "MISSING_SIGN_ON", "source": "assignment/internal", "traceId": "am-799e7595edca42b1900053cb0dfb668b", "metadata": [{"key": "FIRST_DEPARTURE_DATE_TIME", "value": "2024-11-29T00:27:00Z"}, {"key": "TIME_BOUNDARY", "value": "PT1M30S"}], "timestamp": "2024-11-29T00:34:04.088669252Z", "description": "[MISSING_SIGN_ON] The journey was not assigned within a configured time before / after first departure OK: MISSING_SIGN_ON: OK"}], "header": {"ownerId": "ruter", "traceId": "am-799e7595edca42b1900053cb0dfb668b", "originId": "hastus", "publisherId": "journey-manager", "expiresTimestamp": "2024-11-30T00:00Z", "messageTimestamp": "2024-11-29T00:34:04.088669252Z", "receivedTimestamp": "2024-11-29T00:34:04.088669252Z", "publishedTimestamp": "2024-11-26T00:03:37.016576809Z"}, "public": true, "deleted": false, "lineage": [], "omitted": false, "vehicles": [], "cancelled": false, "direction": "1", "operators": [{"name": "Connect Bus AS", "operatorRef": "RUT:Operator:130"}], "vehicleTask": "7307", "lifeCycleInfo": {"created": "2024-11-14T08:36:07.365631Z", "modified": "2024-11-14T08:36:07.365631Z", "revision": 4, "dataSource": "hastus"}, "operatingDate": "2024-11-28", "journeyReferences": {"blockRef": "7307-2024-11-28", "journeyRef": "RUT:ServiceJourney:008235-010233-012700-73-1-Weekday-81", "datedBlockRef": "RUT:DatedBlock:208573-7307-202411281421000", "vehicleTaskRef": "7307", "legacyJourneyRef": "7307-2024-11-29T01:27:00+01:00", "vehicleJourneyId": "81", "journeyPatternRef": "RUT:JourneyPattern:008235", "runtimePatternRef": "RUT:RunTimePattern:010233", "externalJourneyRef": "RUT:ServiceJourney:2973a2258d7447798930f550af7ba281", "externalJourneyRefV2": "RUT:ServiceJourney:2973a2258d7447798930f550af7ba281", "datedServiceJourneyId": "RUT:DatedServiceJourney:djj-0ea3a8c1f1c272570877c66b16d009be", "legacyDatedJourneyRef": "9303cfac5844953fcc12114599ebfe5d", "legacyJourneyPatternRefs": ["RUT:JourneyPattern:73-50"]}, "operatorContracts": [{"name": "Oslo_Sør", "operator": {"name": "Connect Bus AS", "operatorRef": "RUT:Operator:130"}, "operatorContractRef": "RUT:OperatorContract:2311"}]}