{"ref": "jre149bf2973de4f529d468e29ee7a04ab", "header": {"messageTimestamp": "2022-05-12T07:40:44.136102Z", "receivedTimestamp": "2022-05-21T13:37+02:00", "publishedTimestamp": "2022-05-21T13:37:03+02:00", "expiresTimestamp": "2022-06-20T13:37:03+02:00", "traceId": "d6959f46-a4fa-4d76-8e02-8b9e1072ce68", "ownerId": "RUTER", "originId": "bifrost", "publisherId": "dated-journey-manager"}, "events": [{"type": "IMPORTED", "source": "block/planned/bifrost", "timestamp": "2022-05-21T13:37+02:00", "description": "The entity was initially imported: Journey imported by dated-journey-manager.", "metadata": []}], "type": "DeadRun", "status": "ORIGINAL", "name": "Slemmestad garasje-Slemmestad", "vehicleTask": "25004", "line": {"lineRef": "RUT:Line:0", "name": "", "publicCode": "0", "privateCode": "0", "backgroundColour": "", "textColour": ""}, "journeyReferences": {"vehicleJourneyId": "551660", "vehicleTaskRef": "25004", "blockRef": "25004-2022-05-22", "datedBlockRef": "RUT:DatedBlock:165475-25004-202205221716000", "journeyRef": "RUT:DeadRun:000474-002209-171600", "legacyJourneyRef": "25004-2022-05-22T17:16:00+02:00", "journeyPatternRef": "RUT:DeadRunJourneyPattern:0-25004502-Slemga-25813", "externalJourneyRef": "RUT:DeadRun:0-165475-23344209", "datedServiceJourneyId": "RUT:DatedServiceJourneyId:jre149bf2973de4f529d468e29ee7a04ab"}, "operators": [], "operatorContracts": [], "lineage": [{"value": "RUT:DeadRunJourneyPattern:0-25004502-Slemga-25813", "type": "JOURNEY_PATTERN_REF", "source": "HASTUS"}, {"value": "25004-2022-05-22T17:16:00+02:00", "type": "JOURNEY_REF", "source": "HASTUS"}, {"value": "RUT:DeadRun:000474-002209-171600", "type": "JOURNEY_REF", "source": "BIFROST"}], "vehicles": [], "operatingDate": "2022-05-22", "journeyDate": "2022-05-22", "plan": {"calls": [{"ref": "internal-planned-call-ref-001", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "srd7bbeac8612b438b8bc075b591afa1f6", "order": 1, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-25004502-1", "destination": {"text": "Slemmestad"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2022-05-22T15:16:00Z", "plannedDeparture": "2022-05-22T15:16:00Z", "nextCallStopPointLinkRef": "lr76a14bf84f2d46bc8ea114c0168bbc3d"}, {"ref": "internal-planned-call-ref-002", "events": [], "cancelled": false, "status": "ORIGINAL", "stopPointRef": "sr340622fd443b41c3852f69c7df51538d", "order": 2, "journeyPatternPointRef": "RUT:StopPointInJourneyPattern:0-25004502-2", "destination": {"text": "Slemmestad"}, "stopPointBehaviourType": "NO_SERVICE", "originalStopPointBehaviourType": "NO_SERVICE", "plannedArrival": "2022-05-22T15:21:00Z", "plannedDeparture": "2022-05-22T15:21:00Z"}], "stops": [{"ref": "srd7bbeac8612b438b8bc075b591afa1f6", "status": "ORIGINAL", "quayRef": "RUT:Quay:Slemga", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.487238669248232, 59.7829428999231]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Slemmestad garasje", "tariffZones": []}, {"ref": "sr340622fd443b41c3852f69c7df51538d", "status": "ORIGINAL", "quayRef": "NSR:Quay:25813", "geoPoint": {"location": {"type": "Feature", "geometry": {"type": "Point", "coordinates": [10.486509, 59.779217]}, "properties": {}}, "circleRadius": 25, "entryDirection": 0, "detectionType": "CIRCLE"}, "name": "Slemmestad", "tariffZones": []}], "links": [{"ref": "lr76a14bf84f2d46bc8ea114c0168bbc3d", "serviceLinkRef": "RUT:ServiceLink:Slemga-25813", "fromStopPointRef": "srd7bbeac8612b438b8bc075b591afa1f6", "fromQuayRef": "RUT:Quay:Slemga", "toStopPointRef": "sr340622fd443b41c3852f69c7df51538d", "toQuayRef": "NSR:Quay:25813", "trackLine": {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[10.486964, 59.782901], [10.486455, 59.779264]]}, "properties": {}}, "trackLineType": "DETAILED", "calculatedLength": "406.2002911811819", "measuredLength": "1300.0", "stopPointLinkType": "UNKNOWN", "trafficPriorityPoints": []}]}, "direction": "2", "order": 1}