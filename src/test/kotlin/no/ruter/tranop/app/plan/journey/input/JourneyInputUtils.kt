package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJMessageHeader
import no.ruter.plandata.journey.dated.v2.dto.model.common.link.PDJStopPointLink
import no.ruter.plandata.journey.dated.v2.dto.model.common.stop.PDJStopPoint
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCall
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyPlan
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyReferences
import no.ruter.plandata.journey.dated.v2.dto.value.PDJJourneyType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJStopPointBehaviourType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJStopPointLinkType
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.test.TestUtils
import java.time.OffsetDateTime

object JourneyInputUtils {
    const val QUAY_REF_1 = "start-call-quay-ref"
    const val QUAY_REF_2 = "end-call-quay-ref"

    const val DEFAULT_VEHICLE_TASK_ID = "5555"
    const val DEFAULT_DATED_JOURNEY_REF = "DJR:default:1"

    fun createDatedJourney(
        ref: String = DEFAULT_DATED_JOURNEY_REF,
        operatingDate: String = "2022-01-01",
        startDateTime: String = "${operatingDate}T12:00+02:00",
        endDateTime: String = "${operatingDate}T14:00+02:00",
        vehicleTaskRef: String = DEFAULT_VEHICLE_TASK_ID,
    ) = PDJDatedJourney().apply {
        this.type = PDJJourneyType.SERVICE_JOURNEY
        this.name = "some-journey-name"

        this.header =
            PDJMessageHeader().apply {
                this.expiresTimestamp = OffsetDateTime.now().plusDays(7).toString()
                this.ownerId = "ruter" // otherwise it wouldn't get published
                this.originId = "test-origin-id"
            }
        this.cancelled = false
        this.ref = ref
        this.operatingDate = operatingDate

        val stop1 =
            PDJStopPoint()
                .withRef("start-call-stop-point-ref")
                .withQuayRef(QUAY_REF_1)
                .withEvents(emptyList())
        val stop2 =
            PDJStopPoint()
                .withRef("end-call-stop-point-ref")
                .withQuayRef(QUAY_REF_2)
                .withEvents(emptyList())
        this.plan =
            PDJDatedJourneyPlan().apply {
                calls =
                    listOf(
                        PDJDatedJourneyCall()
                            .withRef("start-call-ref")
                            .withStopPointRef(stop1.ref)
                            .withStopPointBehaviourType(PDJStopPointBehaviourType.FOR_BOARDING_ONLY)
                            .withOriginalStopPointBehaviourType(PDJStopPointBehaviourType.FOR_BOARDING_ONLY)
                            .withCancelled(false)
                            .apply {
                                plannedDeparture = startDateTime
                            },
                        PDJDatedJourneyCall()
                            .withRef("end-call-ref")
                            .withStopPointRef(stop2.ref)
                            .withStopPointBehaviourType(PDJStopPointBehaviourType.FOR_ALIGHTING_ONLY)
                            .withOriginalStopPointBehaviourType(PDJStopPointBehaviourType.FOR_ALIGHTING_ONLY)
                            .withCancelled(false)
                            .apply {
                                plannedArrival = endDateTime
                            },
                    )
                stops = listOf(stop1, stop2)
                links =
                    listOf(
                        PDJStopPointLink()
                            .withRef(
                                "start-call-link-ref",
                            ).withFromStopPointRef(stop1.ref)
                            .withToStopPointRef(stop2.ref)
                            .withStopPointLinkType(PDJStopPointLinkType.ROAD),
                        PDJStopPointLink()
                            .withRef("end-call-link-ref")
                            .withFromStopPointRef(stop2.ref)
                            .withToStopPointRef(stop1.ref)
                            .withStopPointLinkType(PDJStopPointLinkType.ROAD),
                    )
            }
        journeyReferences =
            PDJDatedJourneyReferences().apply {
                this.vehicleTaskRef = vehicleTaskRef
                this.externalJourneyRef = "some-external-journey-ref"
            }
    }

    fun readDatedJourneyFolder(
        folderPath: String,
        update: (PDJDatedJourney) -> Unit = {},
    ): Map<String, List<PDJDatedJourney>> =
        TestUtils
            .readAllFiles(folderPath)
            .map { content ->
                JsonUtils.toObject(content, PDJDatedJourney::class.java).apply(update)
            }.sortedBy {
                it.plan
                    ?.calls
                    ?.first()
                    ?.plannedDeparture ?: "some-sort"
            }.groupBy { it.toMapKey() }

    private fun PDJDatedJourney.toMapKey(): String = "$vehicleTask-$operatingDate"
}
