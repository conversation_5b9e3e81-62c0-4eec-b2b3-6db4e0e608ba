package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.test.assertSize
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DatedJourneyKafkaPublishTest : AbstractKafkaTest() {
    @BeforeEach
    override fun setup() {
        super.setup()
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `Publish - DatedJourneyStopPoint and links`() {
        val journey = vehicleTask1datedJourney1
        val plan = vehicleTask1datedJourney1.plan
        val stops = plan.stops
        val links = plan.links
        val now = timeService.now()
        timeService.offset(journey)

        datedJourneyInputService.process(journey.ref, journey)
        stops.forEach {
            stopPointInputService.process(it.ref, it)
        }
        links.forEach {
            stopPointLinkInputService.process(it.ref, it)
        }
        Assertions.assertEquals(1, journeyRepo.count())
        Assertions.assertEquals(2, stopPointRepository.count())
        Assertions.assertEquals(1, stopPointLinkRepository.count())

        publishDatedJourneyRoutine.execute(now)
        publishStopPointRoutine.execute(now)
        publishStopPointLinkRoutine.execute(now)

        kafka.stopPointProducer.getRecords().assertSize(size = 2, message = "stops")
        kafka.stopPointLinkProducer.getRecords().assertSize(size = 1, message = "links")
        kafka.datedJourneyProducer.getRecords().assertSize(size = 1, message = "datedJourneys")
    }
}
