package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DatedJourneyKafkaRepublishLifeCycleTest : AbstractKafkaTest() {
    lateinit var journeyValidator: DatedJourneyInputValidator

    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
        journeyValidator = DatedJourneyInputValidator(timeService)
    }

    @Test
    fun `dated journey with op date today + 1 should reset its published revision to 0 and be republished`() {
        val journey1 =
            JourneyInputUtils.createDatedJourney(
                ref = "some-ref-next-day",
                operatingDate =
                    timeService
                        .now()
                        .plusDays(1)
                        .toLocalDate()
                        .toString(),
            )
        val context1 = journeyValidator.validate(journey1.ref, journey1)
        journeyRepo.store(context1)

        val journey2 =
            JourneyInputUtils.createDatedJourney(
                ref = "some-ref-current-day",
                operatingDate =
                    timeService
                        .now()
                        .toLocalDate()
                        .toString(),
            )
        val context2 = journeyValidator.validate(journey1.ref, journey2)
        journeyRepo.store(context2)

        republishDatedJourneyRoutine.execute(timeService.now())
        publishDatedJourneyRoutine.execute(timeService.now())

        val currentDayJourney = journeyRepo.fetchByRef("some-ref-current-day")
        val nextDayJourney = journeyRepo.fetchByRef("some-ref-next-day")

        Assertions.assertThat(currentDayJourney?.record?.publishedRevision == 1)
        Assertions.assertThat(currentDayJourney?.record?.revision == 1)

        Assertions.assertThat(nextDayJourney?.record?.publishedRevision == 0)
        Assertions.assertThat(nextDayJourney?.record?.revision == 1)
    }
}
