package no.ruter.tranop.app.plan.journey.db

import no.ruter.rdp.common.json.diff.JsonDiff
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.plan.journey.input.AbstractDatedJourneyInputTest
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.time.Duration.Companion.days

class DatedJourneyRepositoryTest : AbstractDatedJourneyInputTest() {
    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `insert and fetch record in db`() {
        val initialDatedJourneys = journeyRepo.fetchAll()
        Assertions.assertEquals(0, initialDatedJourneys.size)
        val datedJourney = JourneyInputUtils.createDatedJourney()

        storeDatedJourney(datedJourney.ref, datedJourney)

        val datedJourneysFromDb = journeyRepo.fetchAll()
        Assertions.assertEquals(1, datedJourneysFromDb.size)
        val datedJourneyFromDb = datedJourneysFromDb.first()

        val jsonDiff = JsonDiff.of(datedJourney, datedJourneyFromDb.journey)
        Assertions.assertEquals(5, jsonDiff.added)
        Assertions.assertEquals(0, jsonDiff.removed)
        Assertions.assertEquals(0, jsonDiff.modified)

        val summary = jsonDiff.asString().lines()
        Assertions.assertEquals("\$.plan.calls[0].omitted : added : false", summary[0])
        Assertions.assertEquals("\$.plan.calls[1].omitted : added : false", summary[1])
        Assertions.assertEquals("\$.omitted : added : false", summary[2])
        Assertions.assertEquals("\$.direction : added : \"0\"", summary[3])
        Assertions.assertTrue(summary[4].startsWith("\$.journeyState : added : {"))
    }

    @Test
    fun `increase revision when updating same journey`() {
        val inputJourney = JourneyInputUtils.createDatedJourney()
        val ref = inputJourney.ref
        storeDatedJourney(key = ref, datedJourney = inputJourney)

        val journeyR1 = assertJourneyRecordExists(ref)
        Assertions.assertEquals(ref, journeyR1.record.ref)
        Assertions.assertEquals(1, journeyR1.record.revision)

        val name2 = MapperUtils.randomId(prefix = "random-name2-")
        journeyR1.journey.name = name2
        storeInternalJourney(key = ref, journey = journeyR1.journey)

        val journeyR2 = assertJourneyRecordExists(ref)
        Assertions.assertEquals(2, journeyR2.record.revision)
        Assertions.assertEquals(name2, journeyR2.journey.name)
    }

    @Test
    fun `fetch by vehicle task and date returns dated journeys for the correct vehicle task`() {
        val vehicleTaskId1 = "A"
        val datedJourney1 =
            JourneyInputUtils.createDatedJourney(ref = "journey1", vehicleTaskRef = vehicleTaskId1)
        val vehicleTaskId2 = "B"
        val datedJourney2 =
            JourneyInputUtils.createDatedJourney(ref = "journey2", vehicleTaskRef = vehicleTaskId2)

        storeDatedJourneys(datedJourney1, datedJourney2)
        val stored = journeyRepo.fetchAll()
        Assertions.assertEquals(2, stored.size)

        val operatingDate = stored.first().operatingDate!!

        var result = journeyRepo.fetchVehicleTaskJourneys(vehicleTaskId1, operatingDate)
        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(
            "journey1",
            result
                .first()
                .journey.ref,
        )

        result = journeyRepo.fetchVehicleTaskJourneys(vehicleTaskId2, operatingDate)
        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(
            "journey2",
            result
                .first()
                .journey.ref,
        )
    }

    @Test
    fun `fetch by vehicle task and date time returns dated journeys for the correct time`() {
        val datedJourney1 =
            JourneyInputUtils.createDatedJourney(
                ref = "journey1",
                operatingDate = "2022-01-01",
                startDateTime = "2022-01-01T12:00+01:00",
                endDateTime = "2022-01-01T14:00+01:00",
            )
        val datedJourney2 =
            JourneyInputUtils.createDatedJourney(
                ref = "journey2",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-02T12:00+01:00",
                endDateTime = "2022-01-02T18:00+01:00",
            )
        val datedJourney3 =
            JourneyInputUtils.createDatedJourney(
                ref = "journey3",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-02T19:00+01:00",
                endDateTime = "2022-01-02T23:00+01:00",
            )
        val datedJourney4 =
            JourneyInputUtils.createDatedJourney(
                ref = "journey4",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-03T02:00+01:00",
                endDateTime = "2022-01-03T03:00+01:00",
            )
        val datedJourney5 =
            JourneyInputUtils.createDatedJourney(
                ref = "journey5",
                operatingDate = "2022-01-03",
                startDateTime = "2022-01-03T04:00+01:00",
                endDateTime = "2022-01-03T05:00+01:00",
            )

        storeDatedJourneys(datedJourney1, datedJourney2, datedJourney3, datedJourney4, datedJourney5)
        val stored = journeyRepo.fetchAll()
        Assertions.assertEquals(5, stored.size)

        val vehicleTaskId = JourneyInputUtils.DEFAULT_VEHICLE_TASK_ID
        val operatingDate = LocalDate.parse("2022-01-02")
        val result = journeyRepo.fetchVehicleTaskJourneys(vehicleTaskId, operatingDate)
        Assertions.assertEquals(3, result.size)
        Assertions.assertEquals(
            "journey2",
            result
                .first()
                .journey.ref,
        )
        Assertions.assertEquals("journey3", result[1].journey.ref)
        Assertions.assertEquals(
            "journey4",
            result
                .last()
                .journey.ref,
        )
    }

    @Test
    fun `same vehicleTaskRef for given journeys with back to back arrival and departure returns 2 operating dates`() {
        storeDatedJourneys(
            JourneyInputUtils.createDatedJourney(
                ref = "journey1",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-03T01:00+02:00",
                endDateTime = "2022-01-03T02:00+02:00",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey2",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-03T01:00+02:00",
                endDateTime = "2022-01-03T02:00+02:00",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey3",
                operatingDate = "2022-01-03",
                startDateTime = "2022-01-03T02:00+02:00",
                endDateTime = "2022-01-03T03:00+02:00",
            ),
        )
        val stored = journeyRepo.fetchAll()
        Assertions.assertEquals(3, stored.size)
        val operatingDates = stored.mapNotNull { it.operatingDate }.toSortedSet()
        Assertions.assertEquals(2, operatingDates.size)
        Assertions.assertEquals(LocalDate.parse("2022-01-02"), operatingDates.first())
        Assertions.assertEquals(LocalDate.parse("2022-01-03"), operatingDates.last())
    }

    @Test
    fun `same vehicleTaskRef for given journeys with overlapping arrival and departure returns 2 operating dates`() {
        storeDatedJourneys(
            JourneyInputUtils.createDatedJourney(
                ref = "journey1",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-02T23:00+02:00",
                endDateTime = "2022-01-03T01:00+02:00",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey2",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-03T01:00+02:00",
                endDateTime = "2022-01-03T03:00+02:00",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey3",
                operatingDate = "2022-01-03",
                startDateTime = "2022-01-03T02:00+02:00",
                endDateTime = "2022-01-03T03:00+02:00",
            ),
        )
        val stored = journeyRepo.fetchAll()
        Assertions.assertEquals(3, stored.size)
        val operatingDates = stored.mapNotNull { it.operatingDate }.toSortedSet()
        Assertions.assertEquals(2, operatingDates.size)
        Assertions.assertEquals(LocalDate.parse("2022-01-02"), operatingDates.first())
        Assertions.assertEquals(LocalDate.parse("2022-01-03"), operatingDates.last())
    }

    @Test
    fun `same vehicleTaskRef for given journeys returns correct dated journeys for each operating date`() {
        val stored =
            storeDatedJourneys(
                JourneyInputUtils.createDatedJourney(
                    ref = "journey1",
                    operatingDate = "2022-01-02",
                    startDateTime = "2022-01-02T23:00+02:00",
                    endDateTime = "2022-01-03T01:00+02:00",
                ),
                JourneyInputUtils.createDatedJourney(
                    ref = "journey2",
                    operatingDate = "2022-01-02",
                    startDateTime = "2022-01-03T01:00+02:00",
                    endDateTime = "2022-01-03T02:00+02:00",
                ),
                JourneyInputUtils.createDatedJourney(
                    ref = "journey3",
                    operatingDate = "2022-01-03",
                    startDateTime = "2022-01-03T02:00+02:00",
                    endDateTime = "2022-01-03T03:00+02:00",
                ),
            )
        Assertions.assertEquals(stored, journeyRepo.fetchAll().size)

        val vehicleTaskId = JourneyInputUtils.DEFAULT_VEHICLE_TASK_ID
        var vehicleTask =
            journeyRepo.fetchVehicleTaskJourneys(
                vehicleTaskId = vehicleTaskId,
                operatingDate = LocalDate.parse("2022-01-02"),
            )
        Assertions.assertEquals(2, vehicleTask.size)
        Assertions.assertEquals(
            "journey1",
            vehicleTask
                .first()
                .journey.ref,
        )
        Assertions.assertEquals(
            "journey2",
            vehicleTask
                .last()
                .journey.ref,
        )

        vehicleTask =
            journeyRepo.fetchVehicleTaskJourneys(
                vehicleTaskId = vehicleTaskId,
                operatingDate = LocalDate.parse("2022-01-03"),
            )
        Assertions.assertEquals(1, vehicleTask.size)
        Assertions.assertEquals(
            "journey3",
            vehicleTask
                .first()
                .journey.ref,
        )
    }

    // TODO: Move this. It's not testing repository class, but rather higher-level input service.
    @Test
    fun `dated journey update after tombstoning should reset tombstone flag`() {
        val journeyRef = "some-ref"
        val now = timeService.now()
        storeDatedJourneys(
            JourneyInputUtils.createDatedJourney(
                ref = journeyRef,
                operatingDate = now.toLocalDate().toString(),
                startDateTime = now.toString(),
                endDateTime = now.plusHours(8).toString(),
            ),
        )

        timeService.offset(3.days)
        datedJourneyService.markTombstoned(journeyRef)

        val storedJourney = assertJourneyRecordExists(journeyRef)
        assertNotNull(storedJourney.record.tombstonedAt)

        storeDatedJourneys(
            JourneyInputUtils.createDatedJourney(
                ref = journeyRef,
                operatingDate = now.toLocalDate().toString(),
                startDateTime = now.toString(),
                endDateTime = now.plusHours(10).toString(),
            ),
        )

        val reStoredJourney = assertJourneyRecordExists(journeyRef)
        assertNull(reStoredJourney.record.tombstonedAt)
    }
}
