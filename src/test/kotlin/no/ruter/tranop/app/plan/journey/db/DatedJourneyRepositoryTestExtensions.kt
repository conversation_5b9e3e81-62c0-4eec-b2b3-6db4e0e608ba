package no.ruter.tranop.app.plan.journey.db

import java.time.LocalDate

// Note: Implemented as extension functions, since its only used in tests and not part of production code.
fun DatedJourneyRepository.fetchVehicleTaskJourneys(
    vehicleTaskId: String,
    operatingDate: LocalDate,
): List<InternalDatedJourney> {
    val c1 = table.OPERATING_DATE.eq(operatingDate)
    val c2 = table.VEHICLE_TASK_REF.eq(vehicleTaskId)
    val condition = c1.and(c2)

    return fetch(condition)
}
