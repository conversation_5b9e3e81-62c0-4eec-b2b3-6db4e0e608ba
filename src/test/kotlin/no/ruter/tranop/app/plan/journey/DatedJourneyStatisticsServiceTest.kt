package no.ruter.tranop.app.plan.journey

import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputMapper
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import no.ruter.tranop.app.test.AbstractBaseTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class DatedJourneyStatisticsServiceTest : AbstractBaseTest() {
    @Autowired
    lateinit var datedJourneyStatisticsService: DatedJourneyStatisticsService

    private val inputMapper = DatedJourneyInputMapper()
    lateinit var journeyValidator: DatedJourneyInputValidator

    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
        journeyValidator = DatedJourneyInputValidator(timeService)
    }

    @Test
    fun `DatedJourneyStatisticsService can run`() {
        val journey1 =
            JourneyInputUtils.createDatedJourney(
                ref = "some-ref",
            )
        timeService.offset(journey1)
        val internalJourney1 = inputMapper.mapDatedJourney(journey1)
        val context1 = journeyValidator.validate(journey1.ref, internalJourney1)
        journeyRepo.store(context1)

        datedJourneyStatisticsService.renderStats()
    }
}
