package no.ruter.tranop.app.plan.link.output

import no.ruter.plandata.journey.dated.v2.dto.model.common.link.PDJStopPointLink
import no.ruter.plandata.journey.dated.v2.dto.value.PDJStopPointLinkType
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.seconds

class StopPointLinkKafkaPublishTest : AbstractKafkaTest() {
    @BeforeEach
    fun reset() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `each stop point link at input gives mapped and published DTO at output`() {
        val ref = "StopPointLinkPublishTest-some-stop-point-link-ref"
        val stopPointLink =
            PDJStopPointLink()
                .withRef(
                    ref,
                ).withStopPointLinkType(PDJStopPointLinkType.ROAD)
                .withFromStopPointRef("some-from-stop-point-ref")
                .withToStopPointRef("some-to-stop-point-ref")

        val result = stopPointLinkInputService.process(ref, stopPointLink)

        Assertions.assertTrue(result.valid())

        timeService.offset(30.seconds)

        publishStopPointLinkDTORoutine.execute(timeService.now())

        val producedRecords = kafka.stopPointLinkDTOProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)

        // now check that publish mark works
        publishStopPointLinkDTORoutine.execute(timeService.now())

        val producedRecords2 = kafka.stopPointLinkDTOProducer.getRecords()
        Assertions.assertEquals(1, producedRecords2.size)

        // now change and send new revision - must be 2 records on wire
        val result2 = stopPointLinkInputService.process(ref, stopPointLink.withCalculatedLength("100"))

        Assertions.assertTrue(result2.valid())

        timeService.offset(60.seconds)

        publishStopPointLinkDTORoutine.execute(timeService.now())

        val producedRecords3 = kafka.stopPointLinkDTOProducer.getRecords()
        Assertions.assertEquals(2, producedRecords3.size)
    }
}
