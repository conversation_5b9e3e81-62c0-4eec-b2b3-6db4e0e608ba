package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class DatedJourneyKafkaTombstoneLifeCycleTest : AbstractKafkaTest() {
    lateinit var journeyValidator: DatedJourneyInputValidator

    @BeforeEach
    fun reset() {
        journeyValidator = DatedJourneyInputValidator(timeService)
        testEnvironment.reset()
        timeService.reset()
        kafka.datedJourneyProducer.clear()
        kafka.assignmentJourneyProducer.clear()
    }

    @Test
    fun `dated journey should be tomb-stoned 48 hours after last arrival during frequent lifecycle`() {
        val now = OffsetDateTime.parse("2022-01-01T08:00+02:00")
        timeService.offset(now)

        listOf(
            JourneyInputUtils.createDatedJourney(
                ref = "journey1",
                operatingDate = now.plusDays(0).toLocalDate().toString(),
                startDateTime = now.plusDays(0).withHour(8).toString(),
                endDateTime = now.plusDays(0).withHour(12).toString(),
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey2",
                operatingDate = now.plusDays(1).toLocalDate().toString(),
                startDateTime = now.plusDays(1).withHour(8).toString(),
                endDateTime = now.plusDays(1).withHour(12).toString(),
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey3",
                operatingDate = now.plusDays(2).toLocalDate().toString(),
                startDateTime = now.plusDays(2).withHour(8).toString(),
                endDateTime = now.plusDays(2).withHour(12).toString(),
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey4",
                operatingDate = now.plusDays(3).toLocalDate().toString(),
                startDateTime = now.plusDays(3).withHour(8).toString(),
                endDateTime = now.plusDays(3).withHour(12).toString(),
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey5",
                operatingDate = now.plusDays(4).toLocalDate().toString(),
                startDateTime = now.plusDays(4).withHour(8).toString(),
                endDateTime = now.plusDays(4).withHour(12).toString(),
            ),
        ).forEach {
            val context = journeyValidator.validate(it.ref, it)
            journeyRepo.store(context)
        }

        val future = now.plusDays(5)
        timeService.offset(future)

        publishDatedJourneyRoutine.execute(timeService.now())
        tombstoneDatedJourneyRoutine.execute(timeService.now())
        publishDatedJourneyRoutine.execute(timeService.now().plusMinutes(15))
        val expectedNumTombstoned = 3
        val tombstonedJourneys = kafka.datedJourneyProducer.getRecords().filter { it.value() == null }
        Assertions.assertEquals(expectedNumTombstoned, tombstonedJourneys.size)
    }
}
