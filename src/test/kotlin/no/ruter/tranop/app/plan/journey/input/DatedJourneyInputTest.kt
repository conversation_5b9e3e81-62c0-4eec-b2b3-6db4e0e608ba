package no.ruter.tranop.app.plan.journey.input

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaInputConfigProperties
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.apache.kafka.clients.producer.ProducerRecord
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.Duration
import kotlin.test.assertNotNull

class DatedJourneyInputTest : AbstractKafkaTest() {
    @BeforeEach
    fun init() {
        testEnvironment.reset()
    }

    @Test
    fun `ignore expired dated journey message`() {
        val journey1 =
            readDateDJourney()
        adjustTime(journey1)
        val now = timeService.now()
        journey1.apply {
            this.ref = MapperUtils.randomId(prefix = "test-journey")
            this.header.expiresTimestamp = now.minusHours(3).toString()
            this.header.messageTimestamp = this.header.expiresTimestamp
        }
        pipe(journey1)
        Assertions.assertNull(journeyRepo.fetchByRef(journey1.ref))
    }

    @Test
    fun `DatedJourneyInput - NEW - journey inserted`() {
        val ref = MapperUtils.randomId(prefix = "test-journey")
        val journey1 =
            readDateDJourney()
                .apply {
                    this.ref = ref
                }
        adjustTime(journey1)
        pipe(journey1)

        assertJourneyExists(ref)
        assertJourneyInputExists(ref)
    }

    @Test
    fun `DatedJourneyInput - DELETED - journey with deleted flag must be published with deleted flag`() {
        val journey1 =
            readDateDJourney()
                .apply {
                    this.ref = MapperUtils.randomId(prefix = "test-journey")
                }
        adjustTime(journey1)
        pipe(journey1)
        val journey2 = assertNotNull(journey1.deepCopy()).apply { this.deleted = true }
        pipe(journey2)

        val storedJourney = assertJourneyRecordExists(journey1.ref).record.jsonData
        Assertions.assertNotNull(storedJourney)
        Assertions.assertEquals(true, JsonUtils.toObject(storedJourney.toString(), DTODatedJourney::class.java).deleted)
    }

    @Test
    fun `DatedJourneyInput - DELETED - should publish deleted=true even if tombstone is received before publishing`() {
        val journey1 =
            readDateDJourney()
                .apply {
                    this.ref = MapperUtils.randomId(prefix = "test-journey")
                }
        adjustTime(journey1)
        pipe(journey1)
        publishDatedJourneyRoutine.execute(timeService.now())
        val journey2 = assertNotNull(journey1.deepCopy()).apply { this.deleted = true }
        pipe(journey2)
        pipe(journey2.ref, null)
        publishDatedJourneyRoutine.execute(timeService.now())
        val records = kafka.datedJourneyProducer.getRecords()
        Assertions.assertTrue(records.size == 3)
        val first = isDeleted(records[0])
        val second = isDeleted(records[1])

        Assertions.assertFalse(first)
        Assertions.assertTrue(second)
        Assertions.assertNull(records[2].value())
    }

    private fun isDeleted(producerRecord: ProducerRecord<String?, DatedJourneyKeyV2>): Boolean =
        producerRecord
            .value()
            ?.entityData
            ?.journey
            ?.deleted == true

    @Test
    fun `delete dated journey with operating date before operational window`() {
        val journey = readDateDJourney()
        adjustTime(journey)

        pipe(journey)
        assertJourneyExists(journey.ref)

        val offset = Duration.ofDays(getJourneyConfig().minOperatingDateOffset - 1)
        journey.operatingDate =
            timeService
                .now()
                .plus(offset)
                .toLocalDate()
                .toString()

        pipe(journey)
        Assertions.assertNull(journeyRepo.fetchByRef(journey.ref))
    }

    @Test
    fun `delete dated journey with operating date after operational window`() {
        val journey = readDateDJourney()
        adjustTime(journey)

        pipe(journey)
        assertJourneyExists(journey.ref)

        val offset = Duration.ofDays(getJourneyConfig().maxOperatingDateOffset + 1)
        adjustTime(journey, offset)

        pipe(journey)
        Assertions.assertNull(journeyRepo.fetchByRef(journey.ref))
        Assertions.assertNull(journeyInputRepo.fetchByRef(journey.ref))
    }

    @Test
    fun `when receiving tombstone dated journey the db record should be deleted for existing key`() {
        val journey = readDateDJourney()
        adjustTime(journey)

        pipe(journey)
        assertJourneyExists(journey.ref)

        pipe(journey.ref, null)
        Assertions.assertNull(journeyRepo.fetchByRef(journey.ref))
    }

    private fun readDateDJourney(): PDJDatedJourney {
        val file = "dated-journey/dated-block-8119-2023-01-12-v1/journeys/journey01.json"
        return TestUtils.readDatedJourney(file)
    }

    private fun getJourneyConfig(): KafkaInputConfigProperties =
        datedJourneyConfigProperties.getKafkaInputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO)
}
