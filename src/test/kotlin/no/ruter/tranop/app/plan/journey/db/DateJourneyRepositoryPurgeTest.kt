package no.ruter.tranop.app.plan.journey.db

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleRoutine
import no.ruter.tranop.app.plan.journey.input.AbstractDatedJourneyInputTest
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaPublishRoutine
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaTombstoneRoutine
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate
import java.time.OffsetDateTime

class DateJourneyRepositoryPurgeTest : AbstractDatedJourneyInputTest() {
    @BeforeEach
    fun setUp() {
        testEnvironment.reset()
    }

    @Autowired
    lateinit var tombstoneDatedJourneyRoutine: DatedJourneyKafkaTombstoneRoutine

    @Autowired
    lateinit var publishingDatedJourneyRoutine: DatedJourneyKafkaPublishRoutine

    @Test
    fun `delete all dated journeys older than 2 days relative to time service and after they are tombstoned`() {
        val stored = storeTestDatedJourneys()
        Assertions.assertEquals(stored, journeyRepo.fetchAll().size)

        val numWeWantToGetRidOf = 3

        val future = OffsetDateTime.parse("2022-01-05T14:00+02:00")
        timeService.freeze(future)

        publishingDatedJourneyRoutine.execute(timeService.now())
        val numTombstoned = tombstoneDatedJourneyRoutine.execute(timeService.now())
        Assertions.assertEquals(numWeWantToGetRidOf, numTombstoned)

        val deleteConfig = datedJourneyLifeCycleConfig.get(AbstractLifeCycleRoutine.LC_TYPE_DELETE)
        val numDeleted = datedJourneyService.deleteTombstoneJourneysTransactional(deleteConfig.olderThan)
        Assertions.assertEquals(numWeWantToGetRidOf, numDeleted)

        val vehicleTaskId = JourneyInputUtils.DEFAULT_VEHICLE_TASK_ID
        val vehicleTask =
            journeyRepo.fetchVehicleTaskJourneys(
                vehicleTaskId = vehicleTaskId,
                operatingDate = LocalDate.parse("2022-01-04"),
            )
        Assertions.assertEquals(2, vehicleTask.size)
        Assertions.assertEquals(
            "journey4",
            vehicleTask
                .first()
                .journey.ref,
        )
        Assertions.assertEquals(
            "journey5",
            vehicleTask
                .last()
                .journey.ref,
        )
    }

    private fun storeTestDatedJourneys(): Int =
        storeDatedJourneys(
            JourneyInputUtils.createDatedJourney(
                ref = "journey1",
                operatingDate = "2022-01-01",
                startDateTime = "2022-01-01T08:00:00Z",
                endDateTime = "2022-01-01T12:00:00Z",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey2",
                operatingDate = "2022-01-02",
                startDateTime = "2022-01-02T08:00:00Z",
                endDateTime = "2022-01-02T12:00:00Z",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey3",
                operatingDate = "2022-01-03",
                startDateTime = "2022-01-03T08:00:00Z",
                endDateTime = "2022-01-03T12:00:00Z",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey4",
                operatingDate = "2022-01-04",
                startDateTime = "2022-01-04T10:00+02:00",
                endDateTime = "2022-01-04T10:55+02:00",
            ),
            JourneyInputUtils.createDatedJourney(
                ref = "journey5",
                operatingDate = "2022-01-04",
                startDateTime = "2022-01-04T11:00+02:00",
                endDateTime = "2022-01-04T12:00+02:00",
            ),
        )
}
