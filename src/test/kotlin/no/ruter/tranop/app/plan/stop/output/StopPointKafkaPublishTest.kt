package no.ruter.tranop.app.plan.stop.output

import no.ruter.plandata.journey.dated.v2.dto.model.common.stop.PDJStopPoint
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.time.Duration.Companion.seconds

class StopPointKafkaPublishTest : AbstractKafkaTest() {
    @BeforeEach
    fun reset() {
        testEnvironment.reset()
        timeService.reset()
    }

    @Test
    fun `each stop point link at input gives mapped and published DTO at output`() {
        val ref = "some-stop-point-ref"
        val stopPoint =
            PDJStopPoint()
                .withRef(
                    ref,
                ).withStopPlaceDescription("som description")

        val result = stopPointInputService.process(ref, stopPoint)
        Assertions.assertTrue(result.valid())
        Assertions.assertEquals(1, stopPointRepository.count())

        timeService.offset(30.seconds)

        publishStopPointDTORoutine.execute(timeService.now())

        val producedRecords = kafka.stopPointDTOProducer.getRecords()
        Assertions.assertEquals(1, producedRecords.size)

        // now check that publish mark works
        publishStopPointDTORoutine.execute(timeService.now())

        val producedRecords2 = kafka.stopPointDTOProducer.getRecords()
        Assertions.assertEquals(1, producedRecords2.size)

        // now change and send new revision - must be 2 records on wire
        val result2 = stopPointInputService.process(ref, stopPoint.withDescription("description"))

        Assertions.assertTrue(result2.valid())

        timeService.offset(60.seconds)

        publishStopPointDTORoutine.execute(timeService.now())

        val producedRecords3 = kafka.stopPointDTOProducer.getRecords()
        Assertions.assertEquals(2, producedRecords3.size)
    }
}
