package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.app.test.assert
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DatedJourneyExternalRefTest : AbstractKafkaTest() {
    @BeforeEach
    fun setUp() {
        testEnvironment.reset()
    }

    @Test
    fun `DatedJourney - external refs - populated on import`() {
        val ref = MapperUtils.randomId(prefix = "test-journey")
        val journey = readDateDJourney().apply { this.ref = ref }
        adjustTime(journey)
        pipe(journey)

        assertJourneyExists(ref)
        assertJourneyInputExists(ref)

        val externalRefs = journeyRepo.externalRefRepo.getExternalRefs(ref)
        Assertions.assertEquals(5, externalRefs.size)

        // Assert external ref has been recorded, and that journey can be found by looking up external ref value.
        fun assert(
            type: ExternalRefType,
            values: Collection<String>,
        ) {
            externalRefs.assert(type, values)
            for (value in values) {
                val ownerRefs = journeyRepo.externalRefRepo.findOwners(externalRefs = setOf(value))
                Assertions.assertEquals(1, ownerRefs.size)
                Assertions.assertTrue(ownerRefs.contains(ref))
            }
        }

        assert(type = ExternalRefType.LINE_ID, values = setOf("RUT:Line:0"))
        assert(type = ExternalRefType.NSR_QUAY_ID, values = setOf("NBU:Quay:301", "NSR:Quay:12462"))
        assert(
            type = ExternalRefType.STOP_POINT_REF,
            values = setOf("srcb9ebd863a5a4129b68c335ee3dfa10b", "srf5940741f6b84210ab5b6d59109658ba"),
        )
    }

    private fun readDateDJourney(): PDJDatedJourney {
        val file = "dated-journey/dated-block-8119-2023-01-12-v1/journeys/journey01.json"
        return TestUtils.readDatedJourney(file)
    }
}
