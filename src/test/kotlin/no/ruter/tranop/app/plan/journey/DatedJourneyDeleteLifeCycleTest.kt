package no.ruter.tranop.app.plan.journey

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputMapper
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.app.plan.journey.input.JourneyInputUtils
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime

class DatedJourneyDeleteLifeCycleTest : AbstractKafkaTest() {
    // TODO: refactor test

    private val journeyMapper = DatedJourneyInputMapper()
    lateinit var journeyValidator: DatedJourneyInputValidator

    @BeforeEach
    fun setUp() {
        val config = datedJourneyConfigProperties.getKafkaInputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO)
        journeyValidator =
            DatedJourneyInputValidator(
                minOperatingDateOffset = config.minOperatingDateOffset,
                maxOperatingDateOffset = config.maxOperatingDateOffset,
                timeService = timeService,
            )

        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `cleanup lifecycle purges dated journeys`() {
        val operatingDate1 = "2022-01-03"
        val operatingDate2 = "2022-01-04"
        val datedJourneyRef1 = "datedJourney1"
        val datedJourneyRef2 = "datedJourney2"

        // Create journey 1
        val journey1 =
            JourneyInputUtils.createDatedJourney(
                ref = datedJourneyRef1,
                operatingDate = operatingDate1,
                endDateTime = "${operatingDate1}T10:00:00Z",
            )
        adjustTime(journey1)
        val internalJourney1 = journeyMapper.mapDatedJourney(journey1)
        val context1 = journeyValidator.validate(journey1.ref, internalJourney1)
        datedJourneyInputService.process(context1)

        // Create journey 2
        val journey2 =
            JourneyInputUtils.createDatedJourney(
                ref = datedJourneyRef2,
                operatingDate = operatingDate2,
                endDateTime = "${operatingDate2}T15:00:00Z",
            )
        val internalJourney2 = journeyMapper.mapDatedJourney(journey2)
        val context2 = journeyValidator.validate(journey2.ref, internalJourney2)
        datedJourneyInputService.process(context2)

        // Assert journeys have been stored properly, including external refs.
        val storedJourneys = journeyRepo.fetchAll()
        Assertions.assertEquals(2, storedJourneys.size)
        val journey1ExternalRefs = journeyRepo.externalRefRepo.getExternalRefs(datedJourneyRef1)
        Assertions.assertTrue(journey1ExternalRefs.isNotEmpty())
        val journey2ExternalRefs = journeyRepo.externalRefRepo.getExternalRefs(datedJourneyRef2)
        Assertions.assertTrue(journey2ExternalRefs.isNotEmpty())

        timeService.reset()

        val future = OffsetDateTime.parse("2022-01-05T14:00+02:00")
        timeService.freeze(future)

        // as order of execution is not guaranteed we need to ensure delete routine ran after tombstoning routing
        publishDatedJourneyRoutine.execute(timeService.now())
        tombstoneDatedJourneyRoutine.execute(timeService.now())
        deleteDatedJourneyRoutine.execute(timeService.now())

        // Assert we only have one journey in database after clean-up
        val numDatedJourneys = journeyRepo.fetchAll().size
        Assertions.assertEquals(1, numDatedJourneys)
        Assertions.assertEquals(numDatedJourneys, journeyInputRepo.count())

        // Assert journey 1 has been deleted
        Assertions.assertNull(journeyRepo.fetchByRef(datedJourneyRef1))
        Assertions.assertNull(journeyInputRepo.fetchByRef(datedJourneyRef1))
        Assertions.assertEquals(0, journeyRepo.externalRefRepo.getExternalRefs(datedJourneyRef1).size)

        // Assert journey 2 has not been deleted
        Assertions.assertNotNull(journeyRepo.fetchByRef(datedJourneyRef2))
        Assertions.assertNotNull(journeyInputRepo.fetchByRef(datedJourneyRef2))

        // Assert external refs are only kept for journey 2
        val startCallQuayRefOwners = journeyRepo.externalRefRepo.findOwners(externalRefs = setOf("start-call-quay-ref"))
        Assertions.assertEquals(1, startCallQuayRefOwners.size)
        Assertions.assertTrue(startCallQuayRefOwners.contains(datedJourneyRef2))
        Assertions.assertFalse(startCallQuayRefOwners.contains(datedJourneyRef1))
    }

    @Test
    fun `ensure dated journey with deleted = true will be mapped and published as is before tombstoning`() {
        val opDate = timeService.now().toLocalDate().toString()
        val journey1 =
            JourneyInputUtils
                .createDatedJourney(
                    ref = "some-dated-journey-ref",
                    operatingDate = opDate,
                    endDateTime = "${opDate}T10:00:00Z",
                ).withDeleted(true)
        adjustTime(journey1)

        datedJourneyInputService.process(journey1.ref, journey1)
        val producedJourneys = kafka.datedJourneyProducer.getRecords()
        Assertions.assertEquals(1, producedJourneys.size)
        Assertions.assertEquals(
            true,
            producedJourneys[0]
                .value()
                .entityData.journey.deleted,
        )
    }
}
