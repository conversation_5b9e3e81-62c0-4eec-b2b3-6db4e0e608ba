package no.ruter.tranop.app.outbox

import no.ruter.tranop.app.outbox.config.OutboxCleanupProperties
import no.ruter.tranop.app.outbox.lifecycle.OutboxCleanupScheduler
import no.ruter.tranop.app.test.AbstractBaseTest
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource
import java.time.OffsetDateTime

@TestPropertySource(
    properties = [
        "app.config.outbox.cleanup.enabled=true",
    ],
)
class OutboxCleanupSchedulerIntegrationTest : AbstractBaseTest() {
    @Autowired lateinit var scheduler: OutboxCleanupScheduler

    @Autowired lateinit var config: OutboxCleanupProperties

    @Test
    fun `scheduler deletes old processed events`() {
        val oldTime = OffsetDateTime.now().minusDays(config.retentionDays + 1L)
        val record =
            outboxRepository.newRecord(
                Outbox(
                    ref = "old-event",
                    dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                    targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                    payloadRef = "payload-ref",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(record)
        outboxRepository.markPublished(listOf("old-event"), oldTime)
        val eventsBefore = outboxRepository.fetchAll()
        assertTrue(eventsBefore.any { it.data.ref == "old-event" })

        scheduler.dailyCleanup()

        val events = outboxRepository.fetchAll()
        assertTrue(events.none { it.data.ref == "old-event" })
    }
}
