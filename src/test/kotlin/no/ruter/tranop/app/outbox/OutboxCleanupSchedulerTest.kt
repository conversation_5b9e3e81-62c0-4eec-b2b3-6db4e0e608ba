package no.ruter.tranop.app.outbox

import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.config.OutboxCleanupProperties
import no.ruter.tranop.app.outbox.lifecycle.OutboxCleanupScheduler
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import java.time.OffsetDateTime

class OutboxCleanupSchedulerTest {
    private val outboxService = mock<OutboxService>()
    private val config = OutboxCleanupProperties().apply { retentionDays = 10 }
    private val fixedNow = OffsetDateTime.parse("2023-01-01T00:00:00Z")
    private val timeService = mock<TimeService> { on { now() } doReturn fixedNow }
    private val scheduler = OutboxCleanupScheduler(outboxService, config, timeService)

    @Test
    fun `dailyCleanup calls deleteOldProcessedEvents with correct time`() {
        scheduler.dailyCleanup()
        verify(outboxService).deleteOldProcessedEvents(fixedNow.minusDays(config.retentionDays))
    }
}
