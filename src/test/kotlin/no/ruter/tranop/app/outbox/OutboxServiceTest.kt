package no.ruter.tranop.app.outbox

import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder.Pagination
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.OffsetDateTime

class OutboxServiceTest {
    private val repository = mock<OutboxRepository> { }
    private val service = OutboxService(repository, TimeService())

    @Test
    fun `findUnpublished delegates to repository`() {
        val pagination = mock<Pagination>()
        service.findUnpublished(
            dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
            targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
            pagination = pagination,
        )
        verify(repository).findUnpublished(
            eq(DBOutboxDataType.DATED_JOURNEY_EVENT),
            eq(DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1),
            eq(pagination),
            any(),
            any(),
        )
    }

    @Test
    fun `markAsPublished with empty list returns 0`() {
        assert(service.markAsPublished(emptyList()) == 0)
    }

    @Test
    fun `markAsPublished delegates to repository`() {
        val refs = listOf("ref1", "ref2")
        whenever(repository.markPublished(eq(refs), any())).doReturn(2)
        assert(service.markAsPublished(refs) == 2)
    }

    @Test
    fun `deleteOldProcessedEvents delegates to repository`() {
        val time = OffsetDateTime.now()
        service.deleteOldProcessedEvents(time)
        verify(repository).deleteOldProcessedEvents(time)
    }
}
