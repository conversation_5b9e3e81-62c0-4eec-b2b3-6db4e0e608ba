package no.ruter.tranop.app.common.dataflow.opensearch

import org.apache.http.HttpHost
import org.opensearch.client.RestClient
import org.opensearch.client.json.jackson.JacksonJsonpMapper
import org.opensearch.client.transport.OpenSearchTransport
import org.opensearch.client.transport.rest_client.RestClientTransport
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary

@TestConfiguration
class TestOpenSearchConfig {
    @Primary
    @Bean
    fun configureTestTransport(): OpenSearchTransport {
        val host = HttpHost("localhost", 9200, "http")
        val restClient = RestClient.builder(host).build()
        return RestClientTransport(restClient, JacksonJsonpMapper())
    }
}
