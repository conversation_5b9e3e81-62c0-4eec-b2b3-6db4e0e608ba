package no.ruter.tranop.app.common.time

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.plan.journey.input.firstDeparture
import no.ruter.tranop.assignment.util.toLocalDate
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.springframework.boot.test.context.TestComponent
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.time.toJavaDuration

/**
 * Implementation of TimeService for use in tests.
 *
 * Allows clients to offset the clock from current time during tests.
 */
@TestComponent
class TestTimeService : TimeService() {
    private val defaultClock = Clock.systemDefaultZone()
    private val defaultOffset = Duration.ofSeconds(0L)

    private var clock: Clock = defaultClock
    private var offset: Duration = defaultOffset

    override fun now(): OffsetDateTime = OffsetDateTime.now(clock)

    fun reset(): TestTimeService {
        clock = defaultClock
        offset = defaultOffset
        return this
    }

    fun freeze(): TestTimeService {
        clock = Clock.fixed(clock.instant(), clock.zone)
        return this
    }

    fun freeze(now: OffsetDateTime): TestTimeService {
        clock = Clock.fixed(now.toInstant(), now.toZonedDateTime().zone)
        return this
    }

    fun offset(
        duration: kotlin.time.Duration,
        relative: Boolean = false,
    ): TestTimeService = offset(duration.toJavaDuration(), relative)

    fun offset(
        duration: Duration,
        relative: Boolean = false,
    ): TestTimeService {
        val comparisonClock = if (relative) clock else defaultClock
        clock = Clock.offset(comparisonClock, duration)
        offset = duration
        return this
    }

    fun offset(
        now: OffsetDateTime?,
        relative: Boolean = false,
    ): OffsetDateTime {
        if (now != null) {
            this.offset(Duration.between(OffsetDateTime.now(defaultClock), now), relative)
        }
        return this.now()
    }

    fun offset(seconds: Long): TestTimeService = offset(Duration.ofSeconds(seconds))

    fun freezeOn(dj: PDJDatedJourney) {
        freeze(dj.header?.messageTimestamp?.toOffsetDateTime()!!)
    }

    fun getZoneOffset(): ZoneOffset {
        val currentInstant: Instant = clock.instant()
        return clock.zone.rules.getOffset(currentInstant)
    }

    fun offset(datedJourney: PDJDatedJourney) {
        offset(datedJourney.firstDeparture().toOffsetDateTime(), relative = false)
    }

    fun adjustJourneyTime(
        journey: PDJDatedJourney,
        offset: Duration? = null,
    ) {
        var now =
            journey.operatingDate
                ?.toLocalDate()
                ?.atStartOfDay()
                ?.atOffset(ZoneOffset.UTC)
                ?.plusHours(3)
        if (now != null) {
            if (offset != null) {
                now = now.plus(offset)
            }
            journey.header.messageTimestamp = now.toString()
            journey.header.expiresTimestamp = now?.plusDays(1).toString()

            offset(now)
        }
    }

    fun adjustJourneyTime(
        journey: DTODatedJourney,
        offset: Duration? = null,
    ) {
        var now =
            journey.operatingDate
                ?.toLocalDate()
                ?.atStartOfDay()
                ?.atOffset(ZoneOffset.UTC)
                ?.plusHours(3)
        if (now != null) {
            if (offset != null) {
                now = now.plus(offset)
            }
            journey.header.messageTimestamp = now.toString()
            journey.header.expiresTimestamp = now?.plusDays(1).toString()

            offset(now)
        }
    }
}
