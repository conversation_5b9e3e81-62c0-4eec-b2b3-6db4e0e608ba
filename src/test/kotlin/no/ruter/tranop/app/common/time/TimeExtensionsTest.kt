package no.ruter.tranop.app.common.time

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class TimeExtensionsTest {
    @Test
    fun `return initial delay when retries is one`() {
        val initialDelay = 500L
        val result = delayMillis(retryNumber = 1, initialDelay = initialDelay)
        Assertions.assertEquals(initialDelay, result)
    }

    @Test
    fun `return max delay when retries is very high`() {
        val maxDelay = 10000L
        val result = delayMillis(retryNumber = 100000, maxDelay = maxDelay)
        Assertions.assertEquals(maxDelay, result)
    }

    @Test
    fun `increase the initial delay by the given factor retryNumber - 1 times`() {
        val result =
            delayMillis(
                retryNumber = 3,
                initialDelay = 200L,
                maxDelay = 10000L,
                factor = 3.0,
            )
        Assertions.assertEquals(1800L, result)
    }
}
