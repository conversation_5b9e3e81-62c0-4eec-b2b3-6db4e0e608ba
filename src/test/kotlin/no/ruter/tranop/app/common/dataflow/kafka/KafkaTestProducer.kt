package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.rdp.logging.LoggerFactory
import org.apache.kafka.clients.consumer.ConsumerGroupMetadata
import org.apache.kafka.clients.consumer.OffsetAndMetadata
import org.apache.kafka.clients.producer.Callback
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.RecordMetadata
import org.apache.kafka.common.Metric
import org.apache.kafka.common.MetricName
import org.apache.kafka.common.PartitionInfo
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.Uuid
import java.time.Duration
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

open class KafkaTestProducer<K, V> : Producer<K, V> {
    private val log = LoggerFactory.getLogger(javaClass.canonicalName)

    private val records = ArrayList<ProducerRecord<K, V>>()

    fun getRecordValues() = records.map { it.value() }

    fun getRecords() = records.toList()

    private var simulatedException: Exception? = null

    fun clear() {
        records.clear()
        simulatedException = null
    }

    override fun close() {}

    override fun close(timeout: Duration?) {}

    override fun flush() {}

    override fun metrics(): MutableMap<MetricName, out Metric> = HashMap()

    override fun clientInstanceId(timeout: Duration?): Uuid = Uuid.randomUuid()

    override fun partitionsFor(topic: String?): MutableList<PartitionInfo> = ArrayList()

    override fun initTransactions() {}

    override fun beginTransaction() {}

    override fun abortTransaction() {}

    override fun commitTransaction() {}

    @Deprecated("deprecated in lib")
    override fun sendOffsetsToTransaction(
        offsets: MutableMap<TopicPartition, OffsetAndMetadata>?,
        consumerGroupId: String?,
    ) {
    }

    override fun sendOffsetsToTransaction(
        offsets: MutableMap<TopicPartition, OffsetAndMetadata>?,
        groupMetadata: ConsumerGroupMetadata?,
    ) {
    }

    override fun send(record: ProducerRecord<K, V>?): Future<RecordMetadata> {
        val mockMetadata = getMockMetadata(record)
        if (simulatedException == null) {
            record?.let { appendRecord(it) }
        } else {
            throw Exception(simulatedException)
        }
        return ImmediateFuture(mockMetadata)
    }

    override fun send(
        record: ProducerRecord<K, V>?,
        callback: Callback?,
    ): Future<RecordMetadata> {
        val mockMetadata = getMockMetadata(record)

        if (simulatedException == null) {
            record?.let { appendRecord(it) }
        }

        callback?.onCompletion(mockMetadata, simulatedException)

        return ImmediateFuture(mockMetadata)
    }

    private fun getMockMetadata(record: ProducerRecord<K, V>?) =
        RecordMetadata(
            TopicPartition(record?.topic(), 0),
            // Not accurate, as others may have piped input to topic, but at least it changes...
            records.size.toLong(),
            0,
            record?.timestamp() ?: -1L,
            -1,
            -1,
        )

    fun causeSimulatedException(exception: Exception) {
        simulatedException = exception
    }

    class ImmediateFuture<T>(
        private val value: T,
    ) : Future<T> {
        override fun get() = value

        override fun get(
            timeout: Long,
            unit: TimeUnit,
        ) = value

        override fun isDone() = true

        override fun cancel(mayInterruptIfRunning: Boolean) = false

        override fun isCancelled() = false
    }

    protected open fun appendRecord(record: ProducerRecord<K, V>) {
        records.add(record)
    }
}
