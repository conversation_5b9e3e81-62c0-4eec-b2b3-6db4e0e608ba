package no.ruter.tranop.app.common.dataflow.snowflake

import net.snowflake.ingest.streaming.SnowflakeStreamingIngestClient

class SnowflakeTestClientFactory : SnowflakeClientFactory() {
    val clients = LinkedHashMap<String, SnowflakeTestClient>()

    /** Reset all clients by clearing all channels. **/
    fun reset() {
        clients.values.forEach(SnowflakeTestClient::reset)
    }

    override fun createClient(
        name: String,
        config: SnowflakeClientProperties,
    ): SnowflakeStreamingIngestClient {
        val client = SnowflakeTestClient(name)
        clients[name] = client
        return client
    }

    fun channel(
        clientName: String,
        channelName: String,
    ): SnowflakeTestChannel {
        val client = clients[clientName] ?: throw IllegalArgumentException("client not found: $clientName")
        val prefix = "$channelName-"
        for ((key, value) in client.channels.entries) {
            if (key.startsWith(prefix)) {
                return value
            }
        }
        throw IllegalArgumentException("channel not found: $channelName")
    }
}
