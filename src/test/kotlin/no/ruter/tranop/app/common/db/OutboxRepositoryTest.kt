package no.ruter.tranop.app.common.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.app.test.AbstractBaseTest
import no.ruter.tranop.journey.event.bi.model.BIJourneyEvent
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import kotlin.test.assertNull

class OutboxRepositoryTest : AbstractBaseTest() {
    @BeforeEach
    fun setUp() {
        timeService.reset()
        testEnvironment.reset()
    }

    @Test
    fun `insert and fetch record in db`() {
        val initialEvents = outboxRepository.fetchAll()

        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertEquals(0, initialEvents.size)
        assertEquals(1, storedEvents.size)
        assertEquals(record.data.ref, storedEvents[0].data.ref)
        assertEquals(record.data.dataType, storedEvents[0].data.dataType)
        assertEquals(record.data.payloadRef, storedEvents[0].data.payloadRef)
        assertEquals(record.data.payload, storedEvents[0].data.payload)
    }

    @Test
    fun `insert and fetch record in db and mark as published`() {
        val initialEvents = outboxRepository.fetchAll()
        val outboxRef = MapperUtils.randomId(prefix = "outbox-")
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = outboxRef,
                    payloadRef = "payload-ref",
                    payload =
                        JsonUtils.toJson(
                            BIJourneyEvent(
                                "assignment-123",
                                timeService.now().toString(),
                                "dated-journey-456",
                                emptyList(),
                                "journey-payload-ref",
                                "journey-payload-type",
                            ),
                        ),
                ),
            )
        outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertEquals(0, initialEvents.size)
        assertEquals(1, storedEvents.size)
        assertEquals(record.data.ref, storedEvents[0].data.ref)
        assertEquals(record.data.dataType, storedEvents[0].data.dataType)
        assertEquals(record.data.payloadRef, storedEvents[0].data.payloadRef)

        outboxRepository
            .findUnpublished(
                dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
            ).let { assertEquals(1, it.size) }

        val updated = outboxRepository.markPublished(listOf(outboxRef), timeService.now())
        assertEquals(1, updated)

        val storedEventsAfterPublished = outboxRepository.fetchAll()
        assertEquals(1, storedEventsAfterPublished.size)
        assertNotNull(storedEventsAfterPublished[0].record.publishedAt)

        outboxRepository
            .findUnpublished(
                dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
            ).let { assertEquals(0, it.size) }
    }

    @Test
    fun `insert and fetch record in db and mark as failed`() {
        val initialEvents = outboxRepository.fetchAll()
        val outboxRef = MapperUtils.randomId(prefix = "outbox-")
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = outboxRef,
                    payloadRef = "payload-ref",
                    payload =
                        JsonUtils.toJson(
                            BIJourneyEvent(
                                "assignment-123",
                                timeService.now().toString(),
                                "dated-journey-456",
                                emptyList(),
                                "journey-payload-ref",
                                "journey-payload-type",
                            ),
                        ),
                ),
            )
        outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertEquals(0, initialEvents.size)
        assertEquals(1, storedEvents.size)
        assertEquals(record.data.ref, storedEvents[0].data.ref)
        assertEquals(record.data.dataType, storedEvents[0].data.dataType)
        assertEquals(record.data.payloadRef, storedEvents[0].data.payloadRef)

        outboxRepository
            .findUnpublished(
                dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
            ).let { assertEquals(1, it.size) }

        outboxRepository.markAsFailed(storedEvents, timeService.now().minusMinutes(10))

        outboxRepository
            .findUnpublished(
                dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
            ).let { assertEquals(1, it.size) }

        val storedEventsAfterPublished = outboxRepository.fetchAll()
        assertEquals(1, storedEventsAfterPublished.size)
        assertEquals(null, storedEventsAfterPublished[0].record.publishedAt)
        assertEquals(1, storedEventsAfterPublished[0].record.retries)
        assertNotNull(storedEventsAfterPublished[0].record.nextAttemptAt)

        outboxRepository.markAsFailed(storedEvents, timeService.now().minusMinutes(4))
        val storedEventsAfterPublishedAgain = outboxRepository.fetchAll()
        assertEquals(1, storedEventsAfterPublishedAgain.size)
        assertEquals(null, storedEventsAfterPublishedAgain[0].record.publishedAt)
        assertEquals(2, storedEventsAfterPublishedAgain[0].record.retries)
        assertNotNull(storedEventsAfterPublishedAgain[0].record.nextAttemptAt)

        outboxRepository
            .findUnpublished(
                dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
            ).let { assertEquals(1, it.size) }
    }

    @Test
    fun `insert record with minimal payload`() {
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref",
                    payload = "{}",
                ),
            )

        val result = outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertTrue(result)
        assertEquals(1, storedEvents.size)
        assertEquals("{}", storedEvents[0].data.payload)
    }

    @Test
    fun `insert record with large payload`() {
        val largePayload = "{\"data\": \"" + "x".repeat(9900) + "\"}" // ~10KB valid JSON payload
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref",
                    payload = largePayload,
                ),
            )

        val result = outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertTrue(result)
        assertEquals(1, storedEvents.size)
        assertEquals(largePayload, storedEvents[0].data.payload)
    }

    @Test
    fun `insert record with special characters in payload`() {
        val specialPayload =
            """{"message": "Special chars: åæø, 中文, emoji: 🚌🚍, quotes: \"test\", newlines: \n\r"}"""
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref",
                    payload = specialPayload,
                ),
            )

        val result = outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertTrue(result)
        assertEquals(1, storedEvents.size)
        assertEquals(specialPayload, storedEvents[0].data.payload)
    }

    @Test
    fun `insert record with undefined event type`() {
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    dataType = DBOutboxDataType.of("custom-value"),
                    targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                    payloadRef = "payload-ref",
                    payload = "{}",
                ),
            )

        val result = outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertTrue(result)
        assertEquals(1, storedEvents.size)
        assertEquals("custom-value", storedEvents[0].data.dataType.value)
    }

    @Test
    fun `insert record with empty event ref`() {
        val record =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "",
                    payload = "{}",
                ),
            )

        val result = outboxRepository.storeRecord(data = record)
        val storedEvents = outboxRepository.fetchAll()

        assertTrue(result)
        assertEquals(1, storedEvents.size)
        assertEquals("", storedEvents[0].data.payloadRef)
    }

    @Test
    fun `insert multiple records with same payload ref`() {
        val payloadRef = "duplicate-payload-ref"

        val record1 =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = payloadRef,
                    payload = "{\"id\": 1}",
                ),
            )

        val record2 =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = payloadRef,
                    payload = "{\"id\": 2}",
                ),
            )

        outboxRepository.storeRecord(data = record1)
        outboxRepository.storeRecord(data = record2)
        val storedEvents = outboxRepository.fetchAll()

        assertEquals(2, storedEvents.size)
        assertTrue(storedEvents.all { it.data.payloadRef == payloadRef })
        assertTrue(storedEvents.all { it.data.dataType == DBOutboxDataType.DATED_JOURNEY_EVENT })
        assertTrue(storedEvents.all { it.data.targetType == DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1 })
    }

    @Test
    fun `fetch all returns empty list when no records exist`() {
        val events = outboxRepository.fetchAll()
        assertEquals(0, events.size)
        assertNotNull(events)
    }

    @Test
    fun `findUnpublished does not return events that exceeded retry count`() {
        val refBelow = MapperUtils.randomId(prefix = "outbox-")
        val recordBelow =
            outboxRepository.newRecord(
                outbox(
                    ref = refBelow,
                    payloadRef = "payload-ref-below",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(recordBelow)
        repeat(2) { outboxRepository.markAsFailed(outboxRepository.fetchAll(), timeService.now().minusDays(1)) }

        // Insert an event with retries above the limit
        val refAbove = MapperUtils.randomId(prefix = "outbox-")
        val recordAbove =
            outboxRepository.newRecord(
                outbox(
                    ref = refAbove,
                    payloadRef = "payload-ref-above",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(recordAbove)
        // Mark as failed 4 times (exceeds retryCount = 3)
        repeat(4) {
            outboxRepository.markAsFailed(
                outboxRepository.fetchAll().filter { it ->
                    it.record.ref == refAbove
                },
                timeService.now().minusDays(1),
            )
        }

        val unpublished =
            outboxRepository.findUnpublished(
                dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                pagination = AbstractQueryBuilder.Pagination(10, 0),
                retryCount = 3,
            )

        assertTrue(unpublished.any { it.data.ref == refBelow })
        assertTrue(unpublished.none { it.data.ref == refAbove })
    }

    @Test
    fun `markAsFailed should set next attempt timestamp according to backoff strategy`() {
        // 1 retry: 2 minutes
        // 2 retries: 4 minutes
        // 3 retries: 8 minutes
        // ....
        val ref = MapperUtils.randomId(prefix = "outbox-")
        val outbox =
            outboxRepository.newRecord(
                outbox(
                    ref = ref,
                    payloadRef = "payload-ref-below",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(outbox)
        val list = outboxRepository.fetchAll()
        assertEquals(0, list[0].record.retries)
        assertNull(list[0].record.nextAttemptAt)

        outboxRepository.markAsFailed(list)
        val listAfterMarkAsFailed = outboxRepository.fetchAll()
        assertEquals(1, listAfterMarkAsFailed.size)
        assertEquals(ref, listAfterMarkAsFailed[0].data.ref)
        assertEquals(1, listAfterMarkAsFailed[0].record.retries)
        assertTrue(
            isBetween(
                listAfterMarkAsFailed[0].record.nextAttemptAt,
                timeService.now().plusSeconds(1),
                timeService.now().plusMinutes(3),
            ),
        )

        outboxRepository.markAsFailed(listAfterMarkAsFailed)
        val listAfterMark2 = outboxRepository.fetchAll()
        assertEquals(2, listAfterMark2[0].record.retries)
        assertTrue(
            isBetween(
                listAfterMark2[0].record.nextAttemptAt,
                timeService.now().plusMinutes(3),
                timeService.now().plusMinutes(5),
            ),
        )

        outboxRepository.markAsFailed(listAfterMark2)
        val listAfterMark3 = outboxRepository.fetchAll()
        assertEquals(3, listAfterMark3[0].record.retries)
        assertTrue(
            isBetween(
                listAfterMark3[0].record.nextAttemptAt,
                timeService.now().plusMinutes(7),
                timeService.now().plusMinutes(9),
            ),
        )

        outboxRepository.markAsFailed(listAfterMark3)
        val listAfterMark4 = outboxRepository.fetchAll()
        assertEquals(4, listAfterMark4[0].record.retries)
        assertTrue(
            isBetween(
                listAfterMark4[0].record.nextAttemptAt,
                timeService.now().plusMinutes(15),
                timeService.now().plusMinutes(17),
            ),
        )
    }

    @Test
    fun `findUnpublished should return records ready for publication`() {
        val outbox1 =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref-below",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(outbox1)
        val outbox2 =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref-below",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(outbox2)
        val outbox3 =
            outboxRepository.newRecord(
                outbox(
                    ref = MapperUtils.randomId(prefix = "outbox-"),
                    payloadRef = "payload-ref-below",
                    payload = "{}",
                ),
            )
        outboxRepository.storeRecord(outbox3)

        val all = outboxRepository.fetchAll()
        assertEquals(3, all.size)

        assertEquals(
            3,
            outboxRepository
                .findUnpublished(
                    DBOutboxDataType.DATED_JOURNEY_EVENT,
                    DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                    AbstractQueryBuilder.Pagination(10, 0),
                    3,
                ).size,
        )

        outboxRepository.markPublished(listOf(all.first().record.ref), timeService.now())

        outboxRepository
            .findUnpublished(
                DBOutboxDataType.DATED_JOURNEY_EVENT,
                DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                AbstractQueryBuilder.Pagination(10, 0),
                3,
            ).let { unpublished ->
                assertEquals(2, unpublished.size)
            }

        outboxRepository.markAsFailed(listOf(all.last()), timeService.now())
        assertEquals(
            1,
            outboxRepository
                .findUnpublished(
                    DBOutboxDataType.DATED_JOURNEY_EVENT,
                    DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                    AbstractQueryBuilder.Pagination(10, 0),
                    3,
                ).size,
        )
    }

    private fun isBetween(
        target: OffsetDateTime,
        start: OffsetDateTime,
        end: OffsetDateTime,
    ): Boolean = target.isAfter(start) && target.isBefore(end)

    private fun outbox(
        ref: String,
        payload: String,
        payloadRef: String,
        dataType: DBOutboxDataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
        targetType: DBOutboxTargetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
    ): Outbox =
        Outbox(
            ref = ref,
            dataType = dataType,
            targetType = targetType,
            payloadRef = payloadRef,
            payload = payload,
        )
}
