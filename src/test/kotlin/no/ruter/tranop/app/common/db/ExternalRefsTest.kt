package no.ruter.tranop.app.common.db

import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import org.junit.jupiter.api.Assertions
import kotlin.test.Test

class ExternalRefsTest {
    @Test
    fun testExternalRefs() {
        val refs = ExternalRefs()
        Assertions.assertTrue(refs.isEmpty())
        Assertions.assertEquals(0, refs.size)
        Assertions.assertEquals(0, refs.all().size)
        Assertions.assertFalse(refs.isNotEmpty())

        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-1")
        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-1")
        refs.add(ExternalRefType.NSR_QUAY_ID, "quay-2")
        Assertions.assertEquals(2, refs.size) // duplicate "quay-1" should not count
        Assertions.assertFalse(refs.isEmpty())
        Assertions.assertTrue(refs.isNotEmpty())

        refs.add(ExternalRefType.LINE_ID, "line-1")
        refs.add(ExternalRefType.LINE_ID, "line-2")
        refs.add(ExternalRefType.LINE_ID, "line-1")
        Assertions.assertEquals(4, refs.size) // duplicate "line-1" should not count

        val all = refs.all()
        Assertions.assertEquals(2, all.keys.size)
        Assertions.assertEquals(2, all[ExternalRefType.LINE_ID]?.size)
        Assertions.assertEquals(2, all[ExternalRefType.NSR_QUAY_ID]?.size)
    }
}
