package no.ruter.tranop.app.common.dataflow.snowflake

import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary

@TestConfiguration
class SnowflakeTestConfig {
    companion object {
        val DEFAULT_CLIENT_PROPERTIES =
            SnowflakeClientProperties(
                enabled = true,
                user = "test",
                url = "https://RUTERAS-TEMP.snowflakecomputing.com",
                dbName = "asd",
                schemaName = "dasda",
                tableName = "dasda",
                privateKey = "aa",
            )
    }

    @Bean
    @Primary
    fun snowflakeClientFactory(): SnowflakeTestClientFactory = SnowflakeTestClientFactory()
}
