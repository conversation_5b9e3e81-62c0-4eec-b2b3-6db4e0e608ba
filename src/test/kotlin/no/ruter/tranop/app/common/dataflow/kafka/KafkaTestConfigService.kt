package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopicBinding
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConfig
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaInputTopics
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaOutputTopics
import org.apache.kafka.common.serialization.Serde

class KafkaTestConfigService(
    serdes: KafkaSerdeProvider,
    kafkaConfig: KafkaConfig,
    inputTopics: KafkaInputTopics,
    outputTopics: KafkaOutputTopics,
) : KafkaConfigService(
        serdes,
        kafkaConfig,
        inputTopics,
        outputTopics,
    ) {
    override fun <V, VS : Serde<V>> makeProducerBinding(
        topicBinding: KafkaTopicBinding<String?, Serde<String?>, V, VS>,
    ): KafkaProducerBinding<String?, Serde<String?>, V, VS> {
        val producer = KafkaTestProducer<String?, V>()
        return KafkaProducerBinding(topicBinding, producer)
    }
}
