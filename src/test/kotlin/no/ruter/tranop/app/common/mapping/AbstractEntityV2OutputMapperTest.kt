package no.ruter.tranop.app.common.mapping

import no.ruter.tranop.app.common.dataflow.kafka.entity.AbstractEntityV2OutputMapper
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageOriginatingFrom
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageType
import no.ruter.tranop.dated.journey.dto.model.common.DTOStopPointBehaviourType
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportModeProperty
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLinkType
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOTrafficPriorityTriggerCode
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class AbstractEntityV2OutputMapperTest {
    @Test
    fun `Mapping - Completeness`() {
        Assertions.assertThat(AbstractEntityV2OutputMapper.LINK_TYPES.keys).containsAll(DTOStopPointLinkType.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.EVENT_TYPES.keys).containsAll(DTOEventType.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.EVENT_METADATA_KEY_TYPES.keys).containsAll(DTOEventMetadataKeyType.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.TRIGGER_CODES.keys).containsAll(DTOTrafficPriorityTriggerCode.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.STOP_POINT_BEHAVIOUR_TYPE.keys).containsAll(DTOStopPointBehaviourType.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.LINEAGE_TYPE.keys).containsAll(DTOLineageType.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.LINEAGE_SOURCE_TYPE.keys).containsAll(DTOLineageOriginatingFrom.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.TRANSPORT_MODES.keys).containsAll(DTOTransportMode.ALL)
        Assertions.assertThat(AbstractEntityV2OutputMapper.TRANSPORT_MODE_PROPERTIES.keys).containsAll(DTOTransportModeProperty.ALL)
    }
}
