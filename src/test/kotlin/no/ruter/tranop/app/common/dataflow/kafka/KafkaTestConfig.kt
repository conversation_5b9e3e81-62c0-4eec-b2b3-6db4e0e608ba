package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConfig
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaInputTopics
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaOutputTopics
import org.apache.kafka.common.serialization.Serdes
import org.apache.kafka.streams.StreamsConfig
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import java.nio.file.Files

@TestConfiguration
class KafkaTestConfig {
    @Bean
    @Primary
    fun kafkaConfigService(
        inputTopics: KafkaInputTopics,
        outputTopics: KafkaOutputTopics,
        serdeProvider: KafkaSerdeProvider,
        kafkaConfig: KafkaConfig,
    ): KafkaTestConfigService {
        // Override Kafka configuration with test values.
        val appName = "appInfo.name"
        val conf = LinkedHashMap(kafkaConfig.configuration)
        val stateDir = Files.createTempDirectory(appName).toAbsolutePath().toString()
        conf[StreamsConfig.STATE_DIR_CONFIG] = stateDir
        conf[StreamsConfig.APPLICATION_ID_CONFIG] = "$appName-test"
        conf[StreamsConfig.BOOTSTRAP_SERVERS_CONFIG] = "invalid bootstrap server"

        conf["default.key.serde"] = Serdes.StringSerde::class.java.name
        conf["default.value.serde"] = Serdes.StringSerde::class.java.name
        conf["schema.registry.url"] = "https://invalid.schema.registry.url"

        // Apply updated Kafka configuration.
        kafkaConfig.configuration = conf
        return KafkaTestConfigService(
            serdeProvider,
            kafkaConfig,
            inputTopics,
            outputTopics,
        )
    }

    @Bean
    @Primary
    fun kafkaTestEnvironment(kafkaConfigService: KafkaConfigService): KafkaTestEnvironment = KafkaTestEnvironment(kafkaConfigService)
}
