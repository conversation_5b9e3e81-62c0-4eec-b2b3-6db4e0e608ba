package no.ruter.tranop.app.common.dataflow.kafka

class KafkaTestEnvironment(
    configService: KafkaConfigService,
) {
    val datedJourneyProducer = configService.datedJourneyOutputProducer.producer as KafkaTestProducer

    val stopPointProducer = configService.stopPointOutputProducer.producer as KafkaTestProducer

    val stopPointDTOProducer = configService.stopPointDtoOutputProducer.producer as KafkaTestProducer

    val stopPointLinkProducer = configService.stopPointLinkOutputProducer.producer as KafkaTestProducer

    val stopPointLinkDTOProducer = configService.stopPointLinkDtoOutputProducer.producer as KafkaTestProducer

    val assignmentJourneyProducer = configService.assignmentJourneyOutputProducer.producer as KafkaTestProducer

    private val outputProducers =
        listOf(
            datedJourneyProducer,
            stopPointProducer,
            stopPointDTOProducer,
            stopPointLinkProducer,
            stopPointLinkDTOProducer,
            assignmentJourneyProducer,
        )

    fun cleanOutputTopics() {
        outputProducers.forEach { it.clear() }
    }
}
