package no.ruter.tranop.app.test

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import java.io.BufferedReader
import java.io.File
import kotlin.test.assertNotNull

class TestUtils private constructor() {
    companion object {
        fun readReasonCodes(filePath: String): String {
            val json = readFile(filename = filePath)
            return assertNotNull(json, message = "Reason codes are null [$filePath]")
        }

        fun readTrafficEvent(
            filePath: String,
            update: (DTOTrafficEvent) -> Unit = {},
        ): DTOTrafficEvent {
            val json = readFile(filename = filePath)
            val attempt = JsonUtils.toObject(json, DTOTrafficEvent::class.java)
            return assertNotNull(attempt, message = "DTOTrafficEvent is null [$filePath]").apply(update)
        }

        fun readDatedJourney(
            filePath: String,
            update: (PDJDatedJourney) -> Unit = {},
        ): PDJDatedJourney {
            val json = readFile(filename = filePath)
            val datedJourney = JsonUtils.toObject(json, PDJDatedJourney::class.java)
            return assertNotNull(datedJourney, message = "Dated Journey is null [$filePath]").apply(update)
        }

        fun readFile(filename: String): String =
            Thread.currentThread().contextClassLoader.getResourceAsStream(filename)?.use {
                it.bufferedReader().use(BufferedReader::readText)
            } ?: throw IllegalStateException("File not found ['$filename']?")

        fun readAllFiles(
            folderPath: String,
            suffix: String = "json",
        ): List<String> {
            val contents = mutableListOf<String>()
            Thread.currentThread().contextClassLoader.getResource(folderPath)?.let { path ->
                File(path.file).walk().forEach { file ->
                    if (file.isFile && file.path.endsWith(suffix)) {
                        contents.add(file.bufferedReader().use(BufferedReader::readText))
                    }
                }
            } ?: throw IllegalStateException("Folder not found: $folderPath")
            return contents
        }
    }
}
