package no.ruter.tranop.app.test

import io.micrometer.core.instrument.Measurement
import io.micrometer.core.instrument.Meter
import no.ruter.plandata.journey.dated.v2.dto.model.common.stop.PDJStopPoint
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import org.junit.jupiter.api.Assertions
import kotlin.test.assertNotNull

fun <E> List<E>?.assertSize(
    message: String? = null,
    size: Int,
): List<E> {
    if (this == null) {
        throw AssertionError("Expected: not null, actual: null [$message]")
    }

    if (this.size != size) {
        throw AssertionError("Expected size: {$size} , actual: {${this.size}}} [$message]")
    }
    return this
}

fun List<Pair<Meter, MutableIterable<Measurement>>>.prettyList(): List<String> =
    this
        .map { pair ->
            val meter = pair.first.id
            val tags = meter.tags.associate { it.key to it.value }
            val measurement = pair.second
            val keyParts =
                mutableListOf(
                    tags["dataType"],
                    tags["insightType"],
                    tags["errorType"],
                    tags["topic"],
                ).filterNotNull()

            val tagKey = keyParts.joinToString(": ")
            "${meter.name}: $tagKey: ${measurement.firstOrNull()?.value} "
        }.sorted()

fun ExternalRefs.assert(
    type: ExternalRefType,
    values: Collection<String>,
) {
    val refs = get(type)
    Assertions.assertEquals(values.size, refs.size)

    for (value in values) {
        if (!refs.contains(value)) {
            throw AssertionError("Missing external ref ${type.value} value: $value")
        }
    }
}

fun PDJDatedJourney.assertStopPoint(stopPointRef: String): PDJStopPoint {
    val stops = assertNotNull(assertNotNull(plan).stops)
    for (stop in stops) {
        if (stopPointRef == stop.ref) return stop
    }
    throw AssertionError("stop point [$stopPointRef] not found in journey [$ref]!")
}
