package no.ruter.tranop.app.test

import com.fasterxml.jackson.databind.ObjectMapper
import io.restassured.RestAssured
import io.restassured.builder.RequestSpecBuilder
import io.restassured.http.ContentType
import io.restassured.http.Method
import io.restassured.module.kotlin.extensions.Given
import io.restassured.module.kotlin.extensions.Then
import io.restassured.module.kotlin.extensions.When
import io.restassured.response.ExtractableResponse
import io.restassured.response.Response
import io.restassured.specification.RequestSpecification
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaTest
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeAll
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.RequestMapping
import kotlin.test.assertNotNull

abstract class AbstractApiTest : AbstractKafkaTest() {
    companion object {
        fun getRequestMappingValue(annotations: List<Annotation>): String =
            (annotations.first { it is RequestMapping } as RequestMapping).value.first()
    }

    @LocalServerPort
    protected var localServerPort = -1

    protected val apiUsername = "admin"
    protected val apiPassword = "test"

    lateinit var apiRequestSpec: RequestSpecification

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @BeforeAll
    fun globalSetUp() {
        apiRequestSpec =
            RequestSpecBuilder()
                .setContentType(ContentType.XML)
                .setRelaxedHTTPSValidation()
                .setConfig(RestAssured.config)
                .build()
    }

    @AfterAll
    fun globalTearDown() {
        RestAssured.reset()
    }

    protected fun requestURL(path: String) = "http://localhost:$localServerPort$path"

    protected fun <T> getAndMap(
        url: String,
        status: HttpStatus,
        headers: Map<String, String>,
        responseClass: Class<T>,
        requestBuilder: (RequestSpecification.() -> Unit)? = null,
        shouldLog: Boolean = true,
    ): T = objectMapper.readValue(get(url, status, headers, requestBuilder, shouldLog), responseClass)

    protected fun get(
        url: String,
        status: HttpStatus,
        headers: Map<String, String>,
        requestBuilder: (RequestSpecification.() -> Unit)? = null,
        shouldLog: Boolean = true,
    ): String {
        log.info("request:\nGET $url")

        var res: ExtractableResponse<Response>? = null
        Given {
            spec(apiRequestSpec)
            auth().basic(apiUsername, apiPassword).headers(headers)
        } When {
            requestBuilder?.invoke(this)
            get(requestURL(url))
        } Then {
            res = extract()
        }
        return assertResponse(res, status, shouldLog)
    }

    protected fun <T> put(
        url: String,
        body: T,
        status: HttpStatus,
        headers: Map<String, String> = emptyMap(),
    ): String = send(url, body, method = Method.PUT, status = status, headers = headers)

    protected fun <T> post(
        url: String,
        body: T,
        status: HttpStatus,
        headers: Map<String, String> = emptyMap(),
    ): String = send(url, body, method = Method.POST, status = status, headers = headers)

    protected fun <T> delete(
        url: String,
        body: T,
        status: HttpStatus,
        headers: Map<String, String> = emptyMap(),
    ): String = send(url, body, method = Method.DELETE, status = status, headers = headers)

    protected fun <T> send(
        url: String,
        body: T,
        status: HttpStatus,
        method: Method = Method.POST,
        headers: Map<String, String> = emptyMap(),
    ): String {
        val json = JsonUtils.toJson(body, true)
        log.info("request: ${method.name} $url\n$json")

        var res: ExtractableResponse<Response>? = null
        Given {
            spec(apiRequestSpec)
            auth()
                .basic(apiUsername, apiPassword)
                .headers(headers)
                .body(json)
                .contentType(ContentType.JSON)
        } When {
            request(method, requestURL(url))
        } Then {
            res = extract()
        }
        return assertResponse(res, status)
    }

    private fun assertResponse(
        res: ExtractableResponse<Response>?,
        expectedStatus: HttpStatus,
        shouldLog: Boolean = true,
    ): String {
        assertNotNull(res).apply {
            val actualCode = statusCode()
            val expectedCode = expectedStatus.value()
            return if (actualCode == expectedStatus.value()) {
                val content = body().asString()
                if (shouldLog) {
                    log.info("response:\n${statusLine()}\n$content")
                }
                content
            } else {
                val content = body().asString()
                val msg =
                    listOf(
                        "Unexpected status code: expected <$expectedCode> but was <$actualCode>\n",
                        statusLine(),
                        content,
                    ).joinToString("\n")
                Assertions.fail<String>(msg)
                content
            }
        }
    }
}
