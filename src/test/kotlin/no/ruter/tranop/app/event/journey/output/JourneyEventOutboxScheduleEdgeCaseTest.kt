package no.ruter.tranop.app.event.journey.output

import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestConfig
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.InternalOutbox
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class JourneyEventOutboxScheduleEdgeCaseTest {
    private val timeService = mock<TimeService>()
    private val outboxService = mock<OutboxService>()
    private val streamingIngestClient =
        JourneyEventSnowflakeIngestClient(
            config = SnowflakeTestConfig.DEFAULT_CLIENT_PROPERTIES,
            clientFactory = SnowflakeTestClientFactory(),
        )
    private val config =
        JourneyEventOutboxStreamingConfig().apply {
            batchSize = 10
            retryCount = 5
        }
    private val consumer =
        JourneyEventOutboxSchedule(
            timeService,
            outboxService,
            streamingIngestClient,
            config,
        )

    @Test
    fun `processPendingEvents handles malformed payload gracefully`() {
        val recordMock = mock<OutboxRecord>()
        val dataMock = mock<Outbox>()
        whenever(recordMock.ref).doReturn("bad-payload-ref")
        val internalOutboxMock = mock<InternalOutbox>()
        whenever(internalOutboxMock.record).doReturn(recordMock)
        whenever(internalOutboxMock.data).doReturn(dataMock)
        whenever(dataMock.payload) doReturn ("{not-json}")

        whenever(
            outboxService.findUnpublished(
                eq(DBOutboxDataType.DATED_JOURNEY_EVENT),
                eq(DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1),
                any(),
                any(),
            ),
        ).doReturn(listOf(internalOutboxMock))

        consumer.processPendingEvents()
        verify(outboxService).markAsFailed(listOf(internalOutboxMock))
    }
}
