package no.ruter.tranop.app.event.journey.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.app.event.journey.db.InternalJourneyEvent
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.app.test.AbstractBaseTest
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource
import java.time.OffsetDateTime
import kotlin.test.Test

@TestPropertySource(
    properties = [
        "${SnowflakeClientProperties.KEY_PREFIX}.enabled=true",
        "app.config.outbox.cleanup.enabled=true",
        "app.config.outbox.snowflake-streaming-journey-event.enabled=true",
    ],
)
class JourneyEventOutboxSnowflakeStreamingIntegrationTest : AbstractBaseTest() {
    @Autowired lateinit var consumer: JourneyEventOutboxSchedule

    @BeforeEach
    fun setUp() {
        testEnvironment.reset()
    }

    @Test
    fun `event is processed and marked as published`() {
        val ref = "integration-test-ref"

        val journeyEventContext =
            JourneyEventInputContext(
                channel = RecordType.DATED_JOURNEY.channels.internal,
                received = OffsetDateTime.now(),
                internalEvent =
                    InternalJourneyEvent(
                        event =
                            DTOJourneyEvent().apply {
                                this.entityDatedJourneyKeyV2Ref = "dated-journey-456"
                                this.id = "journey-payload-ref"
                            },
                    ),
            )

        val record =
            outboxRepository.newRecord(
                Outbox(
                    ref = ref,
                    dataType = DBOutboxDataType.DATED_JOURNEY_EVENT,
                    targetType = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                    payloadRef = "payload-ref",
                    payload = JsonUtils.toJson(journeyEventContext.toBIJourneyEvent()),
                ),
            )
        outboxRepository.storeRecord(record)

        val storedEvents = outboxRepository.fetchAll()
        val storedEvent = storedEvents.find { it.data.ref == ref }
        assertNotNull(storedEvent)
        assert(storedEvent!!.record.publishedAt == null)

        consumer.processPendingEvents()

        val events = outboxRepository.fetchAll()
        val processed = events.find { it.data.ref == ref }
        assertNotNull(processed)
    }
}
