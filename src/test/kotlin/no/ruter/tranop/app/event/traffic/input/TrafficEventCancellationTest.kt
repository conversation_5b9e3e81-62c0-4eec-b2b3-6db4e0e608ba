package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyCancellation
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyReferences
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class TrafficEventCancellationTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - Empty object test`() {
        Assertions.assertThat(trafficEventService.processInput("key", DTOTrafficEvent()).valid()).isFalse
        Assertions.assertThat(trafficEventService.processInput("key", null).valid()).isTrue
        Assertions.assertThat(trafficEventService.processInput(null, null).valid()).isTrue
    }

    @Test
    fun `TrafficEvent - Full Cancellation - Enabled`() {
        trafficEventConfig.cancellation.apply { enabled = true }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event =
            DTOTrafficEvent().apply {
                this.header =
                    DTOMessageHeader().apply {
                        this.messageTimestamp = timeService.now().toUtcIsoString()
                        this.traceId = "Test - TrafficEvent - Full Cancellation"
                    }
                this.journeyRefs =
                    DTOTrafficEventJourneyReferences().apply {
                        this.entityDatedJourneyKeyV2Ref = journeyRef
                    }
                this.cancellation =
                    DTOTrafficEventJourneyCancellation().apply {
                        this.cancelled = true
                    }
            }
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.CANCELLED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(storedJourney.cancelled).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { it.cancelled == true }
    }

    @Test
    fun `TrafficEvent - Full Cancellation - Disabled`() {
        trafficEventConfig.cancellation.apply { enabled = false }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event =
            DTOTrafficEvent().apply {
                this.header =
                    DTOMessageHeader().apply {
                        this.messageTimestamp = timeService.now().toUtcIsoString()
                        this.traceId = "Test - TrafficEvent - Full Cancellation"
                    }
                this.journeyRefs =
                    DTOTrafficEventJourneyReferences().apply {
                        this.entityDatedJourneyKeyV2Ref = journeyRef
                    }
                this.cancellation =
                    DTOTrafficEventJourneyCancellation().apply {
                        this.cancelled = true
                    }
            }
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CANCELLED]).isNullOrEmpty()
    }

    @Test
    fun `TrafficEvent - Partial Cancellation - Repetitive Quays`() {
        // djv2 has repetitive quays and one repetitive quay is cancelled but should only be cancelled once
        trafficEventConfig.cancellation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-3321b-dated-journey-b1.json")
        timeService.offset(journey)
        pipe(journey)
        val trafficEvent = TestUtils.readTrafficEvent("traffic-event/djj-3321b-traffic-event-b1.json")
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events[DTOEventType.CALLS_CANCELLED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.partiallyCancelled).isTrue
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.cancelled).isFalse
        Assertions.assertThat(storedJourney.plan.calls.count { c -> c.cancelled }).isEqualTo(4)
    }

    @Test
    fun `TrafficEvent - Partial Cancellation - Then Recalled`() {
        // djv2 has 2 traffic events, one is partially cancelled and the other is recalled
        trafficEventConfig.cancellation.apply { enabled = true }
        val journey = TestUtils.readDatedJourney("traffic-event/djj-b028f3-dated-journey.json")
        timeService.offset(journey)
        pipe(journey)
        val trafficEvent = TestUtils.readTrafficEvent("traffic-event/djj-b028f3-traffic-event-cancellation.json")
        val context = trafficEventService.processInput(journey.ref, trafficEvent)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journey.ref)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events[DTOEventType.CALLS_CANCELLED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events).hasSize(3)
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.partiallyCancelled).isTrue
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.cancelled).isFalse
        Assertions.assertThat(storedJourney.plan.calls.count { c -> c.cancelled }).isEqualTo(9)

        val trafficEventRecalled = TestUtils.readTrafficEvent("traffic-event/djj-b028f3-traffic-event-recalled.json")
        val contextRecalled = trafficEventService.processInput(journey.ref, trafficEventRecalled)
        Assertions
            .assertThat(
                contextRecalled.valid(),
            ).`as`(contextRecalled.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourneyRecalled = assertJourneyExists(journey.ref)
        val eventsRecalled = storedJourneyRecalled.events.groupBy { it.type }
        Assertions.assertThat(eventsRecalled[DTOEventType.CALLS_CANCELLED]).hasSize(1)
        Assertions.assertThat(eventsRecalled[DTOEventType.CALLS_UNCANCELLED]).hasSize(1)
        Assertions.assertThat(eventsRecalled[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(eventsRecalled).hasSize(4)
        Assertions.assertThat(storedJourneyRecalled.journeyState.journeyMitigationState.partiallyCancelled).isFalse
        Assertions.assertThat(storedJourneyRecalled.journeyState.journeyMitigationState.cancelled).isFalse
        Assertions.assertThat(storedJourneyRecalled.plan.calls.count { c -> c.cancelled }).isEqualTo(0)
    }
}
