package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyContingencyVehiclePlanned
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyReferences
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test

class TrafficEventContingencyVehiclePlannedTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - ContingencyVehiclePlanned - Update dated journey v2 - Events and state should be the same`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = vehicleTask1datedJourney1
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }

        pipe(journeyRef, journey)
        val storedJourneyAfterUpdateOnJourney = assertJourneyExists(journeyRef)
        val eventsAfterUpdateOnJourney = storedJourneyAfterUpdateOnJourney.events.groupBy { it.type }
        Assertions.assertThat(eventsAfterUpdateOnJourney).hasSize(2)
        Assertions.assertThat(eventsAfterUpdateOnJourney[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(eventsAfterUpdateOnJourney[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions.assertThat(storedJourneyAfterUpdateOnJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned).isTrue
        Assertions.assertThat(storedJourneyAfterUpdateOnJourney.plan.calls).allMatch { !it.cancelled }
    }

    @Test
    fun `TrafficEvent - ContingencyVehiclePlanned - Planned`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).hasSize(1)
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned).isTrue
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
    }

    @Test
    fun `TrafficEvent - ContingencyVehiclePlanned - Not planned`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, false)
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).isNull()
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned).isFalse
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
    }

    @Test
    fun `TrafficEvent - ContingencyVehiclePlanned - Handles null`() {
        trafficEventConfig.mitigation.apply { enabled = true }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, null)
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).isNull()
        Assertions.assertThat(storedJourney.journeyState.journeyMitigationState.contingencyVehiclePlanned).isFalse
        Assertions.assertThat(storedJourney.plan.calls).allMatch { !it.cancelled }
    }

    @Test
    fun `TrafficEvent - Mitigation - Config flag is false`() {
        trafficEventConfig.mitigation.apply { enabled = false }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        trafficEventService.processInput(journeyRef, event)
        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.CONTINGENCY_VEHICLE_PLANNED]).isNull()
    }

    private fun createEvent(
        journeyRef: String,
        planned: Boolean?,
    ): DTOTrafficEvent =
        DTOTrafficEvent().apply {
            header =
                DTOMessageHeader().apply {
                    messageTimestamp = timeService.now().toUtcIsoString()
                    traceId = "Test - TrafficEvent - ContingencyVehiclePlanned"
                }
            journeyRefs =
                DTOTrafficEventJourneyReferences().apply {
                    entityDatedJourneyKeyV2Ref = journeyRef
                }
            contingencyVehiclePlanned =
                planned?.let {
                    DTOTrafficEventJourneyContingencyVehiclePlanned().apply { this.planned = it }
                }
        }
}
