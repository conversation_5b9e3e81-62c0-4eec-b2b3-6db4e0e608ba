package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.app.test.TestUtils
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyOrganizedRailReplacementVehicles
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyReferences
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TrafficEventOrganizedRailReplacementTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - OrganizedRailReplacement`() {
        trafficEventConfig.mitigation.enabled = true
        trafficEventConfig.organizedRailReplacementVehicles.enabled = true

        val journey = TestUtils.readDatedJourney("mitigation/service-replacement-line-12/dated-journey-01.json")

        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event = createEvent(journeyRef, true)
        val context = trafficEventService.processInput(journeyRef, event)
        assertThat(
            context.valid(),
        ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        assertThat(storedJourney.cancelled).isTrue()
    }

    private fun createEvent(
        journeyRef: String,
        planned: Boolean?,
    ): DTOTrafficEvent =
        DTOTrafficEvent().apply {
            ref = "te-0000"
            header =
                DTOMessageHeader().apply {
                    messageTimestamp = timeService.now().toUtcIsoString()
                    traceId = "Test - TrafficEvent - OrganizedRailReplacement"
                }
            journeyRefs =
                DTOTrafficEventJourneyReferences().apply {
                    entityDatedJourneyKeyV2Ref = journeyRef
                }

            organizedRailReplacementVehicles =
                planned?.let {
                    DTOTrafficEventJourneyOrganizedRailReplacementVehicles().apply { this.planned = it }
                }
        }
}
