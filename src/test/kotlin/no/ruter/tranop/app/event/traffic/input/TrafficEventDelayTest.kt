package no.ruter.tranop.app.event.traffic.input

import no.ruter.tranop.app.test.AbstractApiTest
import no.ruter.tranop.app.test.assertSize
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficCaseReferences
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEvent
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyDelay
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventJourneyReferences
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficEventReferences
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficReferences
import no.ruter.tranop.traffic.event.dto.model.DTOTrafficSituationReferences
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import java.time.Duration
import kotlin.test.assertNotNull

class TrafficEventDelayTest : AbstractApiTest() {
    @Test
    fun `TrafficEvent - Delay - Enabled`() {
        trafficEventConfig.delay.apply { enabled = true }
        val expectedDelayMinutes = 12
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event =
            DTOTrafficEvent().apply {
                this.header =
                    DTOMessageHeader().apply {
                        this.messageTimestamp = timeService.now().toUtcIsoString()
                        this.traceId = "Test - TrafficEvent - Full Cancellation"
                    }
                this.trafficRefs =
                    DTOTrafficReferences().apply {
                        this.eventRefs =
                            DTOTrafficEventReferences().apply {
                                this.entityTrafficEventKeyV0Ref = "EventKeyV0Ref"
                                this.entityTrafficEventKeyV1Ref = "EventKeyV1Ref"
                            }
                        this.caseRefs =
                            DTOTrafficCaseReferences().apply {
                                this.entityTrafficCaseKeyV2Ref = "CaseKeyV2Ref"
                            }
                        this.situationRefs =
                            DTOTrafficSituationReferences().apply {
                                this.entityTrafficSituationKeyV2Ref = "SituationKeyV2Ref"
                            }
                    }
                this.journeyRefs =
                    DTOTrafficEventJourneyReferences().apply {
                        this.entityDatedJourneyKeyV2Ref = journeyRef
                    }
                this.delay =
                    DTOTrafficEventJourneyDelay().apply {
                        this.delayMinutes = expectedDelayMinutes.toString()
                    }
            }
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        assertStoredJourneyDto(journeyRef, expectedDelayMinutes)
        assertProducedJourneyEntity(expectedDelayMinutes)
    }

    private fun assertStoredJourneyDto(
        journeyRef: String?,
        expectedDelayMinutes: Int,
    ) {
        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(2)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.DELAYED]).hasSize(1)
        val delayed = assertNotNull(events[DTOEventType.DELAYED]).first()
        val metadata = assertNotNull(delayed.metadata)
        val delayMeta =
            metadata
                .filter {
                    it.key == DTOEventMetadataKeyType.DELAY_MINUTES
                }.assertSize(size = 1)
                .first()
        Assertions.assertThat(delayMeta.value).isEqualTo(expectedDelayMinutes.toString())

        storedJourney.plan.calls.forEach { call ->
            if (expectedDelayMinutes != 0) {
                val plannedArrival = call.plannedArrival.toOffsetDateTime()
                val plannedDeparture = call.plannedDeparture.toOffsetDateTime()
                val expectedArrival = call.expectedArrival.toOffsetDateTime()
                val expectedDeparture = call.expectedDeparture.toOffsetDateTime()

                Assertions
                    .assertThat(Duration.between(plannedDeparture, expectedDeparture).toMinutes())
                    .isEqualTo(expectedDelayMinutes.toLong())
                Assertions
                    .assertThat(Duration.between(plannedArrival, expectedArrival).toMinutes())
                    .isEqualTo(expectedDelayMinutes.toLong())
            } else {
                Assertions.assertThat(call.expectedArrival).isNull()
                Assertions.assertThat(call.expectedDeparture).isNull()
            }
        }
    }

    private fun assertProducedJourneyEntity(expectedDelayMinutes: Int) {
        publishDatedJourneyRoutine.execute(timeService.now())
        val produced = kafka.datedJourneyProducer.getRecords()
        Assertions.assertThat(produced).hasSize(1)
        val producedEntity = assertNotNull(produced.first().value())
        val producedEntityData = producedEntity.entityData
        val producedEntityHeader = producedEntity.entityHeader
        val producedEvents = assertNotNull(producedEntityData.events)
        val producedJourney = assertNotNull(producedEntityData.journey)
        Assertions.assertThat(producedEntityHeader).isNotNull
        Assertions.assertThat(producedEvents).hasSize(2)
        Assertions.assertThat(producedEvents.filter { it.type == DTOEventType.DELAYED.value() }).hasSize(1)
        producedJourney.callSequence.forEach { call ->
            if (expectedDelayMinutes != 0) {
                val plannedArrival = call.plannedArrivalDateTime.toOffsetDateTime()
                val plannedDeparture = call.plannedDepartureDateTime.toOffsetDateTime()
                val expectedArrival = call.expectedArrivalDateTime.toOffsetDateTime()
                val expectedDeparture = call.expectedDepartureDateTime.toOffsetDateTime()

                Assertions
                    .assertThat(Duration.between(plannedDeparture, expectedDeparture).toMinutes())
                    .isEqualTo(expectedDelayMinutes.toLong())
                Assertions
                    .assertThat(Duration.between(plannedArrival, expectedArrival).toMinutes())
                    .isEqualTo(expectedDelayMinutes.toLong())
            } else {
                Assertions.assertThat(call.expectedArrivalDateTime).isNull()
                Assertions.assertThat(call.expectedDepartureDateTime).isNull()
            }
        }
    }

    @Test
    fun `TrafficEvent - UnDelay - Enabled`() {
        trafficEventConfig.delay.apply { enabled = true }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        val expectedDelayMinutes = 12
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event =
            DTOTrafficEvent().apply {
                this.header =
                    DTOMessageHeader().apply {
                        this.messageTimestamp = timeService.now().toUtcIsoString()
                        this.traceId = "Test - TrafficEvent - Full Cancellation"
                    }
                this.journeyRefs =
                    DTOTrafficEventJourneyReferences().apply {
                        this.entityDatedJourneyKeyV2Ref = journeyRef
                    }
                this.delay =
                    DTOTrafficEventJourneyDelay().apply {
                        this.delayMinutes = expectedDelayMinutes.toString()
                    }
            }
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        assertStoredJourneyDto(journeyRef, expectedDelayMinutes)
        assertProducedJourneyEntity(expectedDelayMinutes)
    }

    @Test
    fun `TrafficEvent - Delay - Disabled`() {
        trafficEventConfig.delay.apply { enabled = false }
        val journey = vehicleTask1datedJourney2
        val journeyRef = journey.ref
        timeService.offset(journey)
        pipe(journeyRef, journey)

        val event =
            DTOTrafficEvent().apply {
                this.header =
                    DTOMessageHeader().apply {
                        this.messageTimestamp = timeService.now().toUtcIsoString()
                        this.traceId = "Test - TrafficEvent - Full Cancellation"
                    }
                this.journeyRefs =
                    DTOTrafficEventJourneyReferences().apply {
                        this.entityDatedJourneyKeyV2Ref = journeyRef
                    }
                this.delay =
                    DTOTrafficEventJourneyDelay().apply {
                        this.delayMinutes = "12"
                    }
            }
        val context = trafficEventService.processInput(journeyRef, event)
        Assertions
            .assertThat(
                context.valid(),
            ).`as`(context.statusDetails.joinToString(System.lineSeparator()) { it.description })
            .isTrue

        val storedJourney = assertJourneyExists(journeyRef)
        val events = storedJourney.events.groupBy { it.type }
        Assertions.assertThat(events).hasSize(1)
        Assertions.assertThat(events[DTOEventType.IMPORTED]).hasSize(1)
        Assertions.assertThat(events[DTOEventType.DELAYED]).isNullOrEmpty()
    }
}
