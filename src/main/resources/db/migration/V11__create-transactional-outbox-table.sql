--
-- Migration V11: Create transactional outbox table
--
-- This migration creates the `outbox` table for implementing the transactional outbox pattern.
-- The table stores events to be published.
-- Fields include data type, target type, payload reference, payload, publish status, retry count, and timestamps.
--
CREATE TABLE outbox
(
    -- Base record fields
    "id"            SERIAL PRIMARY KEY,
    "ref"           VARCHAR                  NOT NULL,
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by      VA<PERSON><PERSON><PERSON>(255)             NOT NULL,

    -- Outbox specific fields
    data_type       VARCHAR(255),
    last_attempt_at TIMESTAMP WITH TIME ZONE NULL,
    payload         JSONB                    NOT NULL,
    payload_ref     VARCHAR(255),
    published_at    TIMESTAMP WITH TIME ZONE NULL,
    retries         INT                      DEFAULT 0,
    target_type     VARCHAR(255)
);

-- Indexes for the outbox table
CREATE INDEX idx_published_at ON outbox (published_at);
