/* Deviations **/
CREATE TABLE deviation
(
    "id"             SERIAL PRIMARY KEY,
    json_data        JSONB,
    revision         INT,

    "ref"            VARCHAR NOT NULL,

    created_at       TIMESTAMP WITH TIME ZONE,
    created_by       <PERSON><PERSON><PERSON><PERSON>,

    modified_at      TIMESTAMP WITH TIME ZONE,
    modified_by      VARCHAR,

    published_at            TIMESTAMP WITH TIME ZONE,
    published_revision      INT DEFAULT 0,
    tombstoned_at           TIMESTAMP WITH TIME ZONE

);

/** Primary indexes **/
CREATE UNIQUE INDEX idx_deviation_ref ON deviation("ref");
