/* Dated Journey Input Table */
CREATE TABLE dated_journey_input (
    "id"             SERIAL PRIMARY KEY,
    "ref"            VARCHAR NOT NULL,
    revision         INT,
    json_data        JSONB,

    operating_date   DATE,
    first_arrival    TIMESTAMP WITH TIME ZONE,
    first_departure  TIMESTAMP WITH TIME ZONE,
    last_arrival     TIMESTAMP WITH TIME ZONE,
    last_departure   TIMESTAMP WITH TIME ZONE,

    created_at       TIMESTAMP WITH TIME ZONE,
    created_by       VARCHAR,

    modified_at      TIMESTAMP WITH TIME ZONE,
    modified_by      VARCHAR,

    deleted_at       TIMESTAMP WITH TIME ZONE,
    deleted_by       VARCHAR
);

/** Primary indexes **/
CREATE UNIQUE INDEX idx_dated_journey_input ON dated_journey_input("ref");

/** Secondary indexes **/
CREATE INDEX idx_dated_journey_input_active_period ON dated_journey_input("first_departure", "last_arrival");
CREATE INDEX idx_dated_journey_input_operating_date ON dated_journey_input("operating_date");
