/* Mitigations **/
CREATE TABLE mitigation
(
    "id"             SERIAL PRIMARY KEY,
    json_data        JSONB,
    revision         INT,

    "ref"            VARCHAR NOT NULL,

    created_at       TIMESTAMP WITH TIME ZONE,
    created_by       <PERSON><PERSON><PERSON><PERSON>,

    modified_at      TIMESTAMP WITH TIME ZONE,
    modified_by      <PERSON><PERSON><PERSON><PERSON>,

    published_at            TIMESTAMP WITH TIME ZONE,
    published_revision      INT DEFAULT 0,
    tombstoned_at           TIMESTAMP WITH TIME ZONE,

    operator_id     VARCHAR,
    authority_id    VARCHAR,

    deleted_at      TIMESTAMP WITH TIME ZONE,
    deleted_revision    INT

);

/** Primary indexes **/
CREATE UNIQUE INDEX idx_mitigation_ref ON mitigation("ref");
