/* Stop Point */
CREATE TABLE stop_point
(
    "id"               SERIAL PRIMARY KEY,
    "ref"              VARCHAR NOT NULL,

    json_data          JSONB,
    json_hash          VARCHAR,

    quay_id            VARCHAR,

    /* "Audit" fields **/
    revision           INT                      DEFAULT 0,

    created_at         TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by         <PERSON><PERSON><PERSON><PERSON>,

    modified_at        TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_by        VARCHAR,

    deleted_at         TIMESTAMP WITH TIME ZONE,
    deleted_version    INT,

    republish          BOOLEAN,
    published_at       TIMESTAMP WITH TIME ZONE,
    published_revision INT
);

/* Stop Point - primary index */
CREATE UNIQUE INDEX idx_stop_point_ref ON stop_point (ref);

CREATE INDEX idx_stop_point_hash ON stop_point (json_hash)
