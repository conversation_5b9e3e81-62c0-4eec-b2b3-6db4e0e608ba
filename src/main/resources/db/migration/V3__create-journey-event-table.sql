/* Journey Event **/
CREATE TABLE journey_event
(
    "id"                 SERIAL PRIMARY KEY,
    json_data            JSONB,
    revision             INT,
    "ref"                VARCHAR NOT NULL,

    type                    VARCHAR,
    reason                  VARCHAR,
    assignment_ref          VARCHAR,
    dated_journey_v2_ref    VARCHAR,

    created_at           TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by           <PERSON><PERSON><PERSON><PERSON>,

    modified_at          TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_by          VA<PERSON><PERSON><PERSON>,

    published_at         TIMESTAMP WITH TIME ZONE,
    published_revision   INT
);

/** Primary indexes **/
CREATE UNIQUE INDEX idx_journey_event_ref ON journey_event("ref");

/** Secondary indexes **/
CREATE INDEX idx_journey_event_type ON journey_event ("type");
CREATE INDEX idx_journey_event_reason ON journey_event ("reason");
CREATE INDEX idx_journey_event_dated_journey_v2_ref ON journey_event ("dated_journey_v2_ref");

CREATE INDEX idx_journey_event_assignment_ref ON journey_event ("assignment_ref");

