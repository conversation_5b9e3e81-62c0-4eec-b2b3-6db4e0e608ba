/* Dated Journey **/
CREATE TABLE dated_journey
(
    "id"             SERIAL PRIMARY KEY,
    json_data        JSONB,
    revision         INT,

    "ref"            VARCHAR NOT NULL,
    dated_block_ref  VARCHAR,
    vehicle_task_ref VARCHAR,

    line_id                     VARCHAR,
    journey_type                VARCHAR,
    operator_contract_id        VARCHAR,

    vehicle_journey_id          VARCHAR,
    external_journey_ref        VARCHAR,
    external_journey_ref_v2     VARCHAR,

    dated_journey_v1_ref        VARCHAR,
    dated_service_journey_id    VARCHAR,

    operating_date   DATE,
    first_arrival    TIMESTAMP WITH TIME ZONE,
    first_departure  TIMESTAMP WITH TIME ZONE,
    last_arrival     TIMESTAMP WITH TIME ZONE,
    last_departure   TIMESTAMP WITH TIME ZONE,

    created_at       TIMESTAMP WITH TIME ZONE,
    created_by       <PERSON><PERSON><PERSON><PERSON>,

    modified_at      TIMESTAMP WITH TIME ZONE,
    modified_by      VA<PERSON><PERSON><PERSON>,

    published_at            TIMESTAMP WITH TIME ZONE,
    published_revision      INT DEFAULT 0,
    tombstoned_at           TIMESTAMP WITH TIME ZONE,

    omitted                 <PERSON><PERSON><PERSON><PERSON><PERSON>,
    partially_omitted       <PERSON><PERSON><PERSON><PERSON><PERSON>,
    assigned                BOOLEAN,
    cancelled               <PERSON><PERSON><PERSON><PERSON><PERSON>,
    partially_cancelled     <PERSON><PERSON>OLEAN
);

/** Primary indexes **/
CREATE UNIQUE INDEX idx_dated_journey_ref ON dated_journey("ref");

/** Secondary indexes **/
CREATE INDEX idx_dated_journey_dated_block_ref ON dated_journey ("dated_block_ref");
CREATE INDEX idx_dated_journey_vehicle_task_ref ON dated_journey("vehicle_task_ref");
CREATE INDEX idx_dated_journey_operating_date ON dated_journey("operating_date");

CREATE INDEX idx_dated_journey_active_period ON dated_journey("first_departure", "last_arrival");
CREATE INDEX idx_dated_journey_external_journey_ref ON dated_journey("external_journey_ref");
CREATE INDEX idx_dated_journey_external_journey_ref_v2 ON dated_journey("external_journey_ref_v2");

CREATE INDEX idx_dated_journey_dated_journey_v1_ref ON dated_journey (dated_journey_v1_ref);
CREATE INDEX idx_dated_journey_dated_service_journey_id ON dated_journey("dated_service_journey_id");

CREATE INDEX idx_dated_journey_composite_id ON dated_journey(line_id, vehicle_journey_id, first_departure);
