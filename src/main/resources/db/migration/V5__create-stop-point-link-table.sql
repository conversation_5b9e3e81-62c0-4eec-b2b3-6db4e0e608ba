/* Stop Point Link */
CREATE TABLE stop_point_link (
    "id" SERIAL PRIMARY KEY,
    "ref" VARCHAR NOT NULL,

    json_data JSONB,
    json_hash VARCHAR,

    from_stop_point_ref VARCHAR,
    to_stop_point_ref VARCHAR,

    /* "Audit" fields **/
    revision INT DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>,

    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_by <PERSON><PERSON><PERSON><PERSON>,

    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_version INT,

    republish BOOLEAN,
    published_at TIMESTAMP WITH TIME ZONE,
    published_revision INT
);

/* Stop Point Link - primary indexes */
CREATE UNIQUE INDEX idx_stop_point_link_ref ON stop_point_link("ref");
CREATE INDEX  idx_stop_point_link_hash ON stop_point_link(json_hash)
