/* External references */
CREATE TABLE external_ref (
    owner_ref VARCHAR NOT NULL,     /* owner record ref */
    owner_type INTEGER NOT NULL,    /* owner record type (see RecordType.numericValue for values) */

    ref_type INTEGER NOT NULL,      /* external ref type (See ExternalRefType.value for values) */
    ref_value VARCHAR NOT NULL,     /* external ref value. */

    created_by VA<PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY(owner_ref, "ref_type", "ref_value")
);

/* Secondary external reference indexes */
CREATE INDEX idx_external_ref_value ON external_ref("ref_value");
CREATE INDEX idx_external_ref_owner_type_ref_value ON external_ref("owner_type", "ref_value");
