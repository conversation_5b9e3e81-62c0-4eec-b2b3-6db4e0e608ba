CREATE TABLE dated_journey_publish_status
(
    "id"            SERIAL PRIMARY KEY,
    ref             VARCHAR NOT NULL,
    owner           VA<PERSON>HAR NOT NULL,
    published_at    TIMESTAMP WITH TIME ZONE,

    /* "Audit" fields **/
    revision        INT                      DEFAULT 0,

    created_at      TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by      <PERSON><PERSON><PERSON><PERSON>,

    modified_at     TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_by     VARCHAR,

    deleted_at      TIMESTAMP WITH TIME ZONE,
    deleted_version INT,

    CONSTRAINT dated_journey_publish_status_ref_idx UNIQUE (ref, owner)
)

