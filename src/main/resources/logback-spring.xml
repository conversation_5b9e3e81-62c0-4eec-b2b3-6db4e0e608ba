<configuration>
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

  <statusListener class="ch.qos.logback.core.status.NopStatusListener" />


  <springProfile name="local">
    <include resource="custom-console-appender-plain.xml"/>
  </springProfile>

  <springProfile name="!local">
    <include resource="custom-console-appender-json.xml"/>
  </springProfile>

  <logger level="WARN" name="org.springframework"/>

  <logger additivity="false" level="WARN" name="org.apache.kafka">
    <appender-ref ref="CONSOLE"/>
  </logger>

  <logger additivity="false" level="WARN" name="io.confluent.kafka">
    <appender-ref ref="CONSOLE"/>
  </logger>

  <logger additivity="false" level="WARN" name="net.snowflake">
    <appender-ref ref="CONSOLE"/>
  </logger>

  <logger additivity="false" level="INFO" name="no.ruter">
    <appender-ref ref="CONSOLE"/>
  </logger>

  <root level="info">
    <appender-ref ref="CONSOLE"/>
  </root>


</configuration>
