info:
  app:
    name: "assignment-journey-manager"
    team: "tranop"
    domain: "assignment"

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    configprops:
      show-values: always

org:
  jooq:
    no-tips: true
    no-logo: true
spring:
  main:
    banner-mode: off
  jooq:
    sql-dialect: Postgres
  security:
    user:
      roles: admin
      name: admin
      password: test
  datasource:
    url: jdbc:postgresql://${RDP_APP_DB_CLUSTER_ENDPOINT_READ_WRITE}/${RDP_APP_DB_NAME}
    username: ${RDP_APP_DB_USERNAME}
    password: ${RDP_APP_DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jmx:
    enabled: true
  jackson:
    serialization:
      indent_output: true
    default-property-inclusion: non_null
  output.ansi.enabled: never

http:
  assignment-status: http://assignment-gateway:8080/api/v2/status

kafka:
  partitions: 12
  replicationFactor: 3
  consumers:
    enabled: true
  stop:
    name: private.dated-journey.12.stop-point.v2
    group: ${info.app.name}-cg-stop-001-tmp
  assignment-stop-point:
    name: private.assignment.12.stop.v1
  link:
    name: private.dated-journey.12.stop-point-link.v2
    group: ${info.app.name}-cg-link-001-tmp
  assignment-stop-point-link:
    name: private.assignment.12.link.v1
  dated-journey:
    name: private.dated-journey.12.dated-journey.v2
    group: ${info.app.name}-cg-dated-journey-001-tmp
  assignment-journey:
    name: private.assignment.12.journey.v1
    group: ${info.app.name}-cg-assignment-journey-001-tmp
  traffic-event:
    name: private.dated-journey.12.traffic-event.v1
    group: ${info.app.name}-cg-traffic-event-001-tmp
  assignment:
    name: private.assignment.12.vehicle.assignment
    group: ${info.app.name}-cg-assignment-001-tmp
  entity-vehicle-api:
    name: entity.vehicle.api.v1
    group: ${info.app.name}-cg-vehicle-api-001-tmp
  entity-dated-journey-v2:
    name: entity.dated-journey.v2.key.p12
  entity-dated-journey-stop-point-v2:
    name: entity.dated-journey-stop-point.v2.key.p12
    group: ${info.app.name}-cg-stop-point-001
  entity-dated-journey-stop-point-link-v2:
    name: entity.dated-journey-stop-point-link.v2.key.p12
    group: ${info.app.name}-cg-stop-point-link-001
  entity-service-deviation:
    name: entity.service-deviation.v1.key.p12
    group: ${info.app.name}-cg-service-deviation-001
  available-destination-displays-adt2:
    name: mqtt.adt.ota.api.v2.output.di.available.destination.display
  available-destination-displays-adt3:
    name: mqtt.adt.ota.api.v3.output.di.available.destination.display
  journey-event:
    name: private.assignment.12.journey.event
    group: ${info.app.name}-cg-journey-event-001-tmp
  deviation:
      name: private.assignment.12.deviation
      group: ${info.app.name}-cg-deviation-001-tmp

  configuration:
    "[bootstrap.servers]": ${KAFKA_BROKER_URL:"bootstrap.url.not.set"}
    "[schema.registry.url]": ${SCHEMA_REGISTRY_URL:"schemareg.url.not.set"}
    # Kafka Consumer config
    "[application.id]": ${info.app.name} # Used by Streams API to set consumer group id
    "[group.id]": ${info.app.name}-group # Used by Consumer API to set consumer group id
    "[default.key.serde]": "org.apache.kafka.common.serialization.Serdes$StringSerde"
    "[default.value.serde]": "org.apache.kafka.common.serialization.Serdes$StringSerde"
    "[key.deserializer]": "org.apache.kafka.common.serialization.StringDeserializer"
    "[value.deserializer]": "org.apache.kafka.common.serialization.StringDeserializer"
    "[key.serializer]": "org.apache.kafka.common.serialization.StringSerializer"
    "[value.serializer]": "org.apache.kafka.common.serialization.StringSerializer"
    "[num.standby.replicas]": 2
    "[internal.leave.group.on.close]": true
    "[num.stream.threads]": 4
    "[max.request.size]": 5_068_596

  # Get rid of this structure when rdp-kafka-streams-libs are removed
  topics:
    input:
      stop:
        name: ${kafka.stop.name}
      link:
        name: ${kafka.link.name}
      dated-journey:
        name: ${kafka.dated-journey.name}
      assignment:
        name: ${kafka.assignment.name}
      entity-vehicle-api:
        name: ${kafka.vehicle-api.name}
      journey-event:
        name: ${kafka.journey-event.name}

    output:
      assignment:
        name: ${kafka.assignment.name}
      journey-event:
        name: ${kafka.journey-event.name}
      dated-journey:
        name: ${kafka.entity-dated-journey-v2.name}
      assignment-journey:
        name: ${kafka.assignment-journey.name}
      stop-point:
        name: ${kafka.entity-dated-journey-stop-point-v2.name}
      stop-point-dto:
        name: ${kafka.assignment-stop-point.name}
      stop-point-link:
        name: ${kafka.entity-dated-journey-stop-point-link-v2.name}
      stop-point-link-dto:
        name: ${kafka.assignment-stop-point-link.name}
      available-destination-displays-adt2:
        name: ${kafka.available-destination-displays-adt2.name}
      available-destination-displays-adt3:
        name: ${kafka.available-destination-displays-adt3.name}
      service-deviation-dto:
          name: ${kafka.deviation.name}

app:
  config:
    common:
      db:
        probe:
          every: "PT5S"
          failureThreshold: 3
    streaming-ingest:
      user: ${SNOWFLAKE_SERVICE_USER:"snowflake_service_user.not.set"}
      url: ${SNOWFLAKE_URL:"snowflake_url.not.set"}
      dbName: ${SNOWFLAKE_DB_NAME:"snowflake_db_name.not.set"}
      schemaName: ${SNOWFLAKE_SCHEMA_NAME:"snowflake_schema_name.not.set"}
      tableName: ${SNOWFLAKE_TABLE_NAME:"snowflake_table_name.not.set"}
      privateKey: ${SNOWFLAKE_ASSIGNMENT_SU_KEY:"snowflake_assignment_su_key.not.set"}
    outbox:
      cleanup:
        enabled: false
        schedulerLockName: "outbox-cleanup-daily-001"
        cron: "0 0 1 * * *"
        lockAtMostFor: "10m"
        lockAtLeastFor: "30s"
        retentionDays: 30
      snowflake-streaming-journey-event:
        enabled: false
        batchSize: 1000
        retryCount: 10
        frequent:
          schedulerLockName: "outbox-streaming-journey-event-frequent-001"
          fixedRate: "PT30S"
          initialDelay: "PT10S"
          lockAtMostFor: "1m"
          lockAtLeastFor: "30s"
    journey-event:
      input:
        kafka:
          dto:
            consumer:
              enabled: true
              topic: ${kafka.journey-event.name}
              group: ${kafka.journey-event.group}
      delay:
        enabled: true
      omission:
        enabled: true
      mitigation:
        enabled: true
      operatingWithoutSignon:
        enabled: true
      assignment:
        enabled: true
      cancellation:
        enabled: true
      missingSignOn:
        enabled: true
      duplicateSignOn:
        enabled: true
      missingOperatorAction:
        enabled: true
      lifecycle:
        enabled: true
        types:
          - OMITTED
        daily:
          cron: "0 0 2 * * *"
        frequent:
          schedulerLockName: "journey-event-frequent-001"
          fixedRate: "PT60S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          delete:
            frequent: false
            enabled: true
            olderThan: "P21D"
    assignment:
      api:
        v2:
          attempt:
            enabled: true
          omit:
            enabled: true
          status:
            enabled: true
      input:
        kafka:
          dto:
            consumer:
              enabled: false
              topic: ${kafka.assignment.name}
              group: ${kafka.assignment.group}
              validationErrorResponse: FAIL # FAIL, SKIP, CONTINUE
      update:
        source:
          dated-journey:
            enabled: true
            fields:
              cancelled: true
              callCancelled: true
              stopPointBehaviourType: true
      lifecycle:
        enabled: true
        daily:
          cron: "0 0 3 * * *"
        frequent:
          schedulerLockName: "assignment-frequent-001"
          fixedRate: "PT60S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          delete:
            frequent: false
            enabled: true
            olderThan: "PT60S"
          main:
            frequent: true
            enabled: true
            olderThan: "PT30S"
          publish:
            frequent: true
            enabled: true
            olderThan: "PT30S"
          signOff:
            frequent: true
            enabled: true
            olderThan: "PT30M"
      output:
        kafka:
          dto:
            maxRetires: 10
            producer:
              enabled: false
              topic: ${kafka.assignment.name}
        http:
          status:
            enabled: true
            maxRetires: 5
            url: ${http.assignment-status}
    attempt:
      processing:
        v0:
          singleBlock: true
          interchange: true # Should interchange be included in the resulting DTOAssignment
          replacement-side-effect: true # Has no impact on SiS-assignments
          retry-failed-attempt: false
        v1:
          singleBlock: false # Cannot turn to true as the operators expect vehicleTasks in Assignment
          interchange: true # Should interchange be included in the resulting DTOAssignment
          replacement-side-effect: true # true: Corresponding replaced vehicle is signed off
          retry-failed-attempt: true
        v2:
          singleBlock: false # Cannot turn to true as the operators expect vehicleTasks in Assignment
          interchange: true # Should interchange be included in the resulting DTOAssignment
          replacement-side-effect: true # true: Corresponding replaced vehicle is signed off
          retry-failed-attempt: true
        v3:
          singleBlock: false # Set to false as the operator can choose to include what ever service window they want
          interchange: true # Should interchange be included in the resulting DTOAssignment
          replacement-side-effect: false # false: Corresponding replaced vehicle is not signed off
          retry-failed-attempt: true
        v4:
          singleBlock: false # Set to false as the operator can choose to include what ever service window they want
          interchange: true # Should interchange be included in the resulting DTOAssignment
          replacement-side-effect: false # false: Corresponding replaced vehicle is not signed off
          retry-failed-attempt: true
    service-deviation:
      toggles:
          dummy: true
      input:
        kafka:
          dto:
            consumer:
              enabled: true
              topic: ${kafka.deviation.name}
              group: ${kafka.deviation.group}
              validationErrorResponse: FAIL # FAIL, SKIP, CONTINUE
      lifecycle:
        enabled: true
        frequent:
          enabled: true
          schedulerLockName: "service-deviation-frequent-001"
          fixedRate: "PT45S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          publish:
            frequent: true
            enabled: true
            olderThan: "PT30S"
      output:
        kafka-avro-entity-v1:     # DBOutboxTargetType.KAFKA_AVRO...
          enabled: true
        kafka-dto-v1:     # DBOutboxTargetType.KAFKA_DTO...
            enabled: true
        snowflake-json-bi-v1:     # DBOutboxTargetType.SNOWFLAKE_JSON...
          enabled: true
        kafka:
          entity:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.entity-service-deviation.name}
              group: ${kafka.entity-service-deviation.group}
          dto:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.topics.output.service-deviation-dto.name}
      logging:
        logOutputOk: true
        logOutputSkip: true
    traffic-event:
      delay:
        enabled: true
      cancellation:
        enabled: true
      mitigation:
        enabled: true
      operatingWithoutSignon:
        enabled: true
      organizedRailReplacementVehicles:
        enabled: false
      input:
        kafka:
          dto:
            consumer:
              enabled: true
              topic: ${kafka.traffic-event.name}
              group: ${kafka.traffic-event.group}
              validationErrorResponse: FAIL # FAIL, SKIP, CONTINUE
    stop-point:
      input:
        kafka:
          dto:
            consumer:
              enabled: true
              topic: ${kafka.stop.name}
              group: ${kafka.stop.group}
              validationErrorResponse: FAIL # FAIL, SKIP, CONTINUE
      lifecycle:
        enabled: true
        frequent:
          enabled: true
          schedulerLockName: "stop-point-frequent-001"
          fixedRate: "PT45S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          publish:
            frequent: true
            enabled: true
            olderThan: "PT30S"
      output:
        kafka:
          entity:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.entity-dated-journey-stop-point-v2.name}
              group: ${kafka.entity-dated-journey-stop-point-v2.group}
          dto:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.topics.output.stop-point-dto.name}
      logging:
        logOutputOk: true
        logOutputSkip: true
    stop-point-link:
      input:
        kafka:
          dto:
            consumer:
              enabled: true
              topic: ${kafka.link.name}
              group: ${kafka.link.group}
              validationErrorResponse: FAIL # FAIL, SKIP, CONTINUE
      lifecycle:
        enabled: true
        frequent:
          enabled: true
          schedulerLockName: "stop-point-link-frequent-001"
          fixedRate: "PT45S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          publish:
            frequent: true
            enabled: true
            olderThan: "PT30S"
      output:
        kafka:
          entity:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.entity-dated-journey-stop-point-link-v2.name}
              group: ${kafka.entity-dated-journey-stop-point-link-v2.group}
          dto:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.topics.output.stop-point-link-dto.name}
      logging:
        logOutputOk: true
        logOutputSkip: true
    destination-display:
      output:
        kafka:
          displays-adt2:
            maxRetires: 5
            producer:
              enabled: false
              topic: ${kafka.available-destination-displays-adt2.name}
          displays-adt3:
            maxRetires: 5
            producer:
              enabled: false
              topic: ${kafka.available-destination-displays-adt3.name}
      lifecycle:
        enabled: true
        daily:
          cron: "0 0 4 * * *"
          schedulerLockName: "destination-display-daily-001"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        frequent:
          schedulerLockName: "destination-display-frequent-001"
          fixedRate: "PT45S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          publish:
            frequent: false
            enabled: true
    datedjourney-opensearch:
        lifecycle:
            enabled: true
            frequent:
                schedulerLockName: "dated-journey-opensearch-frequent-001"
                fixedRate: "PT45S"
                initialDelay: "PT60S"
                lockAtMostFor: "3m"
                lockAtLeastFor: "30s"
            routines:
                publish:
                    frequent: true
                    enabled: true
                    olderThan: "PT10S"
                delete:
                    frequent: true
                    enabled: true
                    olderThan: "PT10S"
    datedjourney:
      input:
        kafka:
          dto:
            minOperatingDateOffset: -3
            maxOperatingDateOffset: +100
            consumer:
              enabled: true
              topic: ${kafka.dated-journey.name}
              group: ${kafka.dated-journey.group}
              validationErrorResponse: FAIL # FAIL, SKIP, CONTINUE
      lifecycle:
        enabled: true
        daily:
          cron: "0 0 3 * * *"
          schedulerLockName: "dated-journey-daily-001"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        frequent:
          schedulerLockName: "dated-journey-frequent-001"
          fixedRate: "PT45S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        routines:
          tombstone:
            frequent: true
            enabled: true
            olderThan: "PT48H"
          delete:
            frequent: true
            enabled: true
            olderThan: "PT10S"
          publish:
            frequent: true
            enabled: true
            olderThan: "PT10S"
          duplicateSignOn:
            enabled: false
            frequent: true
            arrivalAfter: "PT10M"
            olderThan: "PT90S"
          missingSignOn:
            enabled: true
            frequent: true
            olderThan: "PT30S"
            arrivalAfter: "PT30S"
            excludes:
              operator-contracts:
                - "RUT:OperatorContract:x3" # Oslo_T-Bane (Rammeavtale om banetrafikk)
                - "RUT:OperatorContract:9998" # Kontrakt for test
          missingOperatorAction:
            enabled: true
            frequent: true
            olderThan: "PT30S"
            arrivalAfter: "PT30S"
            excludes:
              operator-contracts:
                - "RUT:OperatorContract:x3" # Oslo_T-Bane (Rammeavtale om banetrafikk)
                - "RUT:OperatorContract:9998" # Kontrakt for test
          republish:
            frequent: false
            enabled: true
          statistics:
            frequent: false
            enabled: true
      output:
        kafka:
          event:
            maxRetires: 10
            producer:
              enabled: false
              topic: ${kafka.journey-event.name}
          entity:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.dated-journey.name}
      logging:
        logOutputOk: true
        logOutputSkip: true
      toggles:
        omitEnabled: true
        callOmitEnabled: true
        cancellationEnabled: true
        callCancellationEnabled: true
        callInterchangeEnabled: true
        useExternalJourneyRefV2: false
        createAdHocDeadRuns: true
    assignmentjourney:
      lifecycle:
        enabled: true
        daily:
          cron: "0 0 3 * * *"
          schedulerLockName: "assignment-journey-daily-001"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
        frequent:
          schedulerLockName: "assignment-journey-frequent-001"
          fixedRate: "PT45S"
          initialDelay: "PT60S"
          lockAtMostFor: "3m"
          lockAtLeastFor: "30s"
          routines:
            tombstone:
              frequent: true
              enabled: true
              olderThan: "PT48H"
            publish:
              frequent: true
              enabled: true
              olderThan: "PT10S"
            republish:
              frequent: false
              enabled: true
      output:
        kafka:
          dto:
            maxRetires: 10
            producer:
              enabled: true
              topic: ${kafka.assignment-journey.name}
      logging:
          logOutputOk: true
          logOutputSkip: true
    vehicle-api:
      input:
        kafka:
          entity:
            consumer:
              enabled: true
              topic: ${kafka.entity-vehicle-api.name}
              group: ${kafka.entity-vehicle-api.group}
integrations:
  traffic-portal-api:
    baseUrl: ${TRAFFIC_PORTAL_API_URL}
    xApiKey: ${TRAFFIC_PORTAL_API_KEY}
    readTimeoutSeconds: 30
