package no.ruter.tranop.app.event.journey.lifecycle

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = JourneyEventLifeCycleConfig.CONF_PREFIX)
class JourneyEventLifeCycleConfig : AbstractLifeCycleConfig(CONF_PREFIX) {
    companion object {
        const val CONF_PREFIX = "${JourneyEventConfig.CONF_PREFIX}.lifecycle"
    }

    var types: List<String> = emptyList()
}
