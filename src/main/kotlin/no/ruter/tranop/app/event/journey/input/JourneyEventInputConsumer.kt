package no.ruter.tranop.app.event.journey.input

import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaConsumer
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConsumerConfig
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.journey.event.dto.kafka.DTOJourneyEventDeserializer
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    name = [
        KafkaConsumerConfig.CONF_KEY_ENABLED,
        "${JourneyEventInputConsumer.CONF_PREFIX}.enabled",
    ],
    havingValue = "true",
)
class JourneyEventInputConsumer(
    insightService: InsightService,
    val journeyEventService: JourneyEventInputService,
) : AbstractKafkaConsumer(
        channel = journeyEventService.defaultChannel,
        insightService = insightService,
    ) {
    companion object {
        const val CONF_PREFIX = "${JourneyEventConfig.CONF_PREFIX}.input.kafka.dto.consumer"
    }

    private val deserializer = DTOJourneyEventDeserializer()

    @KafkaListener(
        topics = ["\${$CONF_PREFIX.topic}"],
        groupId = "\${$CONF_PREFIX.group}",
    )
    fun listenGroup(
        consumerRecord: ConsumerRecord<String?, ByteArray?>,
        ack: Acknowledgment,
    ) {
        try {
            process(
                record = consumerRecord,
                deserializer = deserializer,
                handler = journeyEventService::processInput,
            )
        } catch (e: Exception) {
            logger.error("Not able to process journey event [${consumerRecord.key()} / ${consumerRecord.headers()}]", e)
        } finally {
            ack.acknowledge()
        }
    }
}
