package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.OperatingWithoutSignOnJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class OperatingWithoutSignonJourneyEventHandler(
    config: OperatingWithoutSignOnJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        event.operatingWithoutSignon?.let { withoutSignon ->
            if (journey.journeyState.journeyDeviationState.operatingWithoutSignon == withoutSignon.active) {
                return null
            }

            journey.journeyState.journeyDeviationState.operatingWithoutSignon = withoutSignon.active
            val type = DTOEventType.OPERATING_WITHOUT_SIGNON

            val metadata = collect(mapOf(), withoutSignon)

            return createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Operating without signon processed [$type / ${journey.ref} / ${withoutSignon.active}]",
                source = "journey-event/operational",
                context = context,
            )
        }
        return null
    }
}
