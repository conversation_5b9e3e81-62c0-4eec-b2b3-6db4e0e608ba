package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.OmissionJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventOmission
import java.time.OffsetDateTime

class OmitJourneyEventHandler(
    config: OmissionJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val omitMetadataFields =
            mapOf(
                DTOEventMetadataKeyType.VEHICLE_TASK_ID to DTOJourneyEventOmission::getVehicleTaskId,
                DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to { event -> event.firstDepartureDateTime?.toOffsetDateTime() },
                DTOEventMetadataKeyType.LAST_ARRIVAL_DATE_TIME to { event -> event.lastArrivalDateTime?.toOffsetDateTime() },
            )
    }

    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        event.omission?.let { omission ->
            val omitted = omission.omitted
            val calls = journey.plan.calls
            val lastArrival = omission.lastArrivalDateTime?.toOffsetDateTime()
            val firstDeparture = omission.firstDepartureDateTime?.toOffsetDateTime()
            val eventCalls = omission.calls

            if (eventCalls.isNullOrEmpty() && lastArrival == null && firstDeparture == null) {
                calls.forEach { it.omitted = omitted }
            } else {
                val callsWithinServiceWindow = calls.filter { it.isWithin(firstDeparture, lastArrival) }
                callsWithinServiceWindow.forEach { it.omitted = omitted }

                val stopPoints = journey.plan.stops.associateBy { it.ref }
                val stopPointCalls =
                    calls
                        .associateWith { call ->
                            stopPoints[call.stopPointRef]
                        }.filter { it.value != null }

                if (!eventCalls.isNullOrEmpty()) {
                    val callsMatching =
                        stopPointCalls
                            .filter { (call, stop) ->
                                val quayRef = stop?.quayRef
                                val arrival = call.plannedArrival?.toOffsetDateTime()
                                val departure = call.plannedDeparture?.toOffsetDateTime()

                                eventCalls.any {
                                    val time = it.time?.toOffsetDateTime()
                                    it.nsrQuayId == quayRef && (time == null || time.isEqual(arrival) || time.isEqual(departure))
                                }
                            }
                    callsMatching.forEach { (call, _) -> call.omitted = omitted }
                }
            }

            val allCallsOmitted = calls.all { it.omitted == true }
            val anyCallOmitted = calls.any { it.omitted == true }
            val noCallsOmitted = calls.none { it.omitted == true }

            journey.omitted = allCallsOmitted

            journey.journeyState?.journeyDeviationState?.apply {
                this.omitted = allCallsOmitted
                this.partiallyOmitted = !allCallsOmitted && anyCallOmitted
            }

            val type =
                when {
                    omitted && allCallsOmitted -> DTOEventType.OMITTED
                    omitted && anyCallOmitted -> DTOEventType.CALLS_OMITTED
                    !omitted && noCallsOmitted -> DTOEventType.UN_OMITTED
                    !omitted && anyCallOmitted -> DTOEventType.CALLS_UN_OMITTED
                    else -> DTOEventType.UNKNOWN
                }

            val metadata = collect(omitMetadataFields, omission)

            return createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Omission processed [$type / ${journey.ref}]",
                source = "journey-event/operational",
                context = context,
            )
        }
        return null
    }

    private fun DTODatedJourneyCall.isWithin(
        start: OffsetDateTime?,
        end: OffsetDateTime?,
    ): Boolean {
        start ?: return false
        end ?: return false
        val tmpArrival = this.plannedArrival?.toOffsetDateTime()
        val tmpDeparture = this.plannedDeparture?.toOffsetDateTime()

        val callArrival = tmpArrival ?: tmpDeparture ?: return false
        val callDeparture = tmpDeparture ?: tmpArrival ?: return false

        return ((start.isBefore(callDeparture) || start.isEqual(callDeparture)) && (end.isAfter(callArrival) || end.isEqual(callDeparture)))
    }
}
