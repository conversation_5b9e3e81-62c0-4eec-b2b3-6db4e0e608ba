package no.ruter.tranop.app.event.journey.input.process

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.config.ConfigToggle
import no.ruter.tranop.app.event.journey.input.JourneyEventHandler
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadata
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import java.time.OffsetDateTime

abstract class AbstractJourneyEventHandler(
    private val toggle: ConfigToggle,
) : JourneyEventHandler {
    fun <T> collect(
        fields: Map<DTOEventMetadataKeyType, (T) -> Any?>,
        obj: T,
    ): List<DTOEventMetadata> =
        fields.mapNotNull { (metadataKey, valueFunction) ->
            valueFunction(obj).let {
                val metadataValue =
                    when (it) {
                        null -> null
                        is OffsetDateTime -> it.toUtcIsoString()
                        is String -> it
                        is Collection<*> -> it.joinToString(", ")
                        else -> it.toString()
                    }

                DTOEventMetadata().apply {
                    this.key = metadataKey
                    this.value = metadataValue
                }
            }
        }

    fun createEvent(
        event: DTOJourneyEvent,
        type: DTOEventType?,
        metadata: List<DTOEventMetadata>,
        msg: String? = null,
        source: String? = null,
        context: ProcessingContext,
    ): DTOEvent? {
        if (!toggle.enabled) {
            context.addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "disabled",
                desc = "$type disabled",
            )
            return null
        }
        val header = event.header ?: null
        val status = event.status ?: null
        val descriptionParts =
            listOfNotNull(
                type?.value?.let { "[$it]" },
                msg ?: type?.description,
                status?.let { "${status.code}: ${status.description}" },
                source?.let { "[$it]" },
            )

        val description = descriptionParts.joinToString(" ")
        return DTOEvent().apply {
            this.id = event.id
            this.type = type
            this.traceId = header?.traceId
            this.timestamp = header?.messageTimestamp
            this.description = description
            this.metadata = metadata
            this.source = source ?: "assignment/internal"
        }
    }
}
