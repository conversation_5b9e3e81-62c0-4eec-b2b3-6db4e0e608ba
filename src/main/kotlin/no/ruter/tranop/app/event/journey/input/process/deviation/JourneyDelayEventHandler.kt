package no.ruter.tranop.app.event.journey.input.process.deviation

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventDelayReason
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventDelay
import java.time.OffsetDateTime

class JourneyDelayEventHandler(
    config: JourneyEventConfig.DelayJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val delayMetadataFields =
            mapOf<DTOEventMetadataKeyType, (DTOJourneyEventDelay) -> Any?>(
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_CASE_KEY_V2_REF to DTOJourneyEventDelay::getEntityTrafficCaseKeyV2Ref,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_EVENT_KEY_V1_REF to DTOJourneyEventDelay::getEntityTrafficEventKeyV1Ref,
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_SITUATION_KEY_V2_REF to DTOJourneyEventDelay::getEntityTrafficSituationKeyV2Ref,
                DTOEventMetadataKeyType.DELAY_MINUTES to DTOJourneyEventDelay::getDelayMinutes,
            )
    }

    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? =
        event.delay?.let { delay ->
            val metadata = collect(delayMetadataFields, delay)
            val type =
                when (delay.reason) {
                    DTOJourneyEventDelayReason.DELAYED -> DTOEventType.DELAYED
                    DTOJourneyEventDelayReason.UNDELAYED -> DTOEventType.UNDELAYED
                    else -> DTOEventType.UNKNOWN
                }

            delay.delayMinutes?.let { delayMinutes ->
                journey.plan.calls.forEach { call ->
                    call.plannedArrival?.toOffsetDateTime()?.let { time ->
                        call.apply {
                            expectedArrival = updateTime(delayMinutes, time)
                        }
                    }

                    call.plannedDeparture?.toOffsetDateTime()?.let { time ->
                        call.apply {
                            expectedDeparture = updateTime(delayMinutes, time)
                        }
                    }
                }
            } ?: return null

            createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Delay event",
                source = "journey-event/operational",
                context = context,
            )
        }

    private fun updateTime(
        delayMinutes: Int,
        time: OffsetDateTime,
    ): String? =
        if (delayMinutes == 0) {
            null
        } else {
            time.plusMinutes(delayMinutes.toLong()).toUtcIsoString()
        }
}
