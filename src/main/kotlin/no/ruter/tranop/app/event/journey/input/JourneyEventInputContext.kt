package no.ruter.tranop.app.event.journey.input

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.toStatus
import no.ruter.tranop.app.event.journey.db.InternalJourneyEvent
import no.ruter.tranop.assignment.dto.model.value.DTOJourneyEventAssignedReason
import no.ruter.tranop.common.dto.metadata
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.dated.dto.firstDeparture
import no.ruter.tranop.journey.dated.dto.lastArrival
import no.ruter.tranop.journey.dated.dto.metadata
import no.ruter.tranop.journey.event.dto.common.model.value.DTOValueType
import java.time.OffsetDateTime
import java.util.Objects

class JourneyEventInputContext(
    override val channel: DataChannel,
    val received: OffsetDateTime,
    val internalEvent: InternalJourneyEvent,
    val journey: DTODatedJourney? = null,
    override val recordMetadata: Boolean = false,
) : ProcessingContext() {
    val event = internalEvent.event

    override val traceId: String?
        get() = internalEvent.event.header?.traceId

    val ref: String = MapperUtils.ref(internalEvent.event)

    override var exception: Exception? = null

    override val skip = false
    override val metadata: Map<String, Any?>
        get() =
            buildMap {
                putAll(journey?.metadata() ?: emptyMap())
                putAll(eventHeader.metadata())
                put(LogKey.MESSAGE_SUBJECT, channel.subject)
                putAll(metadata())
            }

    private fun metadata(): Map<String, Any?> {
        val meta =
            linkedMapOf(
                "type" to journeyEventType.name,
                "reason" to reason?.value,
                "code" to statusDetails.toStatus().code?.value,
                LogKey.VEHICLE_REF to vehicleRef,
                LogKey.VEHICLE_TASK_ID to vehicleTaskId,
                LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF to datedJourneyV2Ref,
                LogKey.ENTITY_ASSIGNMENT_KEY_V1_REF to assignmentRef,
                "operator_mqtt_id" to operatorId,
                LogKey.QUAY_REF to quayRefs,
                "cancelled" to cancelled,
                "callsCancelled" to callsCancelled,
                "delay" to delay,
                "omitted" to omitted,
            ).filterValues { it != null }

        return meta
    }

    override val insightKey: String
        get() = channel.insightKey

    override val summary: String
        get() =
            metadata()
                .values
                .asSequence()
                .filterNotNull()
                .map(Objects::toString)
                .map(String::trim)
                .joinToString(" / ", transform = String::trim)

    val eventHeader = event.header
    val datedJourneyV2Ref = event.entityDatedJourneyKeyV2Ref ?: journey?.ref

    val omitted = event.omission
    val assigned = event.assigned
    val unassigned = event.unassigned
    val updated = event.updated
    val cancellation = event.cancellation
    val planned = event.planned
    val delay = event.delay
    val missingSignOn = event.missingSignOn
    val missingOperatorAction = event.missingOperatorAction
    val duplicateSignOn = event.duplicateSignOn
    val operatingWithoutSignon = event.operatingWithoutSignon
    val contingencyVehiclePlanned = event.contingencyVehiclePlanned

    val operatorId = omitted?.mqttOperatorId

    // TODO: Fix up, this is horrible!!!
    val journeyEventType
        get() =
            when {
                omitted != null -> JourneyEventType.OMITTED
                assigned != null -> JourneyEventType.ASSIGNED
                unassigned != null -> JourneyEventType.UNASSIGNED
                updated != null -> JourneyEventType.UPDATED
                cancellation != null -> JourneyEventType.CANCELLATION
                planned != null -> JourneyEventType.PLANNED
                delay != null -> JourneyEventType.DELAY
                missingOperatorAction != null &&
                    missingOperatorAction.resolved != null &&
                    missingOperatorAction.resolved == true -> JourneyEventType.MISSING_OPERATOR_ACTION_RESOLVED
                missingOperatorAction != null -> JourneyEventType.MISSING_OPERATOR_ACTION
                missingSignOn != null &&
                    missingSignOn.resolved != null &&
                    missingSignOn.resolved == true -> JourneyEventType.MISSING_SIGN_ON_RESOLVED
                missingSignOn != null -> JourneyEventType.MISSING_SIGN_ON
                duplicateSignOn != null &&
                    duplicateSignOn.resolved != null &&
                    duplicateSignOn.resolved == true -> JourneyEventType.DUPLICATE_SIGN_ON_RESOLVED

                duplicateSignOn != null -> JourneyEventType.DUPLICATE_SIGN_ON
                operatingWithoutSignon != null -> JourneyEventType.OPERATING_WITHOUT_SIGNON
                contingencyVehiclePlanned != null -> JourneyEventType.CONTINGENCY_VEHICLE_PLANNED

                else -> JourneyEventType.UNKNOWN
            }
    val reason: DTOValueType?
        get() =
            when {
                omitted != null -> omitted.reason
                assigned != null -> assigned.reason
                unassigned != null -> unassigned.reason
                updated != null -> updated.reason
                cancellation != null -> cancellation.reason
                planned != null -> planned.reason
                delay != null -> delay.reason
                missingSignOn != null -> missingSignOn.reason
                duplicateSignOn != null -> duplicateSignOn.reason
                missingOperatorAction != null -> missingOperatorAction.reason
                else -> DTOJourneyEventAssignedReason.UNKNOWN
            }
    val vehicleTaskId = omitted?.vehicleTaskId ?: journey?.journeyReferences?.vehicleTaskRef
    val lastArrivalDateTime =
        when {
            omitted != null -> omitted.lastArrivalDateTime
            assigned != null -> assigned.serviceWindow?.lastArrivalDateTime
            updated != null -> updated.serviceWindow?.lastArrivalDateTime
            else -> journey?.lastArrival()
        }
    val firstDepartureDateTime =
        when {
            omitted != null -> omitted.firstDepartureDateTime
            assigned != null -> assigned.serviceWindow?.firstDepartureDateTime
            updated != null -> updated.serviceWindow?.firstDepartureDateTime
            else -> journey?.firstDeparture()
        }
    val vehicleRef =
        when {
            assigned != null -> assigned.vehicleRef
            unassigned != null -> unassigned.vehicleRef
            updated != null -> updated.vehicleRef
            else -> null
        }
    val assignmentRef =
        when {
            assigned != null -> assigned.assignmentRef
            unassigned != null -> unassigned.assignmentRef
            updated != null -> updated.assignmentRef
            else -> null
        }
    val quayRefs =
        when {
            cancellation != null -> cancellation.quayRefs
            else -> null
        }
    val cancelled =
        when {
            cancellation != null -> cancellation.cancelled
            else -> journey?.cancelled
        }
    val callsCancelled =
        when {
            cancellation != null -> cancellation.calls
            else -> null
        }
}
