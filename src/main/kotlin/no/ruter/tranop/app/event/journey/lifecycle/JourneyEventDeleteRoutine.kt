package no.ruter.tranop.app.event.journey.lifecycle

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.event.journey.db.JourneyEventRepository
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@Service
class JourneyEventDeleteRoutine(
    insightService: InsightService,
    appInfoProperties: AppInfoProperties,
    val journeyEventRepository: JourneyEventRepository,
    journeyEventLifeCycleConfig: JourneyEventLifeCycleConfig,
) : JourneyEventBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_DELETE,
        insightService = insightService,
        journeyEventLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        var res = 0
        try {
            res += journeyEventRepository.deleteUnpublished(olderThan = olderThan)
        } catch (e: Exception) {
            handleException(e = e)
        }
        return res
    }
}
