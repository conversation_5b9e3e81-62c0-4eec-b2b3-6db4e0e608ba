package no.ruter.tranop.app.event.journey.input.process.mitigation

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.app.plan.journey.doesNotContain
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent

class MitigationJourneyEventHandler(
    config: JourneyEventConfig.MitigationJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        const val CONTINGENCY_OPERATOR_NAME = "BEREDSKAPSBUSS"
    }

    private val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? {
        val journeyState = journey.journeyState

        event.contingencyVehiclePlanned?.let { contingency ->
            if (journeyState == null) {
                log.warn("Journey state is null for journey ref: ${journey.ref}")
                return null
            }

            val journeyMitigationState = journeyState.journeyMitigationState

            if (journeyMitigationState?.contingencyVehiclePlanned == contingency.planned) {
                return null
            }

            journeyMitigationState.contingencyVehiclePlanned = contingency.planned

            val journeyOperators = journey.operators
            contingency.operatorRefs.forEach { operatorRef ->
                if (journeyOperators.doesNotContain(operatorRef)) {
                    journeyOperators.add(DTOOperator().withOperatorRef(operatorRef).withName(CONTINGENCY_OPERATOR_NAME))
                }
            }

            val type = DTOEventType.CONTINGENCY_VEHICLE_PLANNED

            val metadata = collect(mapOf(), contingency)

            return createEvent(
                event = event,
                type = type,
                metadata = metadata,
                msg = "Mitigation processed [$type / ${journey.ref}]",
                source = "journey-event/operational",
                context = context,
            )
        }

        return null
    }
}
