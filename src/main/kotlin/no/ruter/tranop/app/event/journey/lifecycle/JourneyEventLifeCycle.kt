package no.ruter.tranop.app.event.journey.lifecycle

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycle
import no.ruter.tranop.app.common.time.TimeService
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
@ConditionalOnProperty(
    prefix = JourneyEventLifeCycleConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class JourneyEventLifeCycle(
    val timeService: TimeService,
    val insightService: InsightService,
    deleteRoutine: JourneyEventDeleteRoutine,
) : AbstractLifeCycle(routines = listOf(deleteRoutine)) {
    companion object {
        const val DAY_PREFIX = "${JourneyEventLifeCycleConfig.CONF_PREFIX}.daily"
        const val FREQ_PREFIX = "${JourneyEventLifeCycleConfig.CONF_PREFIX}.frequent"
    }

    @Scheduled(
        cron = "\${$DAY_PREFIX.cron}",
    )
    fun runDaily() {
        execute(
            started = timeService.now(),
            daily = true,
        )
    }

    @Scheduled(
        fixedRateString = "\${$FREQ_PREFIX.fixedRate}",
        initialDelayString = "\${$FREQ_PREFIX.initialDelay}",
    )
    @SchedulerLock(
        name = "\${$FREQ_PREFIX.schedulerLockName}",
        lockAtMostFor = "\${$FREQ_PREFIX.lockAtMostFor}",
        lockAtLeastFor = "\${$FREQ_PREFIX.lockAtLeastFor}",
    )
    fun runFrequent() {
        execute(
            started = timeService.now(),
            frequent = true,
        )
    }

    fun runAll() {
        runDaily()
        runFrequent()
    }
}
