package no.ruter.tranop.app.event.journey.output

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = JourneyEventOutboxStreamingConfig.CONF_PREFIX)
class JourneyEventOutboxStreamingConfig {
    var batchSize = 1000
    var enabled = false
    var retryCount = 5

    companion object {
        const val CONF_PREFIX = "app.config.outbox.snowflake-streaming-journey-event"
    }
}
