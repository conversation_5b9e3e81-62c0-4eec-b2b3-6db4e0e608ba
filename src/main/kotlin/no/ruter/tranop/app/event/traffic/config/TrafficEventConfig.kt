package no.ruter.tranop.app.event.traffic.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = TrafficEventConfig.CONF_PREFIX)
class TrafficEventConfig {
    companion object {
        const val CONF_PREFIX = "app.config.traffic-event"
    }

    lateinit var delay: TrafficEventDelayConfig
    lateinit var cancellation: TrafficEventCancellationConfig
    lateinit var mitigation: TrafficEventMitigationConfig
    lateinit var operatingWithoutSignon: TrafficEventOperatingWithoutSignonConfig
    lateinit var organizedRailReplacementVehicles: TrafficEventOrganizedRailReplacementVehiclesConfig

    class TrafficEventCancellationConfig {
        var enabled: Boolean = true
    }

    class TrafficEventDelayConfig {
        var enabled: Boolean = false
    }

    class TrafficEventMitigationConfig {
        var enabled: Boolean = false
    }

    class TrafficEventOperatingWithoutSignonConfig {
        var enabled: Boolean = false
    }

    class TrafficEventOrganizedRailReplacementVehiclesConfig {
        var enabled: Boolean = false
    }
}
