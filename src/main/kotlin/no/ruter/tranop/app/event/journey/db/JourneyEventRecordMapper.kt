package no.ruter.tranop.app.event.journey.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.JourneyEventRecord
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import org.jooq.JSON
import org.jooq.RecordMapper

class JourneyEventRecordMapper(
    private val appInfoProperties: AppInfoProperties,
    private val timeService: TimeService,
) : RecordMapper<JourneyEventRecord, InternalJourneyEvent> {
    override fun map(record: JourneyEventRecord): InternalJourneyEvent {
        val json = record.jsonData
        val event = JsonUtils.toObject(json?.data(), DTOJourneyEvent::class.java)
        return InternalJourneyEvent(
            event = event,
            record = record,
        )
    }

    fun update(
        record: JourneyEventRecord,
        event: DTOJourneyEvent,
        context: JourneyEventInputContext,
    ) {
        val creator = appInfoProperties.name
        val now = timeService.now()
        record.ref = context.ref
        event.id = context.ref
        record.datedJourneyV2Ref = event.entityDatedJourneyKeyV2Ref

        record.reason = context.reason?.value()
        record.type = context.journeyEventType.name

        record.assignmentRef = event.assigned?.assignmentRef ?: event.unassigned?.assignmentRef

        // TODO: The update logic is similar for all database kinds. Try to gather the logic.
        record.jsonData = JSON.valueOf(JsonUtils.toJson(event))

        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
        }
        record.modifiedAt = now
        record.modifiedBy = creator
    }
}
