package no.ruter.tranop.app.event.journey.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.app.event.journey.input.JourneyEventInputContext
import no.ruter.tranop.dated.journey.bi.BIEventMetadataKeyType
import no.ruter.tranop.journey.event.bi.model.BIEventMetadata
import no.ruter.tranop.journey.event.bi.model.BIJourneyEvent
import org.springframework.stereotype.Service
import java.time.Instant.now

/**
 * Client for streaming ingestion of journey events into Snowflake.
 *
 * @property config Configuration for streaming ingest.
 * @property clientFactory Factory for creating Snowflake clients and channels.
 */
@Service
class JourneyEventSnowflakeIngestClient(
    config: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<BIJourneyEvent>(
        config,
        clientFactory,
        CLIENT_BUILDER_NAME,
        CHANNEL_BUILDER_NAME,
    ) {
    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_EVENTS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_JOURNEY_EVENT_CHANNEL"
    }

    override fun BIJourneyEvent.toIngestMap(): Map<String, Any> =
        // TODO lag konstanter utifra disse
        mapOf(
            "ASSIGNMENT_REF" to assignmentRef,
            "CREATED_AT" to createdAt,
            "DATED_JOURNEY_V2_REF" to datedJourneyV2Ref,
            "METADATA" to JsonUtils.toJson(metadata.associate { it.key to it.value }),
            "REF" to ref,
            "TYPE" to type,
        )
}

fun JourneyEventInputContext.toBIJourneyEvent(): BIJourneyEvent =
    BIJourneyEvent(
        assignmentRef,
        now().toString(),
        datedJourneyV2Ref,
        metadata.toBIEventMetadataList(),
        ref,
        journeyEventType.name,
    )

private fun Map<String, Any?>.toBIEventMetadataList(): List<BIEventMetadata> =
    mapNotNull { (key, value) ->
        BIEventMetadata(BIEventMetadataKeyType.of(key), value).takeIf {
            BIEventMetadataKeyType.ALL.contains(it.key)
        }
    }
