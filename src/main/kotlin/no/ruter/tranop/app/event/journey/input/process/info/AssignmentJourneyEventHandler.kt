package no.ruter.tranop.app.event.journey.input.process.info

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.AssignmentEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventAssigned
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventUnassigned

class AssignmentJourneyEventHandler(
    config: AssignmentEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val unassignedMetadataFields =
            mapOf(
                DTOEventMetadataKeyType.VEHICLE_REF to DTOJourneyEventUnassigned::getVehicleRef,
                DTOEventMetadataKeyType.ASSIGNMENT_REF to DTOJourneyEventUnassigned::getAssignmentRef,
                DTOEventMetadataKeyType.ASSIGNMENT_CODE to { event -> event.reason?.value },
            )

        private val assignedMetadataFields =
            mapOf(
                DTOEventMetadataKeyType.VEHICLE_REF to DTOJourneyEventAssigned::getVehicleRef,
                DTOEventMetadataKeyType.ASSIGNMENT_REF to DTOJourneyEventAssigned::getAssignmentRef,
                DTOEventMetadataKeyType.ASSIGNMENT_CODE to { event -> event.reason?.value },
                DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to
                    { event -> event.serviceWindow?.firstDepartureDateTime?.toOffsetDateTime() },
                DTOEventMetadataKeyType.LAST_ARRIVAL_DATE_TIME to
                    { event -> event.serviceWindow?.lastArrivalDateTime?.toOffsetDateTime() },
            )
    }

    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? = handleAssigned(context, event) ?: handleUnAssigned(context, event)

    private fun handleUnAssigned(
        context: ProcessingContext,
        event: DTOJourneyEvent,
    ): DTOEvent? =
        event.unassigned?.let { unassigned ->
            val type = DTOEventType.VEHICLE_UNASSIGNED
            val metadata = collect(unassignedMetadataFields, unassigned)
            createEvent(event, type, metadata, "A vehicle was unassigned from the journey", context = context)
        }

    private fun handleAssigned(
        context: ProcessingContext,
        event: DTOJourneyEvent,
    ): DTOEvent? =
        event.assigned?.let { assigned ->
            val type = DTOEventType.VEHICLE_ASSIGNED
            val metadata = collect(assignedMetadataFields, assigned)
            createEvent(event, type, metadata, "A vehicle was assigned to the journey", context = context)
        }
}
