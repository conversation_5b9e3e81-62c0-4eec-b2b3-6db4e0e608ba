package no.ruter.tranop.app.event.journey.input.process.info

import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.event.journey.config.JourneyEventConfig.MissingOperatorActionJourneyEventConfig
import no.ruter.tranop.app.event.journey.input.process.AbstractJourneyEventHandler
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEventMissingOperatorAction

class MissingOperatorActionJourneyEventHandler(
    config: MissingOperatorActionJourneyEventConfig,
) : AbstractJourneyEventHandler(config) {
    companion object {
        private val metaFields =
            mapOf(
                DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to DTOJourneyEventMissingOperatorAction::getDepartureDateTime,
                DTOEventMetadataKeyType.TIME_BOUNDARY to DTOJourneyEventMissingOperatorAction::getThreshold,
            )
    }

    override fun handleJourneyEvent(
        context: ProcessingContext,
        event: DTOJourneyEvent,
        journey: DTODatedJourney,
    ): DTOEvent? =
        event.missingOperatorAction?.let {
            val type =
                if (it.resolved != null && it.resolved) {
                    DTOEventType.MISSING_OPERATOR_ACTION_RESOLVED
                } else {
                    DTOEventType.MISSING_OPERATOR_ACTION
                }

            createEvent(
                event,
                type,
                collect(metaFields, it),
                context = context,
            )
        }
}
