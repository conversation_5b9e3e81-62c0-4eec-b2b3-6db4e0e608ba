package no.ruter.tranop.app.event.journey.output

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.journey.event.bi.model.BIJourneyEvent
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

// TODO: Convert to life-cycle routine?
@Component
@ConditionalOnProperty(
    prefix = JourneyEventOutboxStreamingConfig.Companion.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class JourneyEventOutboxSchedule(
    val timeService: TimeService,
    val outboxService: OutboxService,
    val streamingIngestClient: JourneyEventSnowflakeIngestClient,
    val config: JourneyEventOutboxStreamingConfig,
) {
    companion object {
        const val FREQ_PREFIX = "${JourneyEventOutboxStreamingConfig.Companion.CONF_PREFIX}.frequent"
    }

    private val log = LoggerFactory.Companion.getLogger(javaClass.canonicalName)

    @Scheduled(
        fixedRateString = "\${${FREQ_PREFIX}.fixedRate}",
        initialDelayString = "\${${FREQ_PREFIX}.initialDelay}",
    )
    @SchedulerLock(
        name = "\${${FREQ_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${FREQ_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${FREQ_PREFIX}.lockAtLeastFor}",
    )
    fun processPendingEvents() {
        val unpublished =
            outboxService.findUnpublished(
                DBOutboxDataType.DATED_JOURNEY_EVENT,
                DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                AbstractQueryBuilder.Pagination(config.batchSize, 0),
                config.retryCount,
            )
        runCatching {
            val (successFullIngestEvents, failedIngestEvents) =
                streamingIngestClient.ingest(
                    unpublished.map { Pair(it.record.ref, JsonUtils.toObject(it.data.payload, BIJourneyEvent::class.java)) },
                )
            log.debug("Successfully ingested ${successFullIngestEvents.size} events, failed to ingest ${failedIngestEvents.size} events.")

            if (successFullIngestEvents.isNotEmpty()) {
                outboxService.markAsPublished(successFullIngestEvents)
            }
            if (failedIngestEvents.isNotEmpty()) {
                outboxService.markAsFailed(
                    unpublished.filter { it ->
                        failedIngestEvents.contains(it.record.ref)
                    },
                )
            }
        }.onFailure { e ->
            log.error("Failed to process pending events, marking as failed", e)
            outboxService.markAsFailed(unpublished)
        }
    }
}
