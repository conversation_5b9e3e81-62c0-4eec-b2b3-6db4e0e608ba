package no.ruter.tranop.app.event.journey.lifecycle

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleRoutine

abstract class JourneyEventBaseRoutine(
    appInfoProperties: AppInfoProperties,
    name: String,
    insightService: InsightService,
    config: AbstractLifeCycleConfig,
) : AbstractLifeCycleRoutine(
        appInfoProperties,
        name,
        insightService,
        config,
        RecordType.DATED_JOURNEY_EVENT.channels.internal,
    )
