package no.ruter.tranop.app.deviationMOVEME.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.AbstractInputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.input.InputMetadata
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.common.dto.metadata
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import java.time.OffsetDateTime

class ServiceDeviationInputContext(
    override val channel: DataChannel,
    private val deviation: DTOServiceDeviation,
    override val ref: String = deviation.ref,
    override val header: DTOMessageHeader? = deviation.header,
    override val received: OffsetDateTime,
    override val events: Collection<InputEvent>,
) : AbstractInputContext() {
    companion object {
        const val REF_PATH = "$.ref"
    }

    override val metrics: Map<String, List<String>>
        get() = emptyMap()
    override val metadata: Map<String, Any?>
        get() = deviation.header.metadata()

    override val inputMetadata =
        InputMetadata().apply {
            this.messageTimestamp = header?.messageTimestamp?.toOffsetDateTime()
            this.receivedTimestamp = header?.receivedTimestamp?.toOffsetDateTime()
            this.expiresTimestamp = header?.expiresTimestamp?.toOffsetDateTime()
            this.traceId = header?.traceId
            this.publisherId = header?.publisherId
            this.ownerId = header?.ownerId
            this.originId = header?.originId
        }

    override val refPath = REF_PATH
}
