package no.ruter.tranop.app.deviationMOVEME.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.output.AbstractOutputContext
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation

class ServiceDeviationEntityOutputContext(
    input: InputMessage<DTOServiceDeviation, ServiceDeviationInputContext>,
) : AbstractOutputContext<DTOServiceDeviation, ServiceDeviationInputContext>(input) {
    override val channel: DataChannel
        get() = input.context.channel

    override val traceId: String?
        get() = input.context.traceId

    override val metadata: Map<String, Any?>
        get() = emptyMap() // TODO maybe implement this in assignment-api?

    override val metrics: Map<String, List<String>>
        get() = emptyMap()
}
