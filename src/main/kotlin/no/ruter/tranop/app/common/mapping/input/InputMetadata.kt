package no.ruter.tranop.app.common.mapping.input

import java.time.OffsetDateTime

class InputMetadata {
    var traceId: String? = null
    var ownerId: String? = null
    var publisherId: String? = null
    var originId: String? = null

    var expiresTimestamp: OffsetDateTime? = null
    var messageTimestamp: OffsetDateTime? = null
    var receivedTimestamp: OffsetDateTime? = null
    var publishedTimestamp: OffsetDateTime? = null
}

data class InputEvent(
    val type: String?,
    val traceId: String?,
    val timestamp: String?,
    val metadata: Map<String, Any?>?,
)
