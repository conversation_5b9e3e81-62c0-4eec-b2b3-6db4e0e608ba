package no.ruter.tranop.app.common.dataflow.opensearch

import org.opensearch.client.opensearch.OpenSearchClient
import org.opensearch.client.transport.OpenSearchTransport
import org.opensearch.client.transport.aws.AwsSdk2Transport
import org.opensearch.client.transport.aws.AwsSdk2TransportOptions
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.http.apache.ApacheHttpClient
import software.amazon.awssdk.regions.Region

@Configuration
class OpenSearchConfig {
    companion object {
        private const val SERVICE_NAME = "aoss"
    }

    @Bean
    fun openSearchClientBean(transport: OpenSearchTransport): OpenSearchClient = OpenSearchClient(transport)

    @Bean
    fun openSearchTransportBean(properties: OpenSearchProperties): OpenSearchTransport {
        val httpClient = ApacheHttpClient.builder().build()
        return AwsSdk2Transport(
            httpClient,
            properties.endpoint,
            SERVICE_NAME,
            Region.of(properties.region),
            AwsSdk2TransportOptions.builder().build(),
        )
    }
}
