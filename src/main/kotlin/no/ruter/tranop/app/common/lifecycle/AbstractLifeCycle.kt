package no.ruter.tranop.app.common.lifecycle

import no.ruter.rdp.logging.LogKey
import no.ruter.rdp.logging.LoggerFactory
import java.time.OffsetDateTime

abstract class AbstractLifeCycle(
    private val routines: List<AbstractLifeCycleRoutine>,
) {
    private val logger = LoggerFactory.getLogger(javaClass)
    private val logMeta = mapOf(LogKey.MESSAGE_SUBJECT to "lifecycle")
    private val lcName = javaClass.simpleName

    fun execute(
        started: OffsetDateTime,
        daily: Boolean = false,
        frequent: Boolean = false,
    ) {
        logger.debug("Starting ${if (frequent) "frequent" else "daily"} lifecycle $lcName", metadata = logMeta)
        execute(
            started = started,
            routines = routines.filter { it.enabled && it.daily == daily && it.frequent == frequent },
        )
        logger.debug("Completed ${if (frequent) "frequent" else "daily"} lifecycle $lcName", metadata = logMeta)
    }

    private fun execute(
        started: OffsetDateTime,
        routines: List<AbstractLifeCycleRoutine>,
    ) {
        routines
            .forEach { routine ->
                execute(routine, started)
            }
    }

    private fun execute(
        routine: AbstractLifeCycleRoutine,
        started: OffsetDateTime,
    ) {
        val specification = "${routine.subject}/${routine.name}"
        try {
            val res = routine.execute(started)
            routine.recordInsight(specification = specification, result = res)
        } catch (e: Exception) {
            routine.handleException(specification = specification, e = e)
        }
    }
}
