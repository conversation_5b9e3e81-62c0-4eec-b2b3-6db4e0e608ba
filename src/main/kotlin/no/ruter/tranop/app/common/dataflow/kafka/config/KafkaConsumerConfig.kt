package no.ruter.tranop.app.common.dataflow.kafka.config

import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.ByteArrayDeserializer
import org.apache.kafka.common.serialization.StringDeserializer
import org.apache.kafka.streams.StreamsConfig
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory
import org.springframework.kafka.listener.ContainerProperties

@ConditionalOnProperty(
    name = [KafkaConsumerConfig.CONF_KEY_ENABLED],
    havingValue = "true",
)
@Configuration
class KafkaConsumerConfig(
    kafkaConfigService: KafkaConfigService,
) {
    companion object {
        private const val CONF_PREFIX = "kafka.consumers"

        const val CONF_KEY_ENABLED = "$CONF_PREFIX.enabled"
    }

    final val config = kafkaConfigService.configuration.configuration
    val bootstrapServers = getBootstrapServers(config)

    private fun getBootstrapServers(conf: Map<String, Any?>): String =
        conf[StreamsConfig.BOOTSTRAP_SERVERS_CONFIG]?.let {
            val servers = it as String
            if (servers.contains("localhost")) {
                throw IllegalStateException("localhost not permitted for ${StreamsConfig.BOOTSTRAP_SERVERS_CONFIG}")
            } else {
                servers
            }
        } ?: throw IllegalStateException("No bootstrap servers defined for consumers")

    @Bean
    fun consumerFactory(): ConsumerFactory<String?, ByteArray?> {
        val props: MutableMap<String, Any> = HashMap()
        props[ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG] = bootstrapServers
        props[ConsumerConfig.GROUP_ID_CONFIG] = "default-consumer-group"
        props[ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG] = StringDeserializer::class.java
        props[ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG] = ByteArrayDeserializer::class.java
        props[ConsumerConfig.AUTO_OFFSET_RESET_CONFIG] = "earliest"
        return DefaultKafkaConsumerFactory(props)
    }

    @Bean
    fun kafkaListenerContainerFactory(): ConcurrentKafkaListenerContainerFactory<String?, ByteArray?>? {
        val factory = ConcurrentKafkaListenerContainerFactory<String?, ByteArray?>()
        factory.consumerFactory = consumerFactory()
        factory.containerProperties.ackMode = ContainerProperties.AckMode.MANUAL_IMMEDIATE
        factory.containerProperties.isSyncCommits = true
        return factory
    }
}
