package no.ruter.tranop.app.common.db.record.base

import org.jooq.Table
import org.jooq.TableField
import org.jooq.UpdatableRecord
import java.time.OffsetDateTime

open class BaseRecordTableMetadata<R : UpdatableRecord<R>, A : Table<R>>(
    val table: A,
) {
    val id: TableField<R, Int> = required(table, name = "id")
    val ref: TableField<R, String> = required(table, name = "ref")
    val revision: TableField<R, Int>? = optional<Int, R, A, TableField<R, Int>>(table, name = "revision")

    val created: TableField<R, OffsetDateTime> = required(table, name = "created_at")
    val modified: TableField<R, OffsetDateTime>? =
        optional<OffsetDateTime, R, A, TableField<R, OffsetDateTime>>(table, name = "modified_at")

    val deleted: TableField<R, OffsetDateTime>? = optional<OffsetDateTime, R, A, TableField<R, OffsetDateTime>>(table, name = "deleted_at")
    val deletedRevision: TableField<R, Int>? = optional<Int, R, A, TableField<R, Int>>(table, name = "deleted_revision")

    // TODO: Support multiple operator ids on a record? Only meaningful for journeys...?
    val operatorId: TableField<R, String?>? = optional<String?, R, A, TableField<R, String?>>(table, name = "operator_id")
    val authorityId: TableField<R, String?>? = optional<String?, R, A, TableField<R, String?>>(table, name = "authority_id")

    companion object {
        @JvmStatic
        protected inline fun <
            T,
            R : UpdatableRecord<R>,
            A : Table<R>,
            reified F : TableField<R, T>,
        > required(
            table: A,
            name: String,
        ): F = table.field(name) as? F ?: throw IllegalStateException("Missing field '$name' in table ${table.name}.")

        @JvmStatic
        protected inline fun <
            T,
            R : UpdatableRecord<R>,
            A : Table<R>,
            reified F : TableField<R, T>,
        > optional(
            table: A,
            name: String,
        ): F? = table.field(name) as? F
    }
}
