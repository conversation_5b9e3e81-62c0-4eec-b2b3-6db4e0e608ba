package no.ruter.tranop.app.common.dataflow.opensearch

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "opensearch")
data class OpenSearchProperties(
    val endpoint: String,
    val region: String?,
    val journeyIndexName: String,
    val useBatchedPublish: Boolean = true,
    val publishDaysBeforeOperatingDate: Long = 1L,
    val publishDaysAfterOperatingDate: Long = 2L,
    val enabled: Boolean = true,
)
