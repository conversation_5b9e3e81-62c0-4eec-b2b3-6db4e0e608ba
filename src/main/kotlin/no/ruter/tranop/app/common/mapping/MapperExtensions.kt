package no.ruter.tranop.app.common.mapping

import no.ruter.rdp.common.json.JsonUtils
import java.math.BigDecimal

fun ByteArray.toHex() = joinToString(separator = "") { byte -> "%02x".format(byte) }

fun String.toRoundedBigDecimal(): BigDecimal? = this.toBigDecimalOrNull()?.toRoundedBigDecimal()

fun BigDecimal.toRoundedBigDecimal(): BigDecimal =
    this.setScale(MapperUtils.DEFAULT_BD_SCALE, MapperUtils.DEFAULT_ROUNDING_MODE).stripTrailingZeros()

inline fun <reified T : Any?> T?.deepCopy() =
    try {
        JsonUtils.toObject(JsonUtils.toJson(this), T::class.java)
    } catch (e: Exception) {
        null
    }

fun Map<String, Any?>.ensureNonNullValues(): Map<String, Any> =
    buildMap { for ((k, v) in this@ensureNonNullValues) if (v != null) put(k, v) }

fun MutableCollection<String>.addIfNotEmpty(value: String?) {
    if (!value.isNullOrEmpty()) {
        add(value)
    }
}
