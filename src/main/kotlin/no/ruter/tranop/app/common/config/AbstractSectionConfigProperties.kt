package no.ruter.tranop.app.common.config

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.config.input.InputConfigProperties
import no.ruter.tranop.app.common.config.output.OutputConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaInputConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaOutputConfigProperties

abstract class AbstractSectionConfigProperties {
    val input: InputConfigProperties = InputConfigProperties()
    val output: OutputConfigProperties = OutputConfigProperties()
    lateinit var logging: LoggingProperties

    data class LoggingProperties(
        val logOutputOk: Boolean = true,
        val logOutputSkip: Boolean = true,
    )

    fun getKafkaInputConfig(key: String): KafkaInputConfigProperties = input.kafka?.get(key) ?: throw InputException(key, input)

    fun getKafkaOutputConfig(key: String): KafkaOutputConfigProperties = output.kafka?.get(key) ?: throw OutputException(key, output)

    private open class ConfigurationException(
        type: String,
        spec: String,
        props: Any?,
    ) : Exception("$type is not configured for $spec: ${JsonUtils.toJson(props)} ")

    private class InputException(
        type: String,
        props: Any?,
    ) : ConfigurationException(type, "input", props)

    private class OutputException(
        type: String,
        props: Any?,
    ) : ConfigurationException(type, "output", props)

    companion object {
        const val CONFIG_KEY_DTO = "dto"
        const val CONFIG_KEY_ENTITY = "entity"
    }
}
