package no.ruter.tranop.app.common.insight

enum class ErrorType(
    val value: String,
) {
    DB_READ(value = "db-read"),
    DB_STORE(value = "db-store"),
    DB_DELETE(value = "db-delete"),

    TOO_LARGE(value = "too-large"),
    PROCESSING(value = "processing"),
    MAPPING(value = "mapping"),
    JOURNEY_EVENT_FROM_ATTEMPT(value = "journey-event-from-attempt"),
    MAPPING_ASSIGNMENT(value = "mapping-assignment"),
    KAFKA_PRODUCE(value = "kafka-produce"),
    OS_DELETE(value = "os-delete"),
    ;

    override fun toString(): String = value
}
