package no.ruter.tranop.app.common.dataflow.snowflake

import org.springframework.boot.context.properties.ConfigurationProperties

// TODO: Split this into client properties (user, url, private key, ...?) and target properties (schema name, table name, ...?)
@ConfigurationProperties(prefix = SnowflakeClientProperties.KEY_PREFIX)
class SnowflakeClientProperties(
    val enabled: Boolean = false,
    val user: String,
    val url: String,
    val dbName: String,
    val schemaName: String,
    val tableName: String,
    val privateKey: String,
) {
    companion object {
        const val KEY_PREFIX = "app.config.streaming-ingest"
    }
}
