package no.ruter.tranop.app.common.dataflow.snowflake

import net.snowflake.ingest.streaming.OpenChannelRequest
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestChannel
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestClient
import net.snowflake.ingest.streaming.SnowflakeStreamingIngestClientFactory
import org.springframework.stereotype.Component
import java.util.Properties
import java.util.UUID

@Component
class SnowflakeClientFactory {
    fun createClient(
        name: String,
        config: SnowflakeClientProperties,
    ): SnowflakeStreamingIngestClient =
        SnowflakeStreamingIngestClientFactory
            .builder(name)
            .setProperties(
                Properties().apply {
                    setProperty("user", config.user)
                    setProperty("url", config.url)
                    setProperty("private_key", config.privateKey)
                },
            ).build()

    fun createChannel(
        name: String,
        client: SnowflakeStreamingIngestClient,
        config: SnowflakeClientProperties,
    ): SnowflakeStreamingIngestChannel =
        client.openChannel(
            OpenChannelRequest
                .builder("$name-${UUID.randomUUID()}")
                .setDBName(config.dbName)
                .setSchemaName(config.schemaName)
                .setTableName(config.tableName)
                .setOnErrorOption(OpenChannelRequest.OnErrorOption.CONTINUE)
                .build(),
        )
}
