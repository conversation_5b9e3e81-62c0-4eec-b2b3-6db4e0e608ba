package no.ruter.tranop.app.common.mapping

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.journey.event.dto.model.DTOJourneyEvent
import java.math.RoundingMode
import java.security.MessageDigest
import java.time.OffsetDateTime
import java.util.UUID

class MapperUtils private constructor() {
    companion object {
        const val OWNER_ID = "RUTER"
        const val UNKNOWN = "UNKNOWN"
        const val TRACE_ID_NONE = "-"
        const val ORIGIN_ID_HASTUS = "HASTUS"
        const val DATED_JOURNEY_V2_REF_PREFIX = "djj-"
        const val DATED_JOURNEY_CALL_REF_PREFIX = "djc-"
        const val DATED_JOURNEY_STOP_POINT_REF_PREFIX = "djs-"
        const val DATED_SERVICE_JOURNEY_PREFIX = "RUT:DatedServiceJourney:"

        val DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP
        const val DEFAULT_BD_SCALE: Int = 2

        fun getRandomUUID(): String = UUID.randomUUID().toString()

        fun randomId(prefix: String? = null): String {
            val uuid = UUID.randomUUID().toString().replace(oldValue = "-", newValue = "")
            return if (prefix == null) uuid else "$prefix$uuid"
        }

        fun <T> hashId(
            o: T,
            prefix: String? = null,
            update: (T) -> T = fun(it: T): T = it,
        ): String {
            val hash = hash(JsonUtils.toJson(update(o)))
            return if (prefix == null) hash else "$prefix$hash"
        }

        fun mapHeader(
            now: OffsetDateTime,
            header: DTOMessageHeader?,
            infoProperties: AppInfoProperties,
        ): DTOMessageHeader {
            val appName = infoProperties.name

            return header?.deepCopy()?.let {
                it.publishedTimestamp = now.toString()
                it.publisherId = appName
                it
            } ?: DTOMessageHeader().apply {
                this.publishedTimestamp = now.toString()
                this.messageTimestamp = now.toString()
                this.receivedTimestamp = now.toString()
                this.ownerId = OWNER_ID
                this.publisherId = appName
                this.traceId = randomId("am-")
                this.originId = appName
            }
        }

        fun ref(event: DTOJourneyEvent): String {
            val tmp =
                event.deepCopy()?.apply {
                    val traceId = header?.traceId ?: randomId("am-")
                    this.header =
                        DTOMessageHeader()
                            .apply { this.traceId = traceId }
                }
            val hash = hash(JsonUtils.toJson(tmp))
            return "jev-$hash"
        }

        fun ref(serviceDeviation: DTOServiceDeviation): String = randomId(prefix = "sd-")

        fun hash(
            str: String,
            algorithm: String = "MD5",
        ): String = MessageDigest.getInstance(algorithm).digest(str.toByteArray(Charsets.UTF_8)).toHex()

        fun destinationDisplayRef(
            publicCode: String?,
            text: String?,
            subText: String?,
        ): String {
            val components = listOf(publicCode, text, subText).joinToString(separator = "") { it ?: "" }
            val hash = hash(components)
            return "ddp-$hash"
        }

        inline fun <T, C : MutableCollection<String>> addRefs(
            target: C,
            source: Collection<T>?,
            refSupplier: (T) -> String?,
        ): C {
            source?.forEach { target.addIfNotEmpty(refSupplier(it)) }
            return target
        }

        // START: Copied from journey-manager
        fun toDatedJourneyStopPointRef(
            quayRef: String?,
            legacyQuayRef: String?,
        ): String =
            createKey(
                prefix = DATED_JOURNEY_STOP_POINT_REF_PREFIX,
                components =
                    listOf(
                        quayRef,
                        legacyQuayRef,
                    ),
            )

        fun toDatedJourneyStopPointCallRef(
            stopPointRef: String?,
            datedJourneyRef: String?,
            visitCount: Int,
        ): String =
            createKey(
                prefix = DATED_JOURNEY_CALL_REF_PREFIX,
                components =
                    listOf(
                        stopPointRef,
                        datedJourneyRef,
                        visitCount.toString(),
                    ),
            )

        fun createDatedJourneyRefV2(
            operatingDate: String?,
            vehicleJourneyId: String?,
            direction: String?,
            lineRef: String?,
        ): String =
            createKey(
                prefix = DATED_JOURNEY_V2_REF_PREFIX,
                components =
                    listOf(
                        operatingDate,
                        vehicleJourneyId,
                        direction,
                        lineRef,
                    ),
            )

        fun toDatedJourneyV2Ref(datedJourney: DTODatedJourney): String {
            val refs = datedJourney.journeyReferences
            return createDatedJourneyRefV2(
                operatingDate = datedJourney.operatingDate,
                vehicleJourneyId = refs?.vehicleJourneyId,
                lineRef = datedJourney.line?.lineRef,
                direction = datedJourney.direction.value,
            )
        }

        fun toDatedServiceJourneyId(datedJourney: DTODatedJourney): String {
            val datedJourneyRefV2 = toDatedJourneyV2Ref(datedJourney)
            return "$DATED_SERVICE_JOURNEY_PREFIX$datedJourneyRefV2"
        }

        fun createKey(
            prefix: String,
            components: Collection<String?>,
        ): String {
            val hash = md5Sum(components.joinToString(separator = ";") { it ?: "null" })
            return "$prefix$hash"
        }

        fun md5Sum(value: String): String {
            val digest = MessageDigest.getInstance("MD5").digest(value.toByteArray())
            return digest.joinToString(separator = "", transform = "%02x"::format)
        }
        // END: copied from journey-manager
    }
}
