package no.ruter.tranop.app.common.mapping.output

import no.ruter.avro.common.EntityHeaderV2
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.insight.Insight
import no.ruter.tranop.app.common.insight.Insight.DataType.OUTPUT
import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.AbstractMessageMapper
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.MappingDetails.Type.INFO
import no.ruter.tranop.app.common.mapping.input.InputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputValidator
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import java.util.Locale

abstract class AbstractOutputMapper<I, IC : InputContext, O, OC : OutputContext<I, IC>>(
    val type: RecordType<*, *>,
    entityVersion: Int,
    config: AbstractSectionConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
) : AbstractMessageMapper(timeService, insightService) {
    private val logConfig = config.logging
    private val outputEntity = type.outputs!!.entity(entityVersion)
    private val publishedOwners: Set<String> = setOf("ruter")

    override val inputDesc: String = "${type.dto} ${type.value}"
    override val outputDesc: String = "${outputEntity.name}V${outputEntity.version} entity"

    abstract fun getOwnerId(value: O?): String?

    abstract fun createContext(input: InputMessage<I, IC>): OC

    abstract fun mapValue(
        input: InputMessage<I, IC>,
        context: OC,
    ): O?

    override fun recordDataError(
        context: InsightContext,
        key: String,
        msg: String?,
        cause: Exception?,
    ) {
        insightService.insightError(
            channel = context.channel,
            classifier = key,
            msg = msg,
            e = cause,
            metadata = context.metadata,
        )
    }

    fun mapOutput(input: InputMessage<I, IC>): OutputMessage<I, IC, O, OC> {
        val context = createContext(input)
        if (input.tombstone) {
            return ok(context, value = null, Insight.OK_TOMBSTONE)
        } else {
            return try {
                val value = mapValue(input, context)
                val errors = recordDataErrors(context)
                val ownerId = getOwnerId(value)
                if (errors) {
                    skip(context, value, Insight.SKIP_DATA_ERROR)
                } else if (isPublishedOwner(ownerId)) {
                    ok(context, value, Insight.OK_MAPPED)
                } else {
                    skip(context, value, Insight.OK_SKIP_UNPUBLISHED_OWNER)
                }
            } catch (e: Exception) {
                recordMappingException(context, e)
                skip(context, value = null, Insight.SKIP_MAPPING_EXCEPTION)
            }
        }
    }

    private fun ok(
        context: OC,
        value: O?,
        insight: String,
    ): OutputMessage<I, IC, O, OC> = createOutputMessage(insight, context, value, ok = true, logEnabled = logConfig.logOutputOk)

    private fun skip(
        context: OC,
        value: O?,
        insight: String,
    ): OutputMessage<I, IC, O, OC> = createOutputMessage(insight, context, value, ok = false, logEnabled = logConfig.logOutputSkip)

    private fun isPublishedOwner(ownerId: String?): Boolean = ownerId?.let { publishedOwners.contains(ownerId.lowercase()) } ?: false

    private fun createOutputMessage(
        insight: String,
        context: OC,
        value: O?,
        ok: Boolean,
        logEnabled: Boolean,
    ): OutputMessage<I, IC, O, OC> {
        insightService.recordDataInsight(type.toString(), insight)
        if (logEnabled) {
            val md = getInsightMetadata(context)
            val lastEventType = context.events.lastOrNull()?.type ?: "none"
            val result = if (ok) "mapped" else "failed mapping"
            val message = "${type.dto} [${context.key}] $result to ${outputEntity.typeName}: $insight [$lastEventType]"
            insightService.recordMessage(
                dataType = OUTPUT,
                insight = insight,
                message = message,
                context = context,
                metadata = md,
                recordType = type,
            )
        }
        return OutputMessage(context, context.key, value, ok = ok)
    }

    private fun getInsightMetadata(context: OC): Map<String, Any?> {
        val key = "entity${outputEntity.typeName}Ref"
        val value = context.key
        return context.metadata.plus(
            mapOf(
                key to value,
                "events" to context.events,
            ),
        )
    }

    protected fun createEntityHeader(
        oc: OC,
        path: String = PATH_ROOT_HEADER,
    ): EntityHeaderV2 {
        val ic = oc.input.context
        val header = ic.header
        val metadata = ic.inputMetadata

        val now = timeService.now()
        val publisherId = insightService.appName
        val publishedTimeStamp = now.toUtcIsoString()
        val receivedTimestamp = ic.inputMetadata.receivedTimestamp ?: publishedTimeStamp
        val eventTimestamp = metadata.messageTimestamp ?: receivedTimestamp
        val traceId = ic.traceId
        val resolvedTraceId =
            if (traceId.isNullOrBlank() || traceId == DatedJourneyInputValidator.KEY_NONE) {
                MapperUtils.randomId("map-")
            } else {
                traceId
            }

        val ownerId =
            header
                ?.ownerId
                .notEmpty(
                    context = oc,
                    path = "$path.ownerId",
                    insight = "missing.owner.id",
                    errorType = INFO,
                )?.uppercase(Locale.ENGLISH) ?: MapperUtils.OWNER_ID
        val originId =
            header
                ?.originId
                .notEmpty(
                    context = oc,
                    path = "$path.originId",
                    insight = "missing.origin.id",
                    errorType = INFO,
                )?.uppercase(Locale.ENGLISH) ?: MapperUtils.ORIGIN_ID_HASTUS

        return EntityHeaderV2
            .newBuilder()
            .setTraceId(resolvedTraceId)
            .setOwnerId(ownerId)
            .setOriginId(originId)
            .setPublisherId(publisherId)
            .setKey(oc.key)
            .setName(outputEntity.name)
            .setVersion(outputEntity.version)
            .setPartition(outputEntity.partition)
            .setEventTimestamp(eventTimestamp.toString())
            .setReceivedTimestamp(receivedTimestamp.toString())
            .setPublishedTimestamp(now.toString())
            .build()
    }
}

fun DTOEvent?.toInsightEvent(): InputEvent =
    InputEvent(
        type = this?.type?.value(),
        traceId = this?.traceId,
        timestamp = this?.timestamp,
        metadata =
            this
                ?.metadata
                ?.filter {
                    (it?.value) != null
                }?.associate {
                    it.key.value to it.value
                } ?: emptyMap(),
    )
