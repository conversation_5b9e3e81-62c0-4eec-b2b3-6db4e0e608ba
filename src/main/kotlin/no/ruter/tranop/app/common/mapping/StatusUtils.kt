package no.ruter.tranop.app.common.mapping

import no.ruter.tranop.assignment.dto.model.DTOAssignmentStatus
import no.ruter.tranop.assignment.dto.model.DTOAssignmentStatusDetails
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.assignment.dto.util.DTOUtils

fun List<DTOAssignmentStatusDetails>.toStatus(): DTOAssignmentStatus {
    val details = this

    val sortedDetails = sortByPriority(details)
    val leadingDetail: DTOAssignmentStatusDetails =
        when {
            details.all { it.code == DTOStatusCode.OK } || details.isEmpty() -> defaultStatusDetail()
            else -> sortedDetails.first()
        }

    return DTOAssignmentStatus()
        .withDescription(leadingDetail.description)
        .withReason(leadingDetail.reason)
        .withCode(leadingDetail.code)
        .withDetails(sortedDetails)
}

private fun defaultStatusDetail() =
    DTOAssignmentStatusDetails()
        .withReason("processed.ok")
        .withCode(DTOStatusCode.OK)

private val orderedStatusFilters =
    listOf(
        ::getInvalid,
        ::getNotSupported,
        ::getErrors,
        ::getTimeouts,
        ::getOksCompensated,
        ::getOks,
    )

private fun sortByPriority(details: List<DTOAssignmentStatusDetails>?): MutableList<DTOAssignmentStatusDetails> {
    if (details.isNullOrEmpty()) {
        return mutableListOf()
    }

    val sortedList = mutableListOf<DTOAssignmentStatusDetails>()
    val tmpDetails = details.toMutableList()

    orderedStatusFilters.forEach {
        val filteredDetails = it.invoke(tmpDetails)
        sortedList.addAll(filteredDetails)
        tmpDetails.removeAll(filteredDetails)
    }

    if (sortedList.isEmpty()) {
        sortedList.add(defaultStatusDetail())
    }

    return sortedList.plus(tmpDetails).toMutableList()
}

private fun getNotSupported(list: List<DTOAssignmentStatusDetails>) = list.filter { DTOStatusCode.ERR_NOT_SUPPORTED == it.code }

private fun getInvalid(list: List<DTOAssignmentStatusDetails>) = list.filter { DTOStatusCode.ERR_VALIDATE == it.code }

private fun getTimeouts(list: List<DTOAssignmentStatusDetails>) = list.filter { DTOStatusCode.ERR_TIMEOUT == it.code }

private fun getErrors(list: List<DTOAssignmentStatusDetails>) = list.filter { DTOUtils.isError(it) }

private fun getOksCompensated(list: List<DTOAssignmentStatusDetails>) = list.filter { DTOStatusCode.OK_COMPENSATED == it.code }

private fun getOks(list: List<DTOAssignmentStatusDetails>) = list.filter { DTOUtils.isOK(it) }

fun createStatus(
    code: DTOStatusCode,
    reason: String?,
    description: String,
): DTOAssignmentStatus = DTOUtils.createStatus(code, reason, description)
