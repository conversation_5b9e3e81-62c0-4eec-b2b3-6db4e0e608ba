package no.ruter.tranop.app.common.dataflow.kafka.config

class KafkaInputConfigProperties {
    var minOperatingDateOffset: Long = -2
    var maxOperatingDateOffset: Long = +2

    var consumer: ConsumerConfigProperties = ConsumerConfigProperties()

    class ConsumerConfigProperties {
        var enabled: Boolean = true
        var topic: String? = null
        var group: String? = null
        var validationErrorResponse: String = "FAIL"
    }
}
