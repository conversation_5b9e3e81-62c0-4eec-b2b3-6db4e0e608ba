package no.ruter.tranop.app.common.db.xref

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.assignment.common.db.record.base.BaseRecord
import no.ruter.tranop.assignmentmanager.db.sql.Tables
import no.ruter.tranop.assignmentmanager.db.sql.tables.ExternalRefTable
import org.jooq.DSLContext
import org.jooq.Query
import org.jooq.UpdatableRecord
import java.time.OffsetDateTime

class ExternalRefRepository<R>(
    val owner: RecordType<*, R>,
    // public for testing.
    val dslContext: DSLContext,
    private val insightService: InsightService,
) where R : BaseRecord, R : UpdatableRecord<R> {
    // public for testing.
    val table: ExternalRefTable = Tables.EXTERNAL_REF

    /** Find any owners matching _any_ of the provided external refs. **/
    fun findOwners(externalRefs: Collection<String?>?): Set<String> {
        val cond = table.OWNER_TYPE.eq(owner.ownerTypeValue).and(table.REF_VALUE.`in`(externalRefs))
        return dslContext
            .selectDistinct(table.OWNER_REF)
            .from(table)
            .where(cond)
            .fetch {
                it.value1()!! // Column not nullable in SQL schema.
            }.toSet()
    }

    fun getExternalRefs(ownerRef: String): ExternalRefs {
        val res = ExternalRefs()
        dslContext
            .select(table.REF_TYPE, table.REF_VALUE)
            .from(table)
            .where(table.OWNER_REF.eq(ownerRef))
            .orderBy(table.REF_TYPE, table.REF_VALUE)
            .fetch { record ->
                val type = ExternalRefType.of(record.value1())!! // Column not nullable in SQL schema.
                val value = record.value2()
                res.add(type, value)
            }
        return res
    }

    fun update(
        externalRefs: Map<String?, ExternalRefs>,
        createdBy: String?,
        createdAt: OffsetDateTime?,
        clearOnUpdate: Boolean = true,
    ): Int {
        val flattenedExternalRefs =
            externalRefs.flatMap { (ref, externalRefs) ->
                externalRefs.all().map { (type, values) ->
                    ref to (type to values)
                }
            }
        if (flattenedExternalRefs.isEmpty()) {
            return 0
        }

        // Update external refs.
        val dsl = dslContext
        val inserts = ArrayList<Query>()
        for ((ref, refTypeValuePair) in flattenedExternalRefs) {
            val type = refTypeValuePair.first.value
            val values = refTypeValuePair.second
            val ownerType = owner.ownerTypeValue
            values.forEach { value ->
                val query =
                    dsl
                        .insertInto(table)
                        .set(table.REF_TYPE, type)
                        .set(table.REF_VALUE, value)
                        .set(table.OWNER_REF, ref)
                        .set(table.OWNER_TYPE, ownerType)
                        .set(table.CREATED_BY, createdBy)
                        .set(table.CREATED_AT, createdAt)
                        .onDuplicateKeyIgnore()
                inserts.add(query)
            }
        }
        val inserted = dsl.batch(inserts).execute().sum()

        // Delete any existing references not part of our update (now stale)
        if (clearOnUpdate) {
            deleteStaleExternalRefs(flattenedExternalRefs)
        }

        return inserted
    }

    fun deleteByOwnerRefs(ownerRefs: Collection<String>): Int =
        if (ownerRefs.isEmpty()) {
            0
        } else {
            dslContext.deleteFrom(table).where(table.OWNER_REF.`in`(ownerRefs)).execute()
        }

    private fun deleteStaleExternalRefs(externalRefs: List<Pair<String?, Pair<ExternalRefType, Set<String>>>>) {
        val dsl = dslContext
        try {
            val deletes = ArrayList<Query>()
            for ((ref, refTypeValuePair) in externalRefs) {
                val type = refTypeValuePair.first
                val values = refTypeValuePair.second
                val query =
                    dsl.deleteFrom(table).where(
                        table.OWNER_REF.eq(ref).and(
                            table.REF_TYPE.eq(type.value).and(
                                table.REF_VALUE.notIn(values),
                            ),
                        ),
                    )
                deletes.add(query)
            }

            // Only execute batch if we have something to delete.
            if (deletes.isNotEmpty()) {
                dsl.batch(deletes).execute()
            }
        } catch (e: Exception) {
            val details = getRefDetails(externalRefs.mapNotNull { it.first })
            insightService.insightError(
                channel = owner.channels.internal,
                classifier = "internal.error.xref.db.delete.failed",
                msg = "Error deleting external references for record $details: ${e.message}",
                e = e,
            )
        }
    }

    private fun getRefDetails(refs: List<String>): String {
        val maxNumberOfRecordedRefs = 10
        return when {
            refs.size == 1 -> "[${refs.first()}]"
            refs.size > maxNumberOfRecordedRefs ->
                refs
                    .take(maxNumberOfRecordedRefs)
                    .joinToString(prefix = "[", postfix = ", (${refs.size - maxNumberOfRecordedRefs} refs omitted)]")

            else -> refs.joinToString(prefix = "[", postfix = "]")
        }
    }
}
