package no.ruter.tranop.app.common.insight

import no.ruter.tranop.app.common.DataChannel

interface InsightContext {
    val channel: DataChannel
    val traceId: String?

    val insightKey: String
        get() = channel.insightKey

    val metadata: Map<String, Any?>
        get() = emptyMap()
    val recordMetadata: Boolean
        get() = false

    val metrics: Map<String, List<String>>
    val recordMetrics: Boolean
        get() = true

    val summary: String
        get() = "no.summary"

    var exception: Exception?
        get() = null
        set(_) {}
}
