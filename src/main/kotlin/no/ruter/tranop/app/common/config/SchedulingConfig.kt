package no.ruter.tranop.app.common.config

import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import net.javacrumbs.shedlock.support.KeepAliveLockProvider
import no.ruter.tranop.assignmentmanager.db.sql.Tables
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.SchedulingConfigurer
import org.springframework.scheduling.config.ScheduledTaskRegistrar
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import javax.sql.DataSource

@Configuration
@EnableScheduling
@EnableSchedulerLock(
    defaultLockAtMostFor = "10m",
)
class SchedulingConfig : SchedulingConfigurer {
    companion object {
        private const val KEEP_ALIVE_EXECUTOR_POOL_SIZE = 1
    }

    override fun configureTasks(taskRegistrar: ScheduledTaskRegistrar) {
        taskRegistrar.setScheduler(taskExecutor())
    }

    // Executor used by @Scheduled
    @Bean(destroyMethod = "shutdown")
    fun taskExecutor(): ScheduledExecutorService = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors())

    // Executor used by @ScheduledLock keep-alive provider
    @Bean(destroyMethod = "shutdown")
    fun keepAliveExecutor(): ScheduledExecutorService = Executors.newScheduledThreadPool(KEEP_ALIVE_EXECUTOR_POOL_SIZE)

    // https://github.com/lukas-krecan/ShedLock#jdbctemplate
    @Bean
    fun lockProvider(dataSource: DataSource): LockProvider {
        // Note:
        // We configure table / column names explicitly, even if are just using the default names, for now.
        // If we ever decide to rename them in the future, this code will (intentionally) break, forcing an
        // update of table / column references.
        val table = Tables.SHEDLOCK
        val columns =
            JdbcTemplateLockProvider.ColumnNames(
                table.NAME.name,
                table.LOCK_UNTIL.name,
                table.LOCKED_AT.name,
                table.LOCKED_BY.name,
            )
        val config =
            JdbcTemplateLockProvider.Configuration
                .builder()
                .usingDbTime() // Use UTC clock from DB server for safer / more robust locking.
                .withTableName(table.name)
                .withColumnNames(columns)
                .withJdbcTemplate(JdbcTemplate(dataSource))
                .build()

        // Wrap lock provider with keep-alive provider, since we have (potentially) long-running tasks.
        val provider = JdbcTemplateLockProvider(config)
        return KeepAliveLockProvider(provider, keepAliveExecutor())
    }
}
