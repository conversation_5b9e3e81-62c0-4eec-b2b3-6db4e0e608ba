package no.ruter.tranop.app.common.mapping.output

import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.mapping.input.InputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.input.InputMessage

interface OutputContext<I, IC : InputContext> : MappingContext {
    /** Key to use for message published to Kafka. **/
    val key: String

    /** Original input message (main source of mapped data). **/
    val input: InputMessage<I, IC>

    val events: Collection<InputEvent>
}
