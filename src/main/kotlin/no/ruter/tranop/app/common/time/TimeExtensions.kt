package no.ruter.tranop.app.common.time

import java.time.LocalTime
import kotlin.math.pow

fun String.toLocalTime() =
    try {
        if (this.isEmpty()) null else LocalTime.parse(this)
    } catch (e: Exception) {
        null
    }

/**
 * Calculates the exponential backoff delay for the given retry.
 * */
fun delayMillis(
    retryNumber: Int,
    // 0.1 second
    initialDelay: Long = 100,
    // 10 seconds
    maxDelay: Long = 10000,
    factor: Double = 2.0,
): Long {
    require(retryNumber > 0) { "retryNumber must be greater than 0" }
    val multiplier = factor.pow(retryNumber - 1)
    return (initialDelay * multiplier).toLong().coerceAtMost(maxDelay)
}
