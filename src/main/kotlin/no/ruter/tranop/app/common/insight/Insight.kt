package no.ruter.tranop.app.common.insight

import no.ruter.tranop.app.common.RecordType

class Insight private constructor() {
    enum class DataType(
        val value: String,
    ) {
        INPUT(DT_INPUT),
        OUTPUT(DT_OUTPUT),
        ;

        override fun toString(): String = value
    }

    companion object {
        const val DT_INPUT = "input"
        const val DT_OUTPUT = "output"
        const val DT_DISABLED = "disabled"

        const val MAPPING_EXCEPTION = "mapping.exception"
        const val MAPPING_BUILD_EXCEPTION = "mapping.build.exception"

        const val OK_MAPPED = "ok.mapped"
        const val OK_TOMBSTONE = "ok.tombstone"
        const val OK_SKIP_UNPUBLISHED_OWNER = "ok.skip.unpublished.owner"

        const val SKIP_DATA_ERROR = "error.skip.data.error"
        const val SKIP_MAPPING_EXCEPTION = "error.skip.$MAPPING_EXCEPTION"

        fun getKey(
            enabled: Boolean,
            key: String,
        ) = if (enabled) key else "$key.$DT_DISABLED"

        fun getMessage(
            enabled: Boolean,
            msg: String,
        ) = if (enabled) msg else "[$DT_DISABLED] $msg"

        fun getInputKey(
            type: RecordType<*, *>,
            key: String,
        ) = "$DT_INPUT.${type.value}.$key"

        fun getOutputKey(
            type: RecordType<*, *>,
            key: String,
        ) = "$DT_OUTPUT.${type.value}.$key"
    }
}
