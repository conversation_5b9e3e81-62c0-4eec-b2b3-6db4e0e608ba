package no.ruter.tranop.app.common

import no.ruter.tranop.app.common.insight.InsightContext

interface TraceInfoContext : InsightContext {
    val trace: TraceInfo

    override val traceId: String?
        get() = trace.traceId

    /**
     * Resolve operator id and authority id of the context's trace info. This may update the internal state of the implementing object,
     * causing [trace.operatorId][TraceInfo.operatorId] and [trace.authorityId][TraceInfo.authorityId] to returned different values than
     * before once this method has been called.
     **/
    fun resolveActiveOperator()
}
