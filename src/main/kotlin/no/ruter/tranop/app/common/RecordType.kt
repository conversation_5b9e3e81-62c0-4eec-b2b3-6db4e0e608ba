package no.ruter.tranop.app.common

import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.avro.entity.operational.OperationalServiceDeviationKeyV1
import no.ruter.tranop.app.common.dataflow.kafka.entity.EntityDetails
import no.ruter.tranop.app.common.db.record.base.BaseRecordTableMetadata
import no.ruter.tranop.assignmentmanager.db.sql.Tables
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyInputTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.DeviationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.JourneyEventTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.MitigationTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.OutboxTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointLinkTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.StopPointTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyInputRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.JourneyEventRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.MitigationRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointLinkRecord
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.jooq.Table
import org.jooq.UpdatableRecord
import kotlin.reflect.KClass

open class RecordType<
    T : Table<R>,
    R : UpdatableRecord<R>,
>(
    val value: String,
    val ownerTypeValue: Int,
    val table: T,
    dtoClass: KClass<*>,
    val outputs: Outputs? = null,
) {
    companion object {
        val STOP =
            object : RecordType<StopPointTable, StopPointRecord>(
                value = "stop-point",
                ownerTypeValue = 1,
                table = Tables.STOP_POINT,
                dtoClass = DTOStopPoint::class,
                outputs =
                    Outputs(
                        type = DBOutboxDataType.STOP_POINT,
                        kafkaEntities =
                            mapOf(
                                DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2 to EntityDetails(DatedJourneyStopPointKeyV2::class),
                            ),
                        kafkaInternal = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
                    ),
            ) { }

        val LINK =
            object : RecordType<StopPointLinkTable, StopPointLinkRecord>(
                value = "stop-point-link",
                ownerTypeValue = 2,
                table = Tables.STOP_POINT_LINK,
                dtoClass = DTOStopPointLink::class,
                outputs =
                    Outputs(
                        type = DBOutboxDataType.STOP_POINT_LINK,
                        kafkaInternal = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
                        kafkaEntities =
                            mapOf(
                                DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2 to EntityDetails(DatedJourneyStopPointKeyV2::class),
                            ),
                    ),
            ) { }

        val OUTBOX =
            object : RecordType<OutboxTable, OutboxRecord>(
                value = "outbox",
                ownerTypeValue = -1, // not used as external ref owner.
                table = Tables.OUTBOX,
                dtoClass = no.ruter.tranop.app.outbox.Outbox::class,
            ) { }

        val DATED_JOURNEY =
            object : RecordType<DatedJourneyTable, DatedJourneyRecord>(
                value = "dated-journey",
                ownerTypeValue = 3,
                table = Tables.DATED_JOURNEY,
                dtoClass = DTODatedJourney::class,
                outputs =
                    Outputs(
                        type = DBOutboxDataType.DATED_JOURNEY,
                        kafkaInternal = DBOutboxTargetType.KAFKA_JSON_INTERNAL,
                        kafkaEntities =
                            mapOf(
                                DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2 to EntityDetails(DatedJourneyKeyV2::class),
                            ),
                    ),
            ) { }

        val DATED_JOURNEY_INPUT =
            object : RecordType<DatedJourneyInputTable, DatedJourneyInputRecord>(
                value = "dated-journey-input",
                ownerTypeValue = DATED_JOURNEY.ownerTypeValue, // same as dated-journey regarding x_refs
                table = Tables.DATED_JOURNEY_INPUT,
                dtoClass = DTODatedJourney::class,
            ) { }

        val DATED_JOURNEY_EVENT =
            object : RecordType<JourneyEventTable, JourneyEventRecord>(
                value = "dated-journey-event",
                ownerTypeValue = -2, // probably not going to be used as external ref owner...?
                table = Tables.JOURNEY_EVENT,
                dtoClass = DTOEvent::class,
                outputs =
                    Outputs(
                        type = DBOutboxDataType.DATED_JOURNEY_EVENT,
                        snowflake = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                    ),
            ) { }

        val SERVICE_DEVIATION =
            object : RecordType<DeviationTable, DeviationRecord>(
                value = "service-deviation",
                ownerTypeValue = 4,
                table = Tables.DEVIATION,
                dtoClass = DTOServiceDeviation::class,
                outputs =
                    Outputs(
                        type = DBOutboxDataType.SERVICE_DEVIATION,
                        snowflake = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                        kafkaEntities =
                            mapOf(
                                DBOutboxTargetType.KAFKA_AVRO_ENTITY_V2 to
                                    EntityDetails(
                                        OperationalServiceDeviationKeyV1::class,
                                        version = 2,
                                    ),
                            ),
                    ),
            ) { }

        val SERVICE_MITIGATION =
            object : RecordType<MitigationTable, MitigationRecord>(
                value = "service-mitigation",
                ownerTypeValue = 5,
                table = Tables.MITIGATION,
                dtoClass = DTOServiceMitigation::class,
                outputs =
                    Outputs(
                        type = DBOutboxDataType.SERVICE_MITIGATION,
                        snowflake = DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                        kafkaEntities = emptyMap(), // TODO: Add v1 entity.
                    ),
            ) { }
    }

    val dto: String = dtoClass.java.simpleName
    val desc: String = value.replace("-", " ")
    val channels: Channels = Channels(value)
    val tableMeta = BaseRecordTableMetadata(table)

    override fun toString(): String = value

    override fun hashCode(): Int = value.hashCode()

    override fun equals(other: Any?): Boolean = if (other is RecordType<*, *>) this.value == other.value else false

    class Outputs(
        val type: DBOutboxDataType,
        val snowflake: DBOutboxTargetType? = null,
        val openSearch: DBOutboxTargetType? = null,
        kafkaEntities: Map<DBOutboxTargetType, EntityDetails> = emptyMap(),
        val kafkaInternal: DBOutboxTargetType? = null,
    ) {
        private val entities = EntityDetails.entityDetailsMap(kafkaEntities)

        fun entity(version: Int): EntityDetails =
            entities[version]?.second ?: throw IllegalArgumentException("Unknown entity version: $version")
    }

    class Channels(
        name: String,
    ) {
        val input = DataChannel.define(name, direction = DataChannel.Direction.IN)
        val output = DataChannel.define(name, direction = DataChannel.Direction.OUT)
        val internal = DataChannel.define(name, direction = DataChannel.Direction.INTERNAL)
    }
}
