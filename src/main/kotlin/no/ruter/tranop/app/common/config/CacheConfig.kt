package no.ruter.tranop.app.common.config

import com.github.benmanes.caffeine.cache.Caffeine
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cache.caffeine.CaffeineCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.TimeUnit

@Configuration
@EnableCaching
class CacheConfig {
    @Bean
    fun config(): Caf<PERSON>ine<Any, Any>? = Caffeine.newBuilder().recordStats().expireAfterWrite(60, TimeUnit.MINUTES)

    @Bean
    fun cacheManager(caffeine: Caffeine<Any, Any>) = CaffeineCacheManager().apply { setCaffeine(caffeine) }
}
