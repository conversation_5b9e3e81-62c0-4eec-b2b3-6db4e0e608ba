package no.ruter.tranop.app.common.dataflow.kafka

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2
import no.ruter.tranop.dated.journey.dto.kafka.DTODatedJourneySerde
import no.ruter.tranop.dated.journey.dto.kafka.DTOStopPointLinkSerde
import no.ruter.tranop.dated.journey.dto.kafka.DTOStopPointSerde
import no.ruter.tranop.journey.deviation.dto.kafka.DTOServiceDeviationSerde
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serdes
import org.springframework.stereotype.Component

@Component
class KafkaSerdeProvider {
    val stringSerde: Serde<String?> = Serdes.StringSerde()

    val datedJourneyEntitySerde: SpecificAvroSerde<DatedJourneyKeyV2> = SpecificAvroSerde<DatedJourneyKeyV2>()
    val stopPointEntitySerde: SpecificAvroSerde<DatedJourneyStopPointKeyV2> = SpecificAvroSerde<DatedJourneyStopPointKeyV2>()
    val stopPointDTOSerde = DTOStopPointSerde()
    val stopPointLinkEntitySerde: SpecificAvroSerde<DatedJourneyStopPointLinkKeyV2> =
        SpecificAvroSerde<DatedJourneyStopPointLinkKeyV2>()
    val stopPointLinkDTOSerde = DTOStopPointLinkSerde()
    val assignmentJourneyDTOSerde = DTODatedJourneySerde()
    val serviceDeviationDTOSerde = DTOServiceDeviationSerde()

    val byteArraySerde = Serdes.ByteArraySerde()
}
