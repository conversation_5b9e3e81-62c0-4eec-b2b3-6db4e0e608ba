package no.ruter.tranop.app.common

import no.ruter.rdp.metrics.InsightUtils
import no.ruter.tranop.assignment.dto.util.DTOUtils

abstract class ProcessingContext : ValidationContext() {
    companion object {
        const val SKIP_REASON_NONE = "no.reason"
        const val SKIP_REASON_TOMBSTONE = "tombstone"
        const val SKIP_REASON_DELETE = "delete"
    }

    class NOOPProcessingContext(
        override val channel: DataChannel,
        override val traceId: String? = null,
    ) : ProcessingContext()

    open val skip: Boolean = false
    open var skipReason: String = SKIP_REASON_NONE

    override val metrics: Map<String, List<String>>
        get() = createMetrics()

    private fun createMetrics(): Map<String, List<String>> {
        val metrics = mutableMapOf<String, List<String>>()
        val error = statusDetails.firstOrNull { DTOUtils.isError(it) }
        val errorSuffixes =
            listOfNotNull(
                error?.code,
                error?.fieldName,
                error?.reason,
            )

        val errorSuffix = error?.let { errorSuffixes.joinToString(separator = ".") } ?: "error"

        val suffix =
            when {
                skip -> "ok.skip.$skipReason"
                valid() -> "ok"
                else -> errorSuffix
            }
        val dataType = channel.insightKey
        metrics[dataType] = listOf("${this.insightKey}.$suffix")

        val subject = "$dataType.details"
        val insightTypes = mutableListOf<String>()
        statusDetails.forEach { detail ->
            val code = detail.code

            val insightType =
                listOfNotNull(
                    code?.toString(),
                    detail.fieldName,
                    detail.reason,
                ).joinToString(separator = ".")
            insightTypes.add(InsightUtils.normalize(insightType))
        }
        if (insightTypes.isNotEmpty()) {
            metrics[subject] = insightTypes
        }
        return metrics
    }
}
