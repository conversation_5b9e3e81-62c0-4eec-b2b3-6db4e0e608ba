package no.ruter.tranop.app.common.mapping.output

import no.ruter.tranop.app.common.mapping.AbstractMappingContext
import no.ruter.tranop.app.common.mapping.input.InputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.input.InputMessage

abstract class AbstractOutputContext<I, IC : InputContext>(
    override val input: InputMessage<I, IC>,
) : AbstractMappingContext(),
    OutputContext<I, IC> {
    // Note: We assume input key is same as output key, i.e. no re-keying from DTO topics to entity topics.
    override val key: String
        get() = input.key

    // We assume input context has a sensible toString() which is also usable by us
    // (i.e., the distinction between the input context and output context are irrelevant for logging purposes).
    override fun toString(): String = input.context.toString()

    // InsightContext interface
    override val traceId: String?
        get() = input.context.traceId
    override val events: Collection<InputEvent>
        get() = input.context.events
}
