package no.ruter.tranop.app.common.insight

import no.ruter.rdp.logging.LogKey
import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.rdp.metrics.InsightUtils
import no.ruter.rdp.metrics.RdpMetricsService
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.insight.Insight.DataType
import no.ruter.tranop.app.common.mapping.ensureNonNullValues
import org.springframework.stereotype.Service

@Service
class InsightService(
    val metricsService: RdpMetricsService,
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass.canonicalName)
    val appName: String get() = metricsService.name

    private fun invoke(
        metadata: Map<String, Any?> = mapOf(),
        insight: () -> Unit,
    ) {
        try {
            insight()
        } catch (e: Exception) {
            val msg = "Recording insight failed"
            try {
                logger.error(msg, metadata, e)
            } catch (e: Exception) {
                logger.error("$msg - Removed structured arguments")
            }
        }
    }

    fun insightError(
        channel: DataChannel,
        classifier: String,
        msg: String?,
        metadata: Map<String, Any?> = emptyMap(),
        e: Exception? = null,
    ) {
        val message =
            listOfNotNull(
                channel,
                classifier,
                msg,
            ).joinToString(": ")

        invoke(metadata) {
            metricsService.recordDataError(channel.name, classifier)
            val meta =
                metadata
                    .ensureNonNullValues()
                    .plus(LogKey.MESSAGE_SUBJECT to channel.name)
                    .plus("direction" to channel.direction.name)
            logger.info(message, meta, e)
        }
        e?.let {
            logger.error(msg ?: "missing message", it)
        }
    }

    fun messageSent(topic: String) {
        invoke {
            metricsService.recordMessageSent(topic)
        }
    }

    fun messageReceived(topic: String) {
        invoke {
            metricsService.recordMessageReceived(topic)
        }
    }

    fun insight(context: InsightContext?): InsightContext? {
        invoke {
            context?.let {
                val exception = context.exception
                if (exception != null) {
                    metricsService.recordDataError("exception", "thrown")
                    logger.error(
                        msg = "Exception thrown: ${context.summary}",
                        metadata = context.metadata,
                        e = exception,
                    )
                }

                context.metrics.forEach { (dataType, insightTypes) ->
                    insightTypes.forEach { insightType ->
                        val data = dataType.normalize()
                        metricsService.recordDataInsight(
                            dataType = data,
                            insightType = insightType,
                            amount = 1.0,
                        )
                    }
                }

                if (context.recordMetadata) {
                    val channel = context.channel
                    val message = "$channel: ${context.summary}"
                    val meta =
                        context.metadata
                            .ensureNonNullValues()
                            .plus(LogKey.MESSAGE_SUBJECT to channel.toString())
                            .plus("direction" to channel.direction.name)

                    if (exception != null) {
                        meta.plus("stack_trace" to exception)
                        logger.error(message, meta, exception)
                    } else {
                        logger.info(msg = message, metadata = meta)
                    }
                }
            }
        }
        return context
    }

    private fun String.normalize(): String = InsightUtils.normalize(this)

    // TODO: [BIG_ONE] Consolidate metrics for dated journeys with assignment with friends
    fun recordMessage(
        type: String = "INFO",
        context: InsightContext,
        dataType: DataType,
        insight: String,
        message: String? = null,
        cause: Throwable? = null,
        metadata: Map<String, Any?>? = null,
        recordType: RecordType<*, *>,
    ) {
        if (message == null && cause == null) {
            return
        }
        val subject = "${dataType.value}-${recordType.value}"
        val msg =
            if (cause == null) {
                getMessage(
                    type = type,
                    insight = insight,
                    msg = message ?: MSG_NONE,
                    subject = subject,
                )
            } else {
                getMessage(
                    type = "$type:exception",
                    insight = insight,
                    msg = "${message ?: MSG_NONE}: ${cause.message}",
                    subject = subject,
                )
            }

        val meta =
            context.metadata
                .ensureNonNullValues()
                .plus(LogKey.MESSAGE_SUBJECT to subject)
                .plus("direction" to "output")
        if (cause != null) {
            logger.error(
                msg,
                meta.plus(
                    "stack_trace" to cause.stackTrace,
                ),
                cause,
            )
        } else {
            logger.info(msg, meta)
        }
    }

    private fun getMessage(
        type: String,
        insight: String? = null,
        msg: String? = null,
        subject: String,
    ): String {
        val infix =
            listOf(
                type,
                insight,
            ).joinToString(" / ")

        return "$subject: $infix: $msg"
    }

    fun recordDataInsight(
        subject: String,
        insightType: String,
        amount: Double = 1.0,
    ) {
        metricsService.recordDataInsight(
            dataType = subject,
            insightType = insightType,
            amount = amount,
        )
    }

    companion object {
        const val MSG_NONE = "<no message>"
    }
}
