package no.ruter.tranop.app.common.db.config

import org.springframework.boot.autoconfigure.jooq.DefaultConfigurationCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class DatabaseConfig {
    // https://blog.jooq.org/how-to-customise-a-jooq-configuration-that-is-injected-using-spring-boot/
    @Bean
    fun configurationCustomizer(): DefaultConfigurationCustomizer =
        DefaultConfigurationCustomizer { c ->
            c
                .settings()
                .withExecuteWithOptimisticLocking(true)
                .withExecuteWithOptimisticLockingExcludeUnversioned(true)
        }
}
