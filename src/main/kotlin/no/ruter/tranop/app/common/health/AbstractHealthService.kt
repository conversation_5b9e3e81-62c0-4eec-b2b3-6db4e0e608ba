package no.ruter.tranop.app.common.health

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.HealthIndicator

abstract class AbstractHealthService(
    val name: String,
) : HealthIndicator {
    protected val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    fun up(detail: Any? = "up"): Health =
        Health
            .up()
            .withDetails(
                details(
                    detail = detail ?: "up",
                ),
            ).build()

    fun down(detail: Any? = "down"): Health {
        val msg = "$name not running, restart application"
        log.error(msg)
        return Health
            .down()
            .withDetails(
                details(
                    detail = detail ?: "down",
                    info = msg,
                ),
            ).build()
    }

    private fun details(
        detail: Any,
        info: Any? = null,
    ) = mutableMapOf<String, Any>().apply {
        put(name, detail)
        info?.let {
            put("info", it)
        }
    }
}
