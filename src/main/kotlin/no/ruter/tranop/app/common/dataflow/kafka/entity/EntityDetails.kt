package no.ruter.tranop.app.common.dataflow.kafka.entity

import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import java.util.TreeMap
import kotlin.reflect.KClass

/** Class representing technical details about an entity data model object. **/
class EntityDetails(
    val type: KClass<*>,
    /** Value to use in $.entityHeader.name when producing entities on Kafka. **/
    val name: String = type.java.simpleName.dropLast(n = 2),
    /** Value to use in $.entityHeader.version when producing entities on Kafka. **/
    val version: Int =
        type.java.simpleName
            .last()
            .toString()
            .toInt(),
    /** Value to use in $.entityHeader.partition when producing entities on Kafka. **/
    val partition: String = "key",
) {
    val typeName: String = type.java.simpleName

    override fun toString(): String = typeName

    companion object {
        const val V1: Int = 1
        const val V2: Int = 2
        const val V3: Int = 3

        fun entityDetailsMap(entities: Map<DBOutboxTargetType, EntityDetails>): Map<Int, Pair<DBOutboxTargetType, EntityDetails>> {
            val res = TreeMap<Int, Pair<DBOutboxTargetType, EntityDetails>>()
            for (e in entities.entries) {
                val key = e.value.version
                if (res.containsKey(key)) {
                    throw IllegalStateException("Duplicate entity definition: $key")
                }
                res[key] = Pair(e.key, e.value)
            }
            return res
        }
    }
}
