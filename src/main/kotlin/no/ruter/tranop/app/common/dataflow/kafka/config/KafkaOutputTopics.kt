package no.ruter.tranop.app.common.dataflow.kafka.config

import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopic
import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopics
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "kafka.topics.output")
class KafkaOutputTopics(
    val assignment: <PERSON>f<PERSON>Topic,
    val datedJourney: KafkaTopic,
    val assignmentJourney: KafkaTopic,
    val stopPoint: KafkaTopic,
    val stopPointDto: KafkaTopic,
    val stopPointLink: KafkaTopic,
    val stopPointLinkDto: KafkaTopic,
    val availableDestinationDisplaysAdt2: KafkaTopic,
    val availableDestinationDisplaysAdt3: KafkaTopic,
    val serviceDeviationDTO: KafkaTopic,
) : KafkaTopics // TODO: Get rid of this dependency.
