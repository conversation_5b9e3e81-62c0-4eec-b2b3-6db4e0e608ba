package no.ruter.tranop.app.common.dataflow.kafka.entity

import no.ruter.avro.entity.datedjourney.v2.common.Event
import no.ruter.avro.entity.datedjourney.v2.common.EventMetadata
import no.ruter.avro.entity.datedjourney.v2.link.DatedJourneyStopPointLinkV2
import no.ruter.avro.entity.datedjourney.v2.link.TrafficPriorityPoint
import no.ruter.avro.entity.datedjourney.v2.stop.DatedJourneyStopPointV2
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.app.common.mapping.MappingDetails
import no.ruter.tranop.app.common.mapping.input.InputContext
import no.ruter.tranop.app.common.mapping.mapList
import no.ruter.tranop.app.common.mapping.output.AbstractOutputMapper
import no.ruter.tranop.app.common.mapping.output.OutputContext
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.common.dto.model.DTOLifeCycleInfo
import no.ruter.tranop.dated.journey.dto.common.model.value.DTOValueType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadata
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageOriginatingFrom
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageType
import no.ruter.tranop.dated.journey.dto.model.common.DTOStopPointBehaviourType
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportModeProperty
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLinkType
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOTrafficPriorityPoint
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOTrafficPriorityTriggerCode
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint

abstract class AbstractEntityV2OutputMapper<I, IC : InputContext, O, OC : OutputContext<I, IC>>(
    type: RecordType<*, *>,
    config: AbstractSectionConfigProperties,
    timeService: TimeService,
    insightService: InsightService,
    entityVersion: Int = EntityDetails.V2,
) : AbstractOutputMapper<I, IC, O, OC>(type, entityVersion, config, timeService, insightService) {
    companion object {
        const val UNKNOWN = "UNKNOWN"

        val LINK_TYPES =
            entityValues(
                DTOStopPointLinkType.MIX to "MIX",
                DTOStopPointLinkType.ROAD to "ROAD",
                DTOStopPointLinkType.TRACK to "TRACK",
                DTOStopPointLinkType.WATER to "WATER",
                DTOStopPointLinkType.UNKNOWN to UNKNOWN,
            )

        val EVENT_TYPES =
            entityValues(
                DTOEventType.CREATED to "CREATED",
                DTOEventType.IMPORTED to "IMPORTED",
                DTOEventType.VEHICLE_PLANNED to "VEHICLE_PLANNED",
                DTOEventType.VEHICLE_ASSIGNED to "VEHICLE_ASSIGNED",
                DTOEventType.VEHICLE_UNASSIGNED to "VEHICLE_UNASSIGNED",
                DTOEventType.CONTINGENCY_VEHICLE_PLANNED to "CONTINGENCY_VEHICLE_PLANNED",
                DTOEventType.OPERATING_WITHOUT_SIGNON to "OPERATING_WITHOUT_SIGNON",
                DTOEventType.UPDATED to "UPDATED",
                DTOEventType.PATCHED to "PATCHED",
                DTOEventType.REPLACED to "REPLACED",
                DTOEventType.CANCELLED to "CANCELLED",
                DTOEventType.UNCANCELLED to "UNCANCELLED",
                DTOEventType.COMPLETED to "COMPLETED",
                DTOEventType.OMITTED to "OMITTED",
                DTOEventType.UN_OMITTED to "UN_OMITTED",
                DTOEventType.CALLS_CANCELLED to "CALLS_CANCELLED",
                DTOEventType.CALLS_UNCANCELLED to "CALLS_UNCANCELLED",
                DTOEventType.CALLS_OMITTED to "CALLS_OMITTED",
                DTOEventType.CALLS_UN_OMITTED to "CALLS_UN_OMITTED",
                DTOEventType.DELAYED to "DELAYED",
                DTOEventType.UNDELAYED to "UNDELAYED",
                DTOEventType.MISSING_SIGN_ON to "MISSING_SIGN_ON",
                DTOEventType.MISSING_SIGN_ON_RESOLVED to "MISSING_SIGN_ON_RESOLVED",
                DTOEventType.MISSING_SIGN_OFF to "MISSING_SIGN_OFF",
                DTOEventType.MISSING_OPERATOR_ACTION to "MISSING_OPERATOR_ACTION",
                DTOEventType.MISSING_OPERATOR_ACTION_RESOLVED to "MISSING_OPERATOR_ACTION_RESOLVED",
                DTOEventType.DUPLICATE_SIGN_ON to "DUPLICATE_SIGN_ON",
                DTOEventType.DUPLICATE_SIGN_ON_RESOLVED to "DUPLICATE_SIGN_ON_RESOLVED",
                DTOEventType.OPERATING_WITHOUT_SIGNON to "OPERATING_WITHOUT_SIGNON",
                DTOEventType.REPLACED to "REPLACED",
                DTOEventType.UNKNOWN to UNKNOWN,
            )

        val EVENT_METADATA_KEY_TYPES =
            entityValues(
                DTOEventMetadataKeyType.VEHICLE_REF to "VEHICLE_REF",
                DTOEventMetadataKeyType.VEHICLE_TASK_ID to "VEHICLE_TASK_ID",
                DTOEventMetadataKeyType.QUAY_REFS to "QUAY_REFS",
                DTOEventMetadataKeyType.ASSIGNMENT_REF to "ASSIGNMENT_REF",
                DTOEventMetadataKeyType.ASSIGNMENT_CODE to "ASSIGNMENT_CODE",
                DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to "FIRST_DEPARTURE_DATE_TIME",
                DTOEventMetadataKeyType.LAST_ARRIVAL_DATE_TIME to "LAST_ARRIVAL_DATE_TIME",
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_CASE_KEY_V2_REF to "ENTITY_TRAFFIC_CASE_KEY_V2_REF",
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_EVENT_KEY_V1_REF to "ENTITY_TRAFFIC_EVENT_KEY_V1_REF",
                DTOEventMetadataKeyType.ENTITY_TRAFFIC_SITUATION_KEY_V2_REF to "ENTITY_TRAFFIC_SITUATION_KEY_V2_REF",
                DTOEventMetadataKeyType.ENTITY_SERVICE_DEVIATION_KEY_V1_REF to "ENTITY_SERVICE_DEVIATION_KEY_V1_REF",
                DTOEventMetadataKeyType.ENTITY_SERVICE_MITIGATION_KEY_V1_REF to "ENTITY_SERVICE_MITIGATION_KEY_V1_REF",
                // TODO: Remove quayRef when all operational events are drained from journey-manager
                DTOEventMetadataKeyType.of("quayRef") to "QUAY_REF",
                DTOEventMetadataKeyType.DELAY_MINUTES to "DELAY_MINUTES",
                DTOEventMetadataKeyType.TIME_BOUNDARY to "TIME_BOUNDARY",
                DTOEventType.UNKNOWN to UNKNOWN,
            )

        val TRIGGER_CODES =
            entityValues(
                DTOTrafficPriorityTriggerCode.ENTER to "ENTER",
                DTOTrafficPriorityTriggerCode.EXIT to "EXIT",
                DTOTrafficPriorityTriggerCode.DOOR_CLOSE to "DOOR_CLOSE",
                DTOTrafficPriorityTriggerCode.UNKNOWN to UNKNOWN,
            )

        val TRANSPORT_MODES = entityValues(DTOTransportMode.ALL, DTOTransportMode.UNKNOWN)
        val TRANSPORT_MODE_PROPERTIES = entityValues(DTOTransportModeProperty.ALL, DTOTransportModeProperty.UNKNOWN)

        val STOP_POINT_BEHAVIOUR_TYPE =
            entityValues(
                DTOStopPointBehaviourType.UNKNOWN to UNKNOWN,
                DTOStopPointBehaviourType.NO_SERVICE to "NO_SERVICE",
                DTOStopPointBehaviourType.FULL_SERVICE to "FULL_SERVICE",
                DTOStopPointBehaviourType.FOR_BOARDING_ONLY to "FOR_BOARDING_ONLY",
                DTOStopPointBehaviourType.FOR_ALIGHTING_ONLY to "FOR_ALIGHTING_ONLY",
                DTOStopPointBehaviourType.UNKNOWN to UNKNOWN,
            )

        val LINEAGE_TYPE =
            entityValues(
                DTOLineageType.JOURNEY_PATTERN_REF to "JOURNEY_PATTERN_REF",
                DTOLineageType.RUNTIME_PATTERN_REF to "RUNTIME_PATTERN_REF",
                DTOLineageType.JOURNEY_REF to "JOURNEY_REF",
                DTOLineageType.UNKNOWN to UNKNOWN,
            )

        val LINEAGE_SOURCE_TYPE =
            entityValues(
                DTOLineageOriginatingFrom.HASTUS to "HASTUS",
                DTOLineageOriginatingFrom.BIFROST to "BIFROST",
                DTOLineageOriginatingFrom.UNKNOWN to UNKNOWN,
            )

        @JvmStatic
        protected fun <T : DTOValueType> entityValues(vararg pairs: Pair<T, String>): Map<T, String> = pairs.toMap()

        private fun <T : DTOValueType> entityValues(
            values: Set<T>,
            unknown: T,
        ): Map<T, String> {
            val res = LinkedHashMap<T, String>(values.size + 1)
            values.forEach { e -> e.value?.let { res[e] = it } }
            unknown.value?.let { res[unknown] = it }
            return res
        }
    }

    protected fun <T : DTOValueType> T?.toEntityValue(
        context: OC,
        path: String,
        insight: String,
        values: Map<T, String>,
        errorType: MappingDetails.Type,
        unmappedPassThrough: Boolean,
    ): String? {
        val key =
            this.notNull(
                context = context,
                path = path,
                insight = "missing.$insight",
                errorType = errorType,
            ) ?: return null
        val res = values[key]
        return if (res != null) {
            res
        } else {
            val msg = "unknown value type value: ${key.value()}"
            val reason = "unknown.$insight"
            if (unmappedPassThrough) {
                context.addWarning(path, msg, reason)
                key.value()
            } else {
                context.addError(path, msg, reason)
                null
            }
        }
    }

    protected fun mapEvent(
        input: DTOEvent,
        path: String,
        context: OC,
    ): Event? {
        val type =
            input.type.toEntityValue(
                context = context,
                path = "$path.type",
                insight = "event.type",
                values = EVENT_TYPES,
                errorType = MappingDetails.Type.WARN,
                unmappedPassThrough = true,
            ) ?: DTOEventType.UNKNOWN.value()

        val timestamp =
            input.timestamp?.toOffsetDateTime(
                context = context,
                path = "$path.timestamp",
                insight = "event.timestamp",
                errorType = MappingDetails.Type.ERROR,
            )
        val metadata = context.mapList(input.metadata, path = "$path.metadata", this::mapEventMetadata)
        return context.build(path) {
            Event
                .newBuilder()
                .setType(type)
                .setSource(input.source) // Source is optional.
                .setTraceId(input.traceId) // TraceId is optional.
                .setEventTimestamp(timestamp?.toString())
                .setDescription(input.description ?: "<none>")
                .setMetadata(metadata)
                .build()
        }
    }

    private fun mapEventMetadata(
        input: DTOEventMetadata,
        path: String,
        context: OC,
    ): EventMetadata? {
        val key =
            input.key.toEntityValue(
                context = context,
                path = "$path.key",
                insight = "event.metadata.key",
                values = EVENT_METADATA_KEY_TYPES,
                errorType = MappingDetails.Type.WARN,
                unmappedPassThrough = true,
            ) ?: DTOEventType.UNKNOWN.value()

        val value =
            input.value.notNull(
                context = context,
                path = "$path.$key.value",
                insight = "event.metadata.missing.value",
                errorType = MappingDetails.Type.WARN,
            ) ?: ""

        return context.build(path) {
            EventMetadata
                .newBuilder()
                .setKey(key)
                .setValue(value)
                .build()
        }
    }

    protected fun mapStopPoint(
        input: DTOStopPoint,
        path: String,
        context: OC,
    ): DatedJourneyStopPointV2? {
        val location = input.geoPoint?.location?.toGeoJson(path = "$path.geoPoint.location", context)
        return context.build(path) {
            DatedJourneyStopPointV2
                .newBuilder()
                .setDataSource(getDataSource(input.lifeCycleInfo))
                .setEntityDatedJourneyStopPointKeyV2Ref(input.ref) // Already verified by input mapper.
                .setLocation(location)
                .setName(input.name)
                .setPublicCode(input.publicCode)
                .setDescription(input.description)
                .setStopPlaceDescription(input.stopPlaceDescription)
                .setTariffZoneRefs(input.tariffZones)
                .setExternalStopPointId(input.quayRef)
                .setLegacyStopPointId(input.legacyQuayRef)
                .setExternalStopPlaceId(input.stopPlaceRef)
                .setExternalStopAreaId(input.stopAreaRef)
                .build()
        }
    }

    protected fun mapStopPointLink(
        input: DTOStopPointLink,
        path: String,
        context: OC,
    ): DatedJourneyStopPointLinkV2? {
        val origin =
            input.fromStopPointRef.notEmpty(
                context = context,
                path = "$path.fromStopPointRef",
                insight = "missing.stop.point.link.from.ref",
                errorType = MappingDetails.Type.ERROR,
            )
        val destination =
            input.toStopPointRef.notEmpty(
                context = context,
                path = "$path.toStopPointRef",
                insight = "missing.stop.point.link.to.ref",
                errorType = MappingDetails.Type.ERROR,
            )

        val type =
            input.stopPointLinkType.toEntityValue(
                context = context,
                path = "$path.stopPointLinkType",
                insight = "stop.point.link.type",
                values = LINK_TYPES,
                // TODO: Should this be stricter?
                errorType = MappingDetails.Type.WARN,
                // TODO: Should this be stricter?
                unmappedPassThrough = true,
            ) ?: DTOStopPointLinkType.UNKNOWN.value()

        val points =
            context.mapList(
                input.trafficPriorityPoints,
                path = "$path.trafficPriorityPoints",
                this::mapTrafficPriorityPoint,
            )
        val trackLine = input.trackLine?.toGeoJson(path = "$path.trackLine", context)
        return context.build(path) {
            DatedJourneyStopPointLinkV2
                .newBuilder()
                .setDataSource(getDataSource(input.lifeCycleInfo))
                .setEntityDatedJourneyStopPointLinkKeyV2Ref(input.ref) // Already verified by input mapper.
                .setStopPointLinkType(type)
                .setOriginEntityDatedJourneyStopPointKeyV2Ref(origin!!) // Coercion is OK, we never get here if fromRef is null.
                .setDestinationEntityDatedJourneyStopPointKeyV2Ref(destination!!) // Coercion is OK, we never get here if toRef is null.
                .setLength(input.calculatedLength)
                .setTrackLine(trackLine)
                .setTrafficPriorityPoints(points)
                .build()
        }
    }

    protected fun mapTrafficPriorityPoint(
        input: DTOTrafficPriorityPoint,
        path: String,
        context: OC,
    ): TrafficPriorityPoint? {
        val location = input.geoPoint.location?.toGeoJson(path = "$path.geoPoint.location", context)
        val triggerCode =
            input.triggerCode.toEntityValue(
                context = context,
                path = "$path.triggerCode",
                insight = "traffic.point.trigger.code",
                values = TRIGGER_CODES,
                // TODO: Should this be stricter?
                errorType = MappingDetails.Type.WARN,
                // TODO: Should this be stricter?
                unmappedPassThrough = true,
            )
        return context.build(path) {
            TrafficPriorityPoint
                .newBuilder()
                .setCode(input.code)
                .setLocation(location)
                .setTriggerCode(triggerCode)
                .build()
        }
    }

    protected fun <MC : MappingContext> Any.toGeoJson(
        path: String,
        context: MC,
    ): String? =
        try {
            JsonUtils.toJson(this)
        } catch (e: Exception) {
            val msg = "Error converting $path to GeoJSON string: ${e.message}"
            context.addError(path, msg, reason = "error.geo.json.serialize.exception")
            null
        }

    protected fun getDataSource(lifeCycleInfo: DTOLifeCycleInfo?): String = lifeCycleInfo?.dataSource ?: MapperUtils.Companion.UNKNOWN
}
