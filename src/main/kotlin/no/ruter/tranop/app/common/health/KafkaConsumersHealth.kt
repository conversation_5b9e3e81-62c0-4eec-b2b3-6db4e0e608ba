package no.ruter.tranop.app.common.health

import org.springframework.boot.actuate.health.Health
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.config.KafkaListenerEndpointRegistry
import org.springframework.stereotype.Component

@ConditionalOnProperty(prefix = "kafka.consumers", name = ["enabled"], havingValue = "true")
@Component
class KafkaConsumersHealth(
    private val kafkaListenerEndpointRegistry: KafkaListenerEndpointRegistry,
) : AbstractHealthService(
        "kafka-consumers",
    ) {
    override fun health(): Health {
        var totalUp = true
        val status =
            kafkaListenerEndpointRegistry.allListenerContainers.associate { container ->
                val groupId = container.groupId
                val listenerId = container.listenerId
                val up = container.isRunning
                totalUp = totalUp && up
                val partitions = container.assignedPartitions?.mapNotNull { it.partition() }

                "$groupId-$listenerId" to
                    mapOf(
                        "state" to if (up) "up" else "down",
                        "assigned-partitions" to partitions,
                    )
            }
        val health = Health.Builder().withDetails(status)
        if (totalUp) health.up() else health.down()

        return health.build()
    }
}
