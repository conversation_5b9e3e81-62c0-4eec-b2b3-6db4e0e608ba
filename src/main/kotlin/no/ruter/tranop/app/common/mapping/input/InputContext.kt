package no.ruter.tranop.app.common.mapping.input

import no.ruter.tranop.app.common.mapping.MappingContext
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import java.time.OffsetDateTime

interface InputContext : MappingContext {
    /** Main reference from input. Must be same as Kafka topic key. **/
    val ref: String?

    /** JSON-like path to main reference in input message. **/
    val refPath: String

    /** DTO message header from input message. **/
    val header: DTOMessageHeader?

    /** Pre-processed / parsed metadata from input message. **/
    val inputMetadata: InputMetadata

    /** Timestamp input message was received by input mapper. **/
    val received: OffsetDateTime

    val events: Collection<InputEvent>
}
