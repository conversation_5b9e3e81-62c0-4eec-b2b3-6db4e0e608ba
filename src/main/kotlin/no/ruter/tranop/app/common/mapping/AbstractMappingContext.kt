package no.ruter.tranop.app.common.mapping

abstract class AbstractMappingContext : MappingContext {
    private val _info = ArrayList<MappingDetails>()
    private val _errors = ArrayList<MappingDetails>()
    private val _warnings = ArrayList<MappingDetails>()

    override val info = _info
    override val errors = _errors
    override val warnings = _warnings

    override fun addInfo(
        path: String,
        msg: String,
        reason: String,
    ) {
        _info.add(MappingDetails(MappingDetails.Type.ERROR, path, "$msg [$path / $reason]", reason))
    }

    override fun addError(
        path: String,
        msg: String,
        reason: String,
    ) {
        _errors.add(MappingDetails(MappingDetails.Type.ERROR, path, "$msg [$path / $reason]", reason))
    }

    override fun addWarning(
        path: String,
        msg: String,
        reason: String,
    ) {
        _warnings.add(MappingDetails(MappingDetails.Type.ERROR, path, "$msg [$path / $reason]", reason))
    }
}
