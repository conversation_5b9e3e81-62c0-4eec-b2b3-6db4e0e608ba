package no.ruter.tranop.app.common.dataflow.kafka.config

import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaConfiguration
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = "kafka")
class KafkaConfig : KafkaConfiguration() {
    override fun toString(): String = super.configuration.toString()
}
