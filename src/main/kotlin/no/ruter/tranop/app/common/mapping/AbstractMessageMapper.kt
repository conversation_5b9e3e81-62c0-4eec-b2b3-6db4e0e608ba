package no.ruter.tranop.app.common.mapping

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.insight.Insight
import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.common.time.toLocalTime
import no.ruter.tranop.assignment.util.toLocalDate
import no.ruter.tranop.assignment.util.toOffsetDateTime
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime

abstract class AbstractMessageMapper(
    protected val timeService: TimeService,
    protected val insightService: InsightService,
) {
    companion object {
        const val PATH_ROOT = "$"

        // Note:
        // These are paths in the DTO (input) model, since both input mapping and output mapping refer to the input path
        // of a mapped property when reporting data errors and data warnings.
        const val PATH_ROOT_HEADER = "$PATH_ROOT.header"
        const val PATH_ROOT_EVENTS = "$PATH_ROOT.events"
    }

    protected val log: Logger = LoggerFactory.getLogger(javaClass)

    protected abstract val inputDesc: String
    protected abstract val outputDesc: String

    abstract fun recordDataError(
        context: InsightContext,
        key: String,
        msg: String? = null,
        cause: Exception? = null,
    )

    protected fun <C : MappingContext> recordMappingException(
        context: C,
        e: Exception,
    ) {
        val msg = "Error mapping $inputDesc [$context] to $outputDesc: ${e.message}"
        recordDataError(context, Insight.MAPPING_EXCEPTION, msg = msg, cause = e)
    }

    /** Record insight about data errors (and warnings), returning true if context has errors, false otherwise. **/
    protected fun <C : MappingContext> recordDataErrors(context: C): Boolean {
        if (context.hasWarnings) {
            recordDataErrors(context, context.warnings, log::debug)
        }

        return if (context.hasErrors) {
            recordDataErrors(context, context.errors, log::warn)
            true
        } else {
            false
        }
    }

    private fun <C : MappingContext> recordDataErrors(
        context: C,
        errors: List<MappingDetails>,
        logger: ((String) -> Unit)?,
    ) {
        var errorType: MappingDetails.Type? = null
        for (err in errors) {
            errorType = err.type
            recordDataError(
                context = context,
                key = err.insight,
                msg = err.msg,
            )
        }

        if (logger != null) {
            val details = MappingDetails.summary(errors)
            logger("Mapping of $inputDesc [$context] to $outputDesc reported data ${errorType}s:\n$details")
        }
    }

    protected fun <C : MappingContext> String?.notEmpty(
        context: C,
        path: String,
        insight: String,
        errorType: MappingDetails.Type,
        defaultValue: String? = this,
    ): String? =
        if (this.isNullOrEmpty()) {
            context.add(errorType, path, msg = "null or empty value: [$this]", reason = insight)
            defaultValue
        } else {
            this
        }

    protected fun <T, C : MappingContext> T?.notNull(
        context: C,
        path: String,
        insight: String,
        errorType: MappingDetails.Type,
        defaultValue: T? = null,
    ): T? =
        if (this == null) {
            context.add(errorType, path = path, msg = "required property is null", reason = insight)
            defaultValue
        } else {
            this
        }

    protected fun <C : MappingContext> String?.toLocalDate(
        context: C,
        path: String,
        insight: String,
        errorType: MappingDetails.Type,
    ): LocalDate? =
        if (this.isNullOrEmpty()) {
            context.add(errorType, path, msg = "missing date", reason = "missing.$insight")
            null
        } else {
            val res = this.toLocalDate()
            if (res == null) {
                context.add(errorType, path, msg = "invalid date: [$this]", reason = "invalid.$insight")
            }
            res
        }

    protected fun <C : MappingContext> String?.toLocalTime(
        context: C,
        path: String,
        insight: String,
        errorType: MappingDetails.Type,
    ): LocalTime? =
        if (this.isNullOrEmpty()) {
            context.add(errorType, path, msg = "missing time", reason = "missing.$insight")
            null
        } else {
            val res = this.toLocalTime()
            if (res == null) {
                context.add(errorType, path, msg = "invalid time: [$this]", reason = "invalid.$insight")
            }
            res
        }

    protected fun <C : MappingContext> String?.toOffsetDateTime(
        context: C,
        path: String,
        insight: String,
        errorType: MappingDetails.Type,
    ): OffsetDateTime? =
        if (this.isNullOrEmpty()) {
            context.add(errorType, path, msg = "missing timestamp", reason = "missing.$insight")
            null
        } else {
            val res = this.toOffsetDateTime()
            if (res == null) {
                context.add(errorType, path, msg = "invalid timestamp: [$this]", reason = "invalid.$insight")
            }
            res
        }

    protected fun <T, C : MappingContext> T.defined(
        context: C,
        path: String,
        insight: String = path,
        defined: Collection<T>,
        errorType: MappingDetails.Type,
    ): T {
        if (!defined.contains(this)) {
            val msg = "undefined value [$this]: allowed values are [${defined.joinToString(separator = ", ")}]"
            context.add(errorType, path, msg = msg, reason = "$insight.not.defined")
        }
        return this
    }

    /** Execute <code>block</code> to build result if context has no errors, otherwise return <code>null</code> **/
    protected fun <T> MappingContext.build(
        path: String,
        block: () -> T,
    ): T? =
        if (errors.isEmpty()) {
            try {
                block()
            } catch (e: Exception) {
                val msg = "Error mapping [$path] of $inputDesc to $outputDesc (build exception): ${e.message}"
                addError(path, msg, reason = Insight.MAPPING_BUILD_EXCEPTION)
                null
            }
        } else {
            null
        }
}
