package no.ruter.tranop.app.common.db.record

import no.ruter.tranop.app.common.time.TimeUtils
import org.jooq.Condition
import org.jooq.Record
import org.jooq.Table
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId

open class AbstractQueryBuilder<
    R : Record,
    T : Table<R>,
    Q : AbstractQueryBuilder<R, T, Q>,
>(
    protected val table: T,
) {
    companion object {
        const val MAX_LIMIT = 50
        const val DEFAULT_OFFSET = 0
        const val DEFAULT_LIMIT = 10

        @JvmStatic
        fun or(
            c1: Condition?,
            c2: Condition?,
        ): Condition? =
            if (c1 != null) {
                if (c2 == null) c1 else c1.or(c2)
            } else {
                c2
            }

        @JvmStatic
        fun and(
            c1: Condition?,
            c2: Condition?,
        ): Condition? =
            if (c1 != null) {
                if (c2 == null) c1 else c1.and(c2)
            } else {
                c2
            }
    }

    private var conditions: Conditions = Conditions()
    protected var operator: (Condition?, Condition?) -> Condition? = Companion::and

    open class Pagination(
        var limit: Int = DEFAULT_LIMIT,
        var offset: Int = DEFAULT_OFFSET,
    )

    open class Conditions(
        val page: Pagination = Pagination(),
        var condition: Condition? = null,
    )

    fun build(): Conditions = conditions

    @Suppress(names = ["UNCHECKED_CAST"])
    fun page(
        limit: Int?,
        offset: Int?,
    ): Q {
        limit(limit)
        offset(offset)
        return this as Q
    }

    @Suppress(names = ["UNCHECKED_CAST"])
    fun limit(limit: Int?): Q {
        limit?.let { conditions.page.limit = it.coerceAtLeast(0).coerceAtMost(MAX_LIMIT) }
        return this as Q
    }

    @Suppress(names = ["UNCHECKED_CAST"])
    fun offset(offset: Int?): Q {
        offset?.let { conditions.page.offset = it.coerceAtLeast(0) }
        return this as Q
    }

    /** Append condition to query, using the currently active condition operator. **/
    @Suppress(names = ["UNCHECKED_CAST"])
    protected fun append(condition: Condition?): Q {
        conditions.condition = operator(conditions.condition, condition)
        return this as Q
    }

    // Note: We have custom date parsing for query builder, since we want to be throwing IllegalArgumentException on errors.
    protected fun String.parseDate(name: String): LocalDate {
        try {
            return LocalDate.parse(this)
        } catch (e: Exception) {
            throw IllegalArgumentException("Invalid $name: $this")
        }
    }

    // Note: We have custom date-time offset parsing for query builder compared to other "business" code, since we are typically
    // interpreting user-provided input values obtained via APIs, meaning we should be more lenient about which inputs we accept and
    // make a best effort attempt at parsing it into something usable.
    protected fun String.parseOffsetDateTime(
        name: String,
        now: OffsetDateTime? = null,
        defaultZone: ZoneId = TimeUtils.ZONE_ID_OSLO,
    ): OffsetDateTime {
        try {
            return OffsetDateTime.parse(this)
        } catch (e1: Exception) {
            return try {
                LocalDateTime.parse(this).atZone(defaultZone).toOffsetDateTime()
            } catch (e2: Exception) {
                try {
                    LocalDate
                        .parse(this)
                        .atTime(LocalTime.MIDNIGHT)
                        .atZone(defaultZone)
                        .toOffsetDateTime()
                } catch (e3: Exception) {
                    try {
                        val t = now ?: OffsetDateTime.now()
                        val date = t.atZoneSameInstant(defaultZone).toLocalDate()
                        LocalTime
                            .parse(this)
                            .atDate(date)
                            .atZone(defaultZone)
                            .toOffsetDateTime()
                    } catch (e4: Exception) {
                        throw IllegalArgumentException("Invalid $name: $this")
                    }
                }
            }
        }
    }
}
