package no.ruter.tranop.app.common.dataflow.kafka.config

import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopic
import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopics
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "kafka.topics.input")
data class KafkaInputTopics(
    val datedJourney: KafkaTopic,
    val assignment: KafkaTopic,
    val entityVehicleApi: KafkaTopic,
) : KafkaTopics // TODO: Get rid of this dependency.
