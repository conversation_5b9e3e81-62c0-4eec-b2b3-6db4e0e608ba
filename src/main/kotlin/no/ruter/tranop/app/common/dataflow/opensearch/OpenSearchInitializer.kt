package no.ruter.tranop.app.common.dataflow.opensearch

import jakarta.annotation.PostConstruct
import no.ruter.rdp.logging.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

@Profile("!test")
@Component
@ConditionalOnProperty(name = ["opensearch.enabled"], havingValue = "true")
class OpenSearchInitializer(
    private val openSearchService: OpenSearchService,
    private val openSearchProperties: OpenSearchProperties,
) {
    private val logger = LoggerFactory.getLogger(javaClass)

    @PostConstruct
    fun initIndices() {
        val indexName = openSearchProperties.journeyIndexName
        val result = openSearchService.createIndexIfNotExists(indexName)
        val msg =
            when (result) {
                true -> "created"
                false -> "already exists"
            }
        logger.info("index $indexName $msg")
    }
}
