package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.insight.InsightService
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.serialization.Deserializer

abstract class AbstractKafkaConsumer(
    val channel: DataChannel,
    private val insightService: InsightService,
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    fun <V> process(
        record: ConsumerRecord<String?, ByteArray?>,
        deserializer: Deserializer<V>,
        handler: (String?, V?) -> ProcessingContext,
    ) {
        val topicName = record.topic()
        insightService.messageReceived(topicName)
        val (key, value) = deserialize(topicName, deserializer, record)
        val context = handler(key, value)
        insightService.insight(context)
    }

    private fun <V> deserialize(
        topicName: String,
        deserializer: Deserializer<V>,
        consumerRecord: ConsumerRecord<String?, ByteArray?>,
    ): Pair<String?, V?> {
        val key = consumerRecord.key()
        val obj =
            consumerRecord.value()?.let { value ->
                deserializer.deserialize(
                    topicName,
                    value,
                )
            }
        return Pair(key, obj)
    }
}
