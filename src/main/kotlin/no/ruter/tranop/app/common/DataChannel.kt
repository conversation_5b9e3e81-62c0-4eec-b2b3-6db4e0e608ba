package no.ruter.tranop.app.common

import no.ruter.rdp.metrics.InsightUtils

class DataChannel private constructor(
    val name: String,
    val direction: Direction,
) {
    enum class Direction(
        val value: String,
    ) {
        IN(value = "input"),
        OUT(value = "output"),
        INTERNAL(value = "internal"),
        ;

        override fun toString(): String = value
    }

    val subject: String = "$direction-$name".lowercase()
    val insightKey: String = InsightUtils.normalize("$direction.$name".lowercase())

    override fun toString(): String = subject

    companion object {
        private val defined = LinkedHashMap<String, DataChannel>()

        fun define(
            name: String,
            direction: Direction,
        ): DataChannel {
            val ch = DataChannel(name, direction)
            val key = ch.subject
            return defined[key] ?: ch.apply { defined[key] = ch }
        }

        // Subjects
        private const val S_TRAFFIC_EVENT = "traffic-event"

        // Input channels
        val TRAFFIC_EVENT_IN = DataChannel(name = S_TRAFFIC_EVENT, direction = Direction.IN)
    }
}
