package no.ruter.tranop.app.common

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.metrics.InsightUtils
import no.ruter.tranop.app.common.insight.ErrorType
import no.ruter.tranop.app.common.insight.InsightContext
import no.ruter.tranop.assignment.dto.detailsToString
import no.ruter.tranop.assignment.dto.model.DTOAssignmentStatusDetails
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.assignment.dto.util.DTOUtils
import no.ruter.tranop.assignment.util.TimeUtils
import java.time.LocalDate
import java.time.OffsetDateTime
import kotlin.reflect.KFunction1

abstract class ValidationContext : InsightContext {
    open val statusDetails: MutableList<DTOAssignmentStatusDetails> = mutableListOf()

    fun valid() = !invalid()

    fun invalid() = DTOUtils.hasError(statusDetails)

    fun addStatusDetail(
        code: DTOStatusCode,
        reason: String,
        desc: String,
        field: String? = null,
        value: String? = null,
    ): DTOAssignmentStatusDetails {
        val statusDetail = DTOUtils.createStatusDetails(code, reason, desc, field, value)
        statusDetails.add(statusDetail)
        return statusDetail
    }

    fun addDetails(details: List<DTOAssignmentStatusDetails>): ValidationContext {
        statusDetails.addAll(details)
        return this
    }

    fun addSimpleDetail(
        fieldName: String,
        fieldValue: Int,
    ) {
        statusDetails.add(
            DTOAssignmentStatusDetails().apply {
                this.fieldName = fieldName
                this.fieldValue = fieldValue.toString()
            },
        )
    }

    fun addInternalException(
        e: Throwable,
        log: Logger,
        type: ErrorType,
        subject: String,
        error: Boolean = true,
        metadata: Map<String, Any?>,
    ): DTOAssignmentStatusDetails {
        val desc = e.message ?: e.localizedMessage ?: "<no message>"
        val reason = InsightUtils.normalize("exception.$type.$subject")
        val statusDetail =
            addStatusDetail(
                code = DTOStatusCode.ERR_INTERNAL,
                reason = reason,
                desc = desc,
            )

        val md = this.metadata.plus(metadata).toMutableMap()
        md["exception-detail"] = mutableListOf(statusDetail).detailsToString()
        md["exception-stack-trace"] = e.stackTraceToString()

        val msg = "$reason: $desc"
        if (error) {
            log.error(msg, md, e)
        } else {
            log.warn(msg, md, e)
        }
        return statusDetail
    }

    private fun validateOK(
        value: Any?,
        field: String,
        desc: String? = null,
        includeOk: Boolean = false,
    ) {
        if (includeOk) {
            val msg = "Field validated [$field]"
            addStatusDetail(
                code = DTOStatusCode.OK,
                reason = "validate.ok",
                desc = desc?.let { "$msg: $desc" } ?: "$msg: OK",
                field = field,
                value = value?.toString(),
            )
        }
    }

    fun validateFailed(
        value: Any?,
        field: String,
        error: Boolean = false,
        desc: String? = null,
    ) {
        val msg = "Validation ${if (error) "error" else "warning"} [$field]"
        addStatusDetail(
            code = if (error) DTOStatusCode.ERR_VALIDATE else DTOStatusCode.INF_QUALITY,
            reason = field,
            desc = desc?.let { "$msg: $desc" } ?: msg,
            field = field,
            value = value?.toString(),
        )
    }

    fun <T> validateNotNull(
        value: T?,
        field: String,
        error: Boolean = false,
        includeOk: Boolean = false,
    ): T? {
        if (value == null) {
            validateFailed(value = null, field, error, desc = "not set / null value")
        } else {
            validateOK(value, field, desc = "not null", includeOk)
        }
        return value
    }

    fun <C, T : Collection<C>> validateSizeGreaterThan(
        collection: T?,
        size: Int,
        field: String,
        error: Boolean = false,
        includeOk: Boolean = false,
    ): T? {
        val notEmpty = validateNotEmpty(collection, field)
        notEmpty?.let {
            if (it.size < size) {
                validateFailed(value = collection, field, error, desc = "collection has $size elements or less")
            } else {
                validateOK(collection, field, desc = "not empty", includeOk)
            }
        }
        return collection
    }

    private fun <C, T : Collection<C>> validateNotEmpty(
        value: T?,
        field: String,
        error: Boolean = false,
        includeOk: Boolean = false,
    ): T? {
        if (value.isNullOrEmpty()) {
            validateFailed(value = null, field, error, desc = "empty collection")
        } else {
            validateOK(value, field, desc = "not empty", includeOk)
        }
        return value
    }

    fun validateNotBlank(
        value: String?,
        field: String,
        error: Boolean = false,
        includeOk: Boolean = false,
    ): String? {
        if (value.isNullOrBlank()) {
            validateFailed(value, field, error, desc = "null / not set, blank or empty")
        } else {
            validateOK(value, field, desc = "not blank", includeOk)
        }
        return value
    }

    fun validateLocalDate(
        value: String?,
        field: String,
        required: Boolean = true,
        type: String? = null,
        includeOk: Boolean = false,
    ): LocalDate? =
        validateField(
            value = value,
            field = field,
            required = required,
            type = "date",
            qualifier = type,
            converter = TimeUtils::toLocalDate,
            includeOk = includeOk,
        )

    fun validateInt(
        value: String?,
        field: String,
        required: Boolean = true,
        type: String? = null,
        includeOk: Boolean = false,
    ): Int? =
        validateField(
            value = value,
            field = field,
            required = required,
            type = "date",
            qualifier = type,
            converter = { s ->
                try {
                    Integer.parseInt(s)
                } catch (e: Exception) {
                    null
                }
            },
            includeOk = includeOk,
        )

    fun validateTimestamp(
        value: String?,
        field: String,
        required: Boolean = true,
        type: String? = null,
        includeOk: Boolean = false,
    ): OffsetDateTime? =
        validateField(
            value = value,
            field = field,
            required = required,
            type = "timestamp",
            qualifier = type,
            converter = TimeUtils::toOffsetDateTime,
            includeOk = includeOk,
        )

    private fun <T> validateField(
        value: String?,
        field: String,
        required: Boolean,
        type: String,
        qualifier: String?,
        converter: ((String?) -> T?),
        includeOk: Boolean,
    ): T? {
        fun describe(reason: String): String = if (qualifier.isNullOrBlank()) "$reason $type" else "$reason $qualifier $type"

        if (value.isNullOrBlank()) {
            validateFailed(value, field, required, describe(reason = "missing"))
            return null
        }

        val res = converter(value)
        if (res == null) {
            validateFailed(value, field, required, desc = "${describe(reason = "invalid")} [$value]")
            return null
        }

        validateOK(value, field, describe(reason = "valid"), includeOk)
        return res
    }

    fun <T> validateValueType(
        all: Set<T>,
        value: String?,
        creator: KFunction1<String, T>,
        field: String,
        default: T,
        required: Boolean = true,
    ): T? {
        validateNotBlank(
            value = value,
            field = field,
            error = required,
        )
        value?.let {
            val valueType = creator(it)
            if (all.contains(valueType)) {
                return valueType
            } else {
                validateFailed(
                    value = value,
                    field = field,
                    error = required,
                    desc = "Invalid code [$value], possible values [${all.joinToString(", ")}]",
                )
            }
        }
        return default
    }
}
