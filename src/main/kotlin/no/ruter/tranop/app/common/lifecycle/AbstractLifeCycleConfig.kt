package no.ruter.tranop.app.common.lifecycle

import no.ruter.rdp.common.json.JsonUtils
import org.springframework.boot.context.properties.NestedConfigurationProperty

abstract class AbstractLifeCycleConfig(
    private val description: String,
) {
    var enabled = false

    @NestedConfigurationProperty
    var routines: Map<String, RoutineConfig> = mapOf()

    fun get(name: String) =
        routines[name] ?: throw IllegalStateException(
            "No config found for '$description.$name': " +
                JsonUtils.toJson(routines, true),
        )
}
