package no.ruter.tranop.app.common.db.xref

class ExternalRefs {
    companion object {
        private val NONE = emptySet<String>()
    }

    var size: Int = 0
        private set

    private val externalRefs = LinkedHashMap<ExternalRefType, LinkedHashSet<String>>()

    fun all(): Map<ExternalRefType, Set<String>> = externalRefs

    fun get(type: ExternalRefType): Set<String> = externalRefs[type] ?: NONE

    fun first(type: ExternalRefType): String? = externalRefs[type]?.first()

    fun add(
        type: ExternalRefType?,
        value: String?,
    ): ExternalRefs {
        if (type != null) {
            value?.let { v ->
                if (v.isNotEmpty()) {
                    externalRefs.getOrPut(type) { LinkedHashSet() }.let { target ->
                        if (target.add(value)) {
                            size++
                        }
                    }
                }
            }
        }
        return this
    }

    fun add(
        type: ExternalRefType,
        values: Collection<String?>?,
    ): ExternalRefs {
        values?.let { vals ->
            if (vals.isNotEmpty()) {
                externalRefs.getOrPut(type) { LinkedHashSet() }.let { target ->
                    for (value in values) {
                        value?.let { v ->
                            if (v.isNotEmpty() && target.add(v)) {
                                size++
                            }
                        }
                    }
                }
            }
        }
        return this
    }

    fun isEmpty(): Boolean = externalRefs.isEmpty()

    fun isNotEmpty(): Boolean = externalRefs.isNotEmpty()

    fun update(externalRefs: ExternalRefs) {
        externalRefs.externalRefs.entries.forEach { add(it.key, it.value) }
    }

    override fun toString(): String =
        externalRefs.keys.sortedBy { it.name }.joinToString(separator = ", ") { "${it.name}=${externalRefs[it]}" }
}
