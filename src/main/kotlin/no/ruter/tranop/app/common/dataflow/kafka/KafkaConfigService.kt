package no.ruter.tranop.app.common.dataflow.kafka

import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaConfiguration
import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopic
import no.ruter.rdp.messaging.kafka.streams.common.config.KafkaTopicBinding
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaInputTopics
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaOutputTopics
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

@Service
class KafkaConfigService(
    private val serdes: KafkaSerdeProvider,
    override val configuration: KafkaConfiguration,
    override val inputTopics: KafkaInputTopics,
    override val outputTopics: KafkaOutputTopics,
) : no.ruter.rdp.messaging.kafka.streams.common.config.KafkaConfigService<KafkaInputTopics, KafkaOutputTopics> {
    private final val datedJourney by lazy {
        bind(outputTopics.datedJourney, serdes.datedJourneyEntitySerde)
    }

    private final val assignmentJourney by lazy {
        bind(outputTopics.assignmentJourney, serdes.assignmentJourneyDTOSerde)
    }

    private final val assignmentJourneyTombstone by lazy {
        bind(outputTopics.assignmentJourney, serdes.byteArraySerde)
    }

    private final val stopPoint by lazy {
        bind(outputTopics.stopPoint, serdes.stopPointEntitySerde)
    }

    private final val stopPointDto by lazy {
        bind(outputTopics.stopPointDto, serdes.stopPointDTOSerde)
    }

    private final val stopPointLink by lazy {
        bind(outputTopics.stopPointLink, serdes.stopPointLinkEntitySerde)
    }

    private final val stopPointLinkDto by lazy {
        bind(outputTopics.stopPointLinkDto, serdes.stopPointLinkDTOSerde)
    }

    private final val serviceDeviationDto by lazy {
        bind(outputTopics.serviceDeviationDTO, serdes.serviceDeviationDTOSerde)
    }

    override val topicsToCreate: List<KafkaTopic>
        get() = emptyList()

    private fun <V, VS : Serde<V>> bind(
        topic: KafkaTopic,
        valueSerde: VS,
    ): KafkaTopicBinding<String?, Serde<String?>, V, VS> {
        valueSerde.configure(configuration.configuration, false)
        return KafkaTopicBinding(topic, serdes.stringSerde, valueSerde)
    }

    val datedJourneyOutputProducer = makeProducerBinding(datedJourney)

    val assignmentJourneyOutputProducer = makeProducerBinding(assignmentJourney)

    val assignmentJourneyTombstoneProducer = makeProducerBinding(assignmentJourneyTombstone)

    val stopPointOutputProducer = makeProducerBinding(stopPoint)

    val stopPointDtoOutputProducer = makeProducerBinding(stopPointDto)

    val stopPointLinkOutputProducer = makeProducerBinding(stopPointLink)

    val stopPointLinkDtoOutputProducer = makeProducerBinding(stopPointLinkDto)

    val serviceDeviationDtoOutputProducer = makeProducerBinding(serviceDeviationDto)

    protected fun <V, VS : Serde<V>> makeProducerBinding(
        topicBinding: KafkaTopicBinding<String?, Serde<String?>, V, VS>,
    ): KafkaProducerBinding<String?, Serde<String?>, V, VS> {
        val producer =
            KafkaProducer<String?, V>(
                configuration.properties,
                topicBinding.keySerde.serializer(),
                topicBinding.valueSerde.serializer(),
            )
        return KafkaProducerBinding(topicBinding, producer)
    }
}
