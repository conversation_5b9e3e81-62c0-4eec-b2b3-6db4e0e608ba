package no.ruter.tranop.app.common.lifecycle

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.rdp.metrics.InsightUtils
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import java.time.OffsetDateTime

abstract class AbstractLifeCycleRoutine(
    val appInfoProperties: AppInfoProperties,
    val name: String,
    private val insightService: InsightService,
    abstractLifeCycleConfig: AbstractLifeCycleConfig,
    val channel: DataChannel,
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass.canonicalName)
    val config = abstractLifeCycleConfig.get(name)
    val excludeOperatorContracts = config.getExcludes(RoutineConfig.OPERATOR_CONTRACTS)
    val excludeJourneyTypes = config.getExcludes(RoutineConfig.JOURNEY_TYPES)

    val appName get() = appInfoProperties.name
    val subject: String = InsightUtils.normalize("${channel.insightKey}.lifecycle")

    companion object {
        const val LC_TYPE_REPUBLISH = "republish"
        const val LC_TYPE_PUBLISH = "publish"
        const val LC_TYPE_DELETE = "delete"
        const val LC_TYPE_TOMBSTONE = "tombstone"
        const val LC_TYPE_STATISTICS = "statistics"
    }

    val frequent: Boolean get() = config.frequent
    val daily: Boolean get() = !frequent
    val enabled: Boolean get() = config.enabled
    val olderThan get() = config.olderThan

    abstract fun execute(started: OffsetDateTime): Int

    private fun routineInsightType(specification: String? = null) = insightType("routine", name, specification)

    fun recordInsight(
        specification: String? = null,
        result: Int = 1,
    ) {
        if (result > 0) {
            val insightType = routineInsightType(specification)
            insightService.recordDataInsight(subject, insightType, result.toDouble())
        }
    }

    fun handleException(
        specification: String? = null,
        e: Exception,
        msg: String? = null,
        metadata: Map<String, Any?>? = emptyMap(),
    ) {
        val insightType = insightType(routineInsightType(specification), "exception")
        val entries = metadata ?: emptyMap()
        val message = msg ?: e.localizedMessage
        logger.error("[$subject/$insightType] $message", entries, e)
        recordInsight(specification = "exception")
    }

    private fun insightType(vararg parts: String?): String =
        InsightUtils.normalize(
            listOfNotNull(
                *parts,
            ).joinToString(separator = "."),
        )
}
