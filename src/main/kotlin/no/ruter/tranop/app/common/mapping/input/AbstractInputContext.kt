package no.ruter.tranop.app.common.mapping.input

import no.ruter.tranop.app.common.mapping.AbstractMappingContext
import no.ruter.tranop.app.common.mapping.MapperUtils

abstract class AbstractInputContext :
    AbstractMappingContext(),
    InputContext {
    override val inputMetadata = InputMetadata()

    override val traceId: String
        get() = inputMetadata.traceId ?: MapperUtils.TRACE_ID_NONE

    override fun toString(): String = "$refPath: $ref"
}
