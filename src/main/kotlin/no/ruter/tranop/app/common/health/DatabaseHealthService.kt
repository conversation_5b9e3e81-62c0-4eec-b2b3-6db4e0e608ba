package no.ruter.tranop.app.common.health

import no.ruter.tranop.app.common.db.config.DatabaseProbeConfig
import no.ruter.tranop.app.common.time.TimeService
import org.jooq.DSLContext
import org.springframework.boot.actuate.health.Health
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.util.concurrent.atomic.AtomicInteger

@Component
class DatabaseHealthService(
    config: DatabaseProbeConfig,
    private val dslContext: DSLContext,
    private val timeService: TimeService,
) : AbstractHealthService(
        "database",
    ) {
    private final val probeEvery = config.every.inWholeSeconds
    private final val failureThreshold = config.failureThreshold

    var lastState: Health = up()
    var lastProbe: OffsetDateTime = timeService.now().minusMinutes(probeEvery)
    var consecutiveFailCount: AtomicInteger = AtomicInteger(0)

    override fun health(): Health {
        val boundary = timeService.now().minusSeconds(probeEvery)
        if (lastProbe.isBefore(boundary)) {
            val healthy = probe()
            lastState =
                if (healthy) {
                    up()
                } else {
                    if (consecutiveFailCount.get() > failureThreshold) {
                        down("Consecutive fail count $consecutiveFailCount")
                    } else {
                        lastState
                    }
                }
        }
        return lastState
    }

    fun probe(): Boolean =
        try {
            lastProbe = timeService.now()
            dslContext.selectOne().fetch()
            consecutiveFailCount = AtomicInteger(0)
            true
        } catch (e: Exception) {
            log.error("DB Health probe detected issues", e)
            consecutiveFailCount.getAndIncrement()
            false
        }
}
