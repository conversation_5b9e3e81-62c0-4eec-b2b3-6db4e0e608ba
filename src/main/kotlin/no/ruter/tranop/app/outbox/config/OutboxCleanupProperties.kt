package no.ruter.tranop.app.outbox.config

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = OutboxCleanupProperties.CONF_PREFIX)
class OutboxCleanupProperties {
    var enabled = false
    var schedulerLockName = "outbox-cleanup-daily-001"
    var cron = "0 0 1 * * *" // Daily at 1
    var lockAtMostFor = "3m"
    var lockAtLeastFor = "30s"
    var retentionDays = 30L // Retain events for 30 days

    companion object {
        const val CONF_PREFIX = "app.config.outbox.cleanup"
    }
}
