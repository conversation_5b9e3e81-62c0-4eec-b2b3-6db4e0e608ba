package no.ruter.tranop.app.outbox.db

import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.InternalOutbox
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.assignmentmanager.db.sql.tables.OutboxTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.jooq.DSLContext
import org.jooq.exception.DataChangedException
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import kotlin.math.pow

@Component
class OutboxRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        OutboxRecord,
        OutboxTable,
        InternalOutbox,
        OutboxRecordMapper,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = SORT_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = OutboxRecordMapper(insightService),
    ) {
    companion object {
        val TYPE = RecordType.OUTBOX
        val TABLE = TYPE.table

        val SORT_FIELDS =
            listOf(
                TABLE.CREATED_AT.asc(),
                TABLE.ID.asc(),
            )
    }

    private val logger = LoggerFactory.Companion.getLogger(javaClass.canonicalName)

    fun store(outbox: Outbox) {
        val ref = outbox.ref ?: MapperUtils.Companion.randomId(prefix = "outbox-")
        fetchByRef(ref)?.let {
            logger.warn("Outbox record with ref $ref already exists, skipping store operation.")
        } ?: storeRecord(wrapSpec(ref, outbox, dslContext.newRecord(table)))
    }

    fun findUnpublished(
        dataType: DBOutboxDataType,
        targetType: DBOutboxTargetType,
        pagination: AbstractQueryBuilder.Pagination,
        retryCount: Int = 5,
        eligibleBefore: OffsetDateTime = timeService.now(),
    ): List<InternalOutbox> =
        fetch(
            TABLE.PUBLISHED_AT.isNull
                .and(TABLE.DATA_TYPE.eq(dataType.value))
                .and(TABLE.TARGET_TYPE.eq(targetType.value))
                .and(TABLE.RETRIES.le(retryCount))
                .and(
                    TABLE.NEXT_ATTEMPT_AT.isNull.or(
                        TABLE.NEXT_ATTEMPT_AT.lessOrEqual(eligibleBefore),
                    ),
                ),
            pagination,
        )

    fun storeRecord(
        data: InternalOutbox,
        now: OffsetDateTime? = null,
    ): Boolean {
        recordMapper.updateRecord(source = data.data, record = data.record, now = now ?: timeService.now())
        return data.record.store() == 1
    }

    fun markPublished(
        ref: List<String>,
        now: OffsetDateTime,
    ): Int? =
        try {
            dslContext
                .update(table)
                .set(table.PUBLISHED_AT, now)
                .where(table.REF.`in`(ref))
                .execute()
        } catch (exception: DataChangedException) {
            log.warn("Could not update published_at timestamp on outbox [ref=$ref]", exception)
            null
        }

    fun markAsFailed(
        list: List<InternalOutbox>,
        offsetDateTime: OffsetDateTime = timeService.now(),
    ) = try {
        logger.warn("Marking outbox records as failed for refs: ${list.map { it.record.ref }}")
        dslContext.batched { batch ->
            list.forEach { internalOutbox ->
                val nextAttemptAt = offsetDateTime.plusMinutes(2.0.pow(internalOutbox.record.retries + 1).toLong())
                batch
                    .dsl()
                    .update(table)
                    .set(table.NEXT_ATTEMPT_AT, nextAttemptAt)
                    .set(table.RETRIES, table.RETRIES.plus(1))
                    .where(table.REF.eq(internalOutbox.record.ref))
                    .execute()
            }
        }
    } catch (exception: DataChangedException) {
        log.warn("Failed to update retries for refs: ${list.map { it.record.ref }}", exception)
    }

    fun newRecord(outbox: Outbox): InternalOutbox {
        val ref = outbox.ref ?: MapperUtils.Companion.randomId(prefix = "outbox-")
        return wrapSpec(ref, outbox, dslContext.newRecord(table))
    }

    private fun wrapSpec(
        ref: String,
        outbox: Outbox,
        record: OutboxRecord,
    ): InternalOutbox =
        InternalOutbox(
            Outbox(
                ref = ref,
                dataType = outbox.dataType,
                targetType = outbox.targetType,
                payloadRef = outbox.payloadRef,
                payload = outbox.payload,
            ),
            record,
        )

    fun deleteOldProcessedEvents(time: OffsetDateTime): Int? =
        try {
            logger.info("Deleting old processed outbox events older than $time")
            dslContext
                .deleteFrom(table)
                .where(
                    table.PUBLISHED_AT.isNotNull
                        .and(table.PUBLISHED_AT.lt(time)),
                ).execute()
        } catch (exception: DataChangedException) {
            logger.warn("Could not delete old processed outbox events", exception)
            null
        }
}
