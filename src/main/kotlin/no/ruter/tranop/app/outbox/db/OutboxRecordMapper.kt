package no.ruter.tranop.app.outbox.db

import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.outbox.InternalOutbox
import no.ruter.tranop.app.outbox.Outbox
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.OutboxRecord
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime

class OutboxRecordMapper(
    private val insightService: InsightService,
) : RecordMapper<OutboxRecord, InternalOutbox> {
    private val appName: String
        get() = insightService.appName

    override fun map(record: OutboxRecord) =
        InternalOutbox(
            data =
                Outbox(
                    ref = record.ref,
                    dataType = DBOutboxDataType.of(record.dataType),
                    targetType = DBOutboxTargetType.of(record.targetType),
                    payloadRef = record.payloadRef,
                    payload = record.payload.toString(),
                ),
            record = record,
        )

    fun updateRecord(
        source: Outbox,
        record: OutboxRecord,
        now: OffsetDateTime,
    ) {
        record.apply {
            ref = source.ref
            dataType = source.dataType.value
            targetType = source.targetType.value
            payloadRef = source.payloadRef
            payload = JSON.valueOf(source.payload)
            createdAt = createdAt ?: now
            createdBy = createdBy ?: appName
            deletedAt = null
            modifiedAt = now
            modifiedBy = modifiedBy ?: appName
        }
    }
}
