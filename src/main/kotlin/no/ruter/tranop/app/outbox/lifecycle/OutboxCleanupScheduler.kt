package no.ruter.tranop.app.outbox.lifecycle

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.app.outbox.config.OutboxCleanupProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = OutboxCleanupProperties.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class OutboxCleanupScheduler(
    private val outboxService: OutboxService,
    private val config: OutboxCleanupProperties,
    private val timeService: TimeService,
) {
    private val log = LoggerFactory.Companion.getLogger(javaClass.canonicalName)

    companion object {
        const val CONF_PREFIX = OutboxCleanupProperties.CONF_PREFIX
    }

    @Scheduled(
        cron = "\${${CONF_PREFIX}.cron}",
    )
    @SchedulerLock(
        name = "\${${CONF_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${CONF_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${CONF_PREFIX}.lockAtLeastFor}",
    )
    fun dailyCleanup() {
        log.info("Running outbox cleanup routine")
        outboxService.deleteOldProcessedEvents(timeService.now().minusDays(config.retentionDays))
    }
}
