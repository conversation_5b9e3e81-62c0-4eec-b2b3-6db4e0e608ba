package no.ruter.tranop.app.outbox

import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.db.OutboxRepository
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@Service
class OutboxService(
    private val repository: OutboxRepository,
    private val timeService: TimeService,
) {
    fun findUnpublished(
        dataType: DBOutboxDataType,
        targetType: DBOutboxTargetType,
        pagination: AbstractQueryBuilder.Pagination,
        retryCount: Int = 5,
    ) = repository.findUnpublished(
        dataType = dataType,
        targetType = targetType,
        pagination = pagination,
        retryCount = retryCount,
        eligibleBefore = timeService.now(),
    )

    fun markAsPublished(refList: List<String>) = if (refList.isEmpty()) 0 else repository.markPublished(refList, timeService.now())

    fun markAsFailed(refList: List<InternalOutbox>) = repository.markAsFailed(refList, timeService.now())

    fun deleteOldProcessedEvents(time: OffsetDateTime) = repository.deleteOldProcessedEvents(time)
}
