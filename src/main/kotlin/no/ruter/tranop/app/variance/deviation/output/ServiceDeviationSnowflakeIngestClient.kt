package no.ruter.tranop.app.variance.deviation.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.app.variance.deviation.db.InternalServiceDeviation
import no.ruter.tranop.operation.deviation.bi.model.BIDateTimeRange
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviation
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviationParameters
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviationSpec
import no.ruter.tranop.operation.deviation.bi.model.BIServiceImpact
import no.ruter.tranop.operation.deviation.bi.model.BIServiceVarianceMetadataEntry
import no.ruter.tranop.operation.deviation.bi.model.BIServiceVarianceTraceInfo
import no.ruter.tranop.operation.deviation.bi.model.common.BIServiceDeviationReason
import org.springframework.stereotype.Service

/**
 * Client for streaming ingestion of service deviations into Snowflake
 *
 * @property config Configuration for streaming ingest.
 * @property clientFactory Factory for creating Snowflake clients and channels.
 */
@Service
class ServiceDeviationSnowflakeIngestClient(
    config: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<BIServiceDeviation>(
        config,
        clientFactory,
        CLIENT_BUILDER_NAME,
        CHANNEL_BUILDER_NAME,
    ) {
    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_DEVIATIONS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_DEVIATION_CHANNEL"
    }

    override fun BIServiceDeviation.toIngestMap(): Map<String, Any> =
        mapOf(
            "OWNER_ID" to trace.ownerId,
            "AUTHORITY_ID" to trace.authorityId,
            "OPERATOR_ID" to trace.operatorId,
            "QUAY_REFS" to spec.impact.stopPoints,
            "DATEDJOURNEYV2_REFS" to spec.impact.journeys,
            "LINE_REFS" to spec.impact.lines,
            "JSONDATA" to JsonUtils.toJson(this),
        )
}

fun InternalServiceDeviation.toBIServiceDeviation(): BIServiceDeviation =
    with(data) {
        BIServiceDeviation(
            BIServiceVarianceTraceInfo(
                ownerId,
                operatorId,
                authorityId,
            ),
            ref ?: "",
            BIServiceDeviationSpec(
                spec?.code?.value,
                BIServiceDeviationReason(
                    spec?.reason?.code,
                    spec?.reason?.comment,
                ),
                BIServiceImpact(
                    spec?.impact?.lines?.map {
                        it.spec.lineId
                    },
                    spec?.impact?.journeys?.map {
                        it.journey.spec.journeyId
                    },
                    spec?.impact?.stopPoints?.map {
                        it.spec.stopPointId
                    },
                ),
                BIDateTimeRange(
                    spec?.duration?.start,
                    spec?.duration?.end,
                ),
                spec?.metadata?.map {
                    BIServiceVarianceMetadataEntry(it.key?.value, it.value)
                } ?: emptyList(),
                BIServiceDeviationParameters(
                    spec?.parameters?.vehicleId,
                    spec?.parameters?.delayMinutes,
                    spec?.parameters?.operatorExempt,
                ),
            ),
        )
    }
