package no.ruter.tranop.app.variance.mitigation.output

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = ServiceMitigationOutboxStreamingConfig.CONF_PREFIX)
class ServiceMitigationOutboxStreamingConfig {
    var batchSize = 1000
    var enabled = false
    var retryCount = 5

    companion object {
        const val CONF_PREFIX = "app.config.outbox.snowflake-streaming-service-mitigation"
    }
}
