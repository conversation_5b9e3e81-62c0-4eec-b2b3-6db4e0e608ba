package no.ruter.tranop.app.variance.mitigation.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviation
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviationSpec
import no.ruter.tranop.operation.deviation.bi.model.BIServiceVarianceTraceInfo
import no.ruter.tranop.operation.mitigation.bi.model.BIServiceMitigation
import org.springframework.stereotype.Service
import java.time.Instant.now

/**
 * Client for streaming ingestion of service mitigations into Snowflake.
 *
 * @property config Configuration for streaming ingest.
 * @property clientFactory Factory for creating Snowflake clients and channels.
 */
@Service
class ServiceMitigationSnowflakeIngestClient(
    config: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<BIServiceMitigation>(
        config,
        clientFactory,
        CLIENT_BUILDER_NAME,
        CHANNEL_BUILDER_NAME,
    ) {
    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_MITIGATIONS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_MITIGATION_CHANNEL"
    }

    override fun BIServiceMitigation.toIngestMap(): Map<String, Any> =
        // TODO lag konstanter utifra disse
        mapOf(
            "REF" to ref,
            "OWNER_ID" to trace.ownerId,
            "AUTHORITY_ID" to trace.authorityId,
            "OPERATOR_ID" to trace.operatorId,
            "QUAY_REFS" to spec.impact.stopPoints,
            "DATED_JOURNEY_V2_REFS" to spec.impact.journeys,
            "LINE_REFS" to spec.impact.lines,
            "JSON_DATA" to JsonUtils.toJson(this),
        )
}

fun DTOServiceMitigation.toBIServiceDeviation(): BIServiceDeviation =
    BIServiceDeviation(
        BIServiceVarianceTraceInfo(
            header?.ownerId ?: "",
            header?.publisherId ?: "",
            header?.ownerId ?: "",
        ),
        ref ?: "",
        BIServiceDeviationSpec(),
    )
