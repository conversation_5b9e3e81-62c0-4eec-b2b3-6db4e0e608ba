package no.ruter.tranop.app.variance.mitigation.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.journey.mitigation.dto.model.DTOServiceMitigation
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviation
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviationSpec
import no.ruter.tranop.operation.deviation.bi.model.BIServiceVarianceTraceInfo
import no.ruter.tranop.operation.mitigation.bi.model.BIServiceMitigation
import org.springframework.stereotype.Service
import java.time.Instant.now

/**
 * Client for streaming ingestion of service mitigations into Snowflake.
 *
 * @property config Configuration for streaming ingest.
 * @property clientFactory Factory for creating Snowflake clients and channels.
 */
@Service
class ServiceMitigationSnowflakeIngestClient(
    config: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<BIServiceMitigation>(
        config,
        clientFactory,
        CLIENT_BUILDER_NAME,
        CHANNEL_BUILDER_NAME,
    ) {
    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_MITIGATIONS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_MITIGATION_CHANNEL"

        // Column names for Snowflake ingestion
        const val COL_REF = "REF"
        const val COL_OWNER_ID = "OWNER_ID"
        const val COL_AUTHORITY_ID = "AUTHORITY_ID"
        const val COL_OPERATOR_ID = "OPERATOR_ID"
        const val COL_QUAY_REFS = "QUAY_REFS"
        const val COL_DATED_JOURNEY_V2_REFS = "DATEDJOURNEYV2_REFS"
        const val COL_LINE_REFS = "LINE_REFS"
        const val COL_JSON_DATA = "JSONDATA"

        // Default values
        const val EMPTY_STRING = ""
    }

    override fun BIServiceMitigation.toIngestMap(): Map<String, Any> =
        mapOf(
            COL_REF to ref,
            COL_OWNER_ID to trace.ownerId,
            COL_AUTHORITY_ID to trace.authorityId,
            COL_OPERATOR_ID to trace.operatorId,
            COL_QUAY_REFS to spec.impact.stopPoints,
            COL_DATED_JOURNEY_V2_REFS to spec.impact.journeys,
            COL_LINE_REFS to spec.impact.lines,
            COL_JSON_DATA to JsonUtils.toJson(this),
        )
}

fun DTOServiceMitigation.toBIServiceDeviation(): BIServiceDeviation =
    BIServiceDeviation(
        BIServiceVarianceTraceInfo(
            header?.ownerId ?: ServiceMitigationSnowflakeIngestClient.EMPTY_STRING,
            header?.publisherId ?: ServiceMitigationSnowflakeIngestClient.EMPTY_STRING,
            header?.ownerId ?: ServiceMitigationSnowflakeIngestClient.EMPTY_STRING,
        ),
        ref ?: ServiceMitigationSnowflakeIngestClient.EMPTY_STRING,
        BIServiceDeviationSpec(),
    )
