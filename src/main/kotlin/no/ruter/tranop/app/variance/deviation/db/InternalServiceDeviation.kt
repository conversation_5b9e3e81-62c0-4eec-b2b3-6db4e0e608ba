package no.ruter.tranop.app.variance.deviation.db

import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DeviationRecord
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation

class InternalServiceDeviation(
    val data: DTOServiceDeviation,
    val record: DeviationRecord,
) {
    val ownerId: String?
        get() = data.header.ownerId

    val authorityId: String?
        get() = record.authorityId

    val operatorId: String?
        get() = record.operatorId

    val deleted: Boolean
        get() = record.deletedAt != null
}
