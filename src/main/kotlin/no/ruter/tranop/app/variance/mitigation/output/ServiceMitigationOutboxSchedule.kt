package no.ruter.tranop.app.variance.mitigation.output

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.operation.mitigation.bi.model.BIServiceMitigation
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = ServiceMitigationOutboxStreamingConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class ServiceMitigationOutboxSchedule(
    val timeService: TimeService,
    val outboxService: OutboxService,
    val streamingIngestClient: ServiceMitigationSnowflakeIngestClient,
    val config: ServiceMitigationOutboxStreamingConfig,
) {
    companion object {
        const val FREQ_PREFIX = "${ServiceMitigationOutboxStreamingConfig.CONF_PREFIX}.frequent"
    }

    private val log = LoggerFactory.getLogger(javaClass.canonicalName)

    @Scheduled(
        fixedRateString = "\${${FREQ_PREFIX}.fixedRate}",
        initialDelayString = "\${${FREQ_PREFIX}.initialDelay}",
    )
    @SchedulerLock(
        name = "\${${FREQ_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${FREQ_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${FREQ_PREFIX}.lockAtLeastFor}",
    )
    fun processPendingEvents() {
        val unpublished =
            outboxService.findUnpublished(
                DBOutboxDataType.SERVICE_MITIGATION,
                DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                AbstractQueryBuilder.Pagination(config.batchSize, 0),
                config.retryCount,
            )
        runCatching {
            val (successFullIngestEvents, failedIngestEvents) =
                streamingIngestClient.ingest(
                    unpublished.map {
                        Pair(
                            it.record.ref,
                            JsonUtils.toObject(it.data.payload, BIServiceMitigation::class.java),
                        )
                    },
                )
            log.debug(
                "Successfully ingested ${successFullIngestEvents.size} service mitigations, failed to ingest ${failedIngestEvents.size} service mitigations.",
            )

            if (successFullIngestEvents.isNotEmpty()) {
                outboxService.markAsPublished(successFullIngestEvents)
            }
            if (failedIngestEvents.isNotEmpty()) {
                outboxService.markAsFailed(
                    unpublished.filter { it ->
                        failedIngestEvents.contains(it.record.ref)
                    },
                )
            }
        }.onFailure { e ->
            log.error("Failed to process pending service mitigation events, marking as failed", e)
            outboxService.markAsFailed(unpublished)
        }
    }
}
