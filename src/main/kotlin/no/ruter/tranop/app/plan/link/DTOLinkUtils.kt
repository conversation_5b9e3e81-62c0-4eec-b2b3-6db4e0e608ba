package no.ruter.tranop.app.plan.link

import no.ruter.tranop.app.common.mapping.addIfNotEmpty
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink

class DTOLinkUtils private constructor() {
    companion object {
        fun <T : MutableCollection<String>> addQuayRefs(
            target: T,
            link: DTOStopPointLink?,
        ): T {
            if (link != null) {
                target.addIfNotEmpty(link.fromQuayRef)
                target.addIfNotEmpty(link.toQuayRef)
            }
            return target
        }

        fun <T : MutableCollection<String>> addStopPointRefs(
            target: T,
            link: DTOStopPointLink?,
        ): T {
            if (link != null) {
                target.addIfNotEmpty(link.fromStopPointRef)
                target.addIfNotEmpty(link.toStopPointRef)
            }
            return target
        }
    }
}
