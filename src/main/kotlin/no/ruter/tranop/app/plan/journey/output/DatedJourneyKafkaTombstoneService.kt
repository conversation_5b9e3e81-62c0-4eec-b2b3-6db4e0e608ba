package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityTombstoneService
import no.ruter.tranop.app.plan.journey.output.internal.AssignmentJourneyTombstoneService
import org.springframework.stereotype.Component

@Component
class DatedJourneyKafkaTombstoneService(
    val assignmentJourneyTombstoningService: AssignmentJourneyTombstoneService,
    val datedJourneyTombstoningService: DatedJourneyEntityTombstoneService,
) {
    fun tombstone(key: String): Result<Unit> =
        try {
            assignmentJourneyTombstoningService.tombstone(key).getOrThrow()
            datedJourneyTombstoningService.tombstone(key).getOrThrow()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
}
