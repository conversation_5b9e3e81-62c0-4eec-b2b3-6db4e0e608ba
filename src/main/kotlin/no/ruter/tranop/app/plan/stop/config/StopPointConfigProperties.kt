package no.ruter.tranop.app.plan.stop.config

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = StopPointConfigProperties.CONF_PREFIX)
class StopPointConfigProperties : AbstractSectionConfigProperties() {
    companion object {
        const val CONF_PREFIX = "app.config.stop-point"
    }
}
