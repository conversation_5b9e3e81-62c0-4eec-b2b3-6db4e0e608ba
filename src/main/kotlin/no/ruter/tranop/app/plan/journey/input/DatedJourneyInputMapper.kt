package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJDestination
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJEvent
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJEventMetadata
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJLifeCycleInfo
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJLine
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJLineage
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJMessageHeader
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJOperator
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJOperatorContract
import no.ruter.plandata.journey.dated.v2.dto.model.common.PDJVehicle
import no.ruter.plandata.journey.dated.v2.dto.model.common.link.PDJGeoPoint
import no.ruter.plandata.journey.dated.v2.dto.model.common.link.PDJStopPointLink
import no.ruter.plandata.journey.dated.v2.dto.model.common.link.PDJTrafficPriorityPoint
import no.ruter.plandata.journey.dated.v2.dto.model.common.stop.PDJStopPoint
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCall
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCallInterchange
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCallInterchangeAttributes
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCallInterchangeFrom
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCallInterchangeReferences
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyCallInterchangeTo
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyPlan
import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourneyReferences
import no.ruter.plandata.journey.dated.v2.dto.value.PDJEventMetadataKeyType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJEventType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJJourneyType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJLegacyTransportMode
import no.ruter.plandata.journey.dated.v2.dto.value.PDJLegacyTransportSubMode
import no.ruter.plandata.journey.dated.v2.dto.value.PDJLineageOriginatingFrom
import no.ruter.plandata.journey.dated.v2.dto.value.PDJLineageType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJStopPointBehaviourType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJStopPointLinkType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJTrackLineType
import no.ruter.plandata.journey.dated.v2.dto.value.PDJTrafficPriorityTriggerCode
import no.ruter.plandata.journey.dated.v2.dto.value.PDJTransportMode
import no.ruter.plandata.journey.dated.v2.dto.value.PDJTransportModeProperty
import no.ruter.tranop.app.plan.journey.JourneyUtils
import no.ruter.tranop.common.dto.model.DTOLifeCycleInfo
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCall
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchange
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeAttributes
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeFrom
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeReferences
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyCallInterchangeTo
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyPlan
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourneyReferences
import no.ruter.tranop.dated.journey.dto.model.common.DTODatedJourneyDirectionCode
import no.ruter.tranop.dated.journey.dto.model.common.DTODestination
import no.ruter.tranop.dated.journey.dto.model.common.DTOEvent
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadata
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventMetadataKeyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import no.ruter.tranop.dated.journey.dto.model.common.DTOJourneyType
import no.ruter.tranop.dated.journey.dto.model.common.DTOLegacyTransportMode
import no.ruter.tranop.dated.journey.dto.model.common.DTOLegacyTransportSubMode
import no.ruter.tranop.dated.journey.dto.model.common.DTOLine
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineage
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageOriginatingFrom
import no.ruter.tranop.dated.journey.dto.model.common.DTOLineageType
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperator
import no.ruter.tranop.dated.journey.dto.model.common.DTOOperatorContract
import no.ruter.tranop.dated.journey.dto.model.common.DTOStopPointBehaviourType
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportMode
import no.ruter.tranop.dated.journey.dto.model.common.DTOTransportModeProperty
import no.ruter.tranop.dated.journey.dto.model.common.DTOVehicle
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOGeoPoint
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLinkType
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOTrackLineType
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOTrafficPriorityPoint
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOTrafficPriorityTriggerCode
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint

class DatedJourneyInputMapper {
    companion object {
        val EVENT_TYPES =
            mapOf(
                PDJEventType.IMPORTED to DTOEventType.IMPORTED,
                PDJEventType.UPDATED to DTOEventType.UPDATED,
                PDJEventType.PATCHED to DTOEventType.PATCHED,
                PDJEventType.CANCELLED to DTOEventType.CANCELLED,
                PDJEventType.UNCANCELLED to DTOEventType.UNCANCELLED,
                PDJEventType.COMPLETED to DTOEventType.COMPLETED,
                PDJEventType.OMITTED to DTOEventType.OMITTED,
                PDJEventType.UN_OMITTED to DTOEventType.UN_OMITTED,
                PDJEventType.CALLS_CANCELLED to DTOEventType.CALLS_CANCELLED,
                PDJEventType.CALLS_UNCANCELLED to DTOEventType.CALLS_UNCANCELLED,
                PDJEventType.CALLS_OMITTED to DTOEventType.CALLS_OMITTED,
                PDJEventType.CALLS_UN_OMITTED to DTOEventType.CALLS_UN_OMITTED,
                PDJEventType.VEHICLE_PLANNED to DTOEventType.VEHICLE_PLANNED,
                PDJEventType.VEHICLE_ASSIGNED to DTOEventType.VEHICLE_ASSIGNED,
                PDJEventType.VEHICLE_UNASSIGNED to DTOEventType.VEHICLE_UNASSIGNED,
                PDJEventType.UNKNOWN to DTOEventType.UNKNOWN,
            )

        val LINK_TYPES =
            mapOf(
                PDJStopPointLinkType.MIX to DTOStopPointLinkType.MIX,
                PDJStopPointLinkType.ROAD to DTOStopPointLinkType.ROAD,
                PDJStopPointLinkType.TRACK to DTOStopPointLinkType.TRACK,
                PDJStopPointLinkType.WATER to DTOStopPointLinkType.WATER,
                PDJStopPointLinkType.UNKNOWN to DTOStopPointLinkType.UNKNOWN,
            )

        val JOURNEY_TYPES =
            mapOf(
                PDJJourneyType.UNKNOWN to DTOJourneyType.UNKNOWN,
                PDJJourneyType.DEAD_RUN to DTOJourneyType.DEAD_RUN,
                PDJJourneyType.SERVICE_JOURNEY to DTOJourneyType.SERVICE_JOURNEY,
            )

        val TRACK_LINE_TYPES =
            mapOf(
                PDJTrackLineType.NONE to DTOTrackLineType.NONE,
                PDJTrackLineType.SIMPLE to DTOTrackLineType.SIMPLE,
                PDJTrackLineType.DETAILED to DTOTrackLineType.DETAILED,
                PDJTrackLineType.UNKNOWN to DTOTrackLineType.UNKNOWN,
            )

        val TRIGGER_CODES =
            mapOf(
                PDJTrafficPriorityTriggerCode.ENTER to DTOTrafficPriorityTriggerCode.ENTER,
                PDJTrafficPriorityTriggerCode.EXIT to DTOTrafficPriorityTriggerCode.EXIT,
                PDJTrafficPriorityTriggerCode.DOOR_CLOSE to DTOTrafficPriorityTriggerCode.DOOR_CLOSE,
                PDJTrafficPriorityTriggerCode.UNKNOWN to DTOTrafficPriorityTriggerCode.UNKNOWN,
            )

        val TRANSPORT_MODES =
            mapOf(
                PDJTransportMode.BUS to DTOTransportMode.BUS,
                PDJTransportMode.BOAT to DTOTransportMode.BOAT,
                PDJTransportMode.METRO to DTOTransportMode.METRO,
                PDJTransportMode.TRAM to DTOTransportMode.TRAM,
                PDJTransportMode.TRAIN to DTOTransportMode.TRAIN,
                PDJTransportMode.UNKNOWN to DTOTransportMode.UNKNOWN,
            )

        val TRANSPORT_MODE_PROPERTIES =
            mapOf(
                PDJTransportModeProperty.AIRPORT to DTOTransportModeProperty.AIRPORT,
                PDJTransportModeProperty.SCHOOL to DTOTransportModeProperty.SCHOOL,
                PDJTransportModeProperty.INDUSTRIAL to DTOTransportModeProperty.INDUSTRIAL,
                PDJTransportModeProperty.LOCAL to DTOTransportModeProperty.LOCAL,
                PDJTransportModeProperty.REGIONAL to DTOTransportModeProperty.REGIONAL,
                PDJTransportModeProperty.REGULAR to DTOTransportModeProperty.REGULAR,
                PDJTransportModeProperty.EXPRESS to DTOTransportModeProperty.EXPRESS,
                PDJTransportModeProperty.NIGHT to DTOTransportModeProperty.NIGHT,
                PDJTransportModeProperty.FLEXI to DTOTransportModeProperty.FLEXI,
                PDJTransportModeProperty.BACK_UP to DTOTransportModeProperty.BACK_UP,
                PDJTransportModeProperty.SERVICE_LINE to DTOTransportModeProperty.SERVICE_LINE,
                PDJTransportModeProperty.BUS_FOR_BOAT to DTOTransportModeProperty.BUS_FOR_BOAT,
                PDJTransportModeProperty.BUS_FOR_METRO to DTOTransportModeProperty.BUS_FOR_METRO,
                PDJTransportModeProperty.BUS_FOR_TRAM to DTOTransportModeProperty.BUS_FOR_TRAM,
                PDJTransportModeProperty.BUS_FOR_TRAIN to DTOTransportModeProperty.BUS_FOR_TRAIN,
                PDJTransportModeProperty.WATER to DTOTransportModeProperty.WATER,
                PDJTransportModeProperty.FERRY to DTOTransportModeProperty.FERRY,
                PDJTransportModeProperty.PASSENGER_FERRY to DTOTransportModeProperty.PASSENGER_FERRY,
                PDJTransportModeProperty.UNKNOWN to DTOTransportModeProperty.UNKNOWN,
            )

        val LEGACY_TRANSPORT_MODE =
            mapOf(
                PDJLegacyTransportMode.BUS to DTOLegacyTransportMode.BUS,
                PDJLegacyTransportMode.TRAM to DTOLegacyTransportMode.TRAM,
                PDJLegacyTransportMode.METRO to DTOLegacyTransportMode.METRO,
                PDJLegacyTransportMode.FERRY to DTOLegacyTransportMode.FERRY,
                PDJLegacyTransportMode.BUS_FOR_TRAM to DTOLegacyTransportMode.BUS_FOR_TRAM,
                PDJLegacyTransportMode.BUS_FOR_METRO to DTOLegacyTransportMode.BUS_FOR_METRO,
                PDJLegacyTransportMode.WATER to DTOLegacyTransportMode.WATER,
                PDJLegacyTransportMode.UNKNOWN to DTOLegacyTransportMode.UNKNOWN,
            )

        val LEGACY_TRANSPORT_SUB_MODE =
            mapOf(
                PDJLegacyTransportSubMode.NIGHT to DTOLegacyTransportSubMode.NIGHT,
                PDJLegacyTransportSubMode.NIGHT_BUS to DTOLegacyTransportSubMode.NIGHT_BUS,
                PDJLegacyTransportSubMode.LOCAL to DTOLegacyTransportSubMode.LOCAL,
                PDJLegacyTransportSubMode.LOCAL_BUS to DTOLegacyTransportSubMode.LOCAL_BUS,
                PDJLegacyTransportSubMode.LOCAL_TRAM to DTOLegacyTransportSubMode.LOCAL_TRAM,
                PDJLegacyTransportSubMode.LOCAL_PASSENGER_FERRY to DTOLegacyTransportSubMode.LOCAL_PASSENGER_FERRY,
                PDJLegacyTransportSubMode.REGION to DTOLegacyTransportSubMode.REGION,
                PDJLegacyTransportSubMode.EXPRESS to DTOLegacyTransportSubMode.EXPRESS,
                PDJLegacyTransportSubMode.EXPRESS_BUS to DTOLegacyTransportSubMode.EXPRESS_BUS,
                PDJLegacyTransportSubMode.SCHOOL_BUS to DTOLegacyTransportSubMode.SCHOOL_BUS,
                PDJLegacyTransportSubMode.SERVICE_LINE to DTOLegacyTransportSubMode.SERVICE_LINE,
                PDJLegacyTransportSubMode.METRO to DTOLegacyTransportSubMode.METRO,
                PDJLegacyTransportSubMode.UNKNOWN to DTOLegacyTransportSubMode.UNKNOWN,
            )

        val LINEAGE_TYPES =
            mapOf(
                PDJLineageType.UNKNOWN to DTOLineageType.UNKNOWN,
                PDJLineageType.JOURNEY_REF to DTOLineageType.JOURNEY_REF,
                PDJLineageType.JOURNEY_PATTERN_REF to DTOLineageType.JOURNEY_PATTERN_REF,
                PDJLineageType.RUNTIME_PATTERN_REF to DTOLineageType.RUNTIME_PATTERN_REF,
            )

        val LINEAGE_ORIGINS =
            mapOf(
                PDJLineageOriginatingFrom.HASTUS to DTOLineageOriginatingFrom.HASTUS,
                PDJLineageOriginatingFrom.BIFROST to DTOLineageOriginatingFrom.BIFROST,
                PDJLineageOriginatingFrom.UNKNOWN to DTOLineageOriginatingFrom.UNKNOWN,
            )

        val STOP_POINT_BEHAVIOUR_TYPES =
            mapOf(
                PDJStopPointBehaviourType.UNKNOWN to DTOStopPointBehaviourType.UNKNOWN,
                PDJStopPointBehaviourType.NO_SERVICE to DTOStopPointBehaviourType.NO_SERVICE,
                PDJStopPointBehaviourType.FULL_SERVICE to DTOStopPointBehaviourType.FULL_SERVICE,
                PDJStopPointBehaviourType.FOR_BOARDING_ONLY to DTOStopPointBehaviourType.FOR_BOARDING_ONLY,
                PDJStopPointBehaviourType.FOR_ALIGHTING_ONLY to DTOStopPointBehaviourType.FOR_ALIGHTING_ONLY,
            )

        val EVENT_METADATA_KEYS =
            mapOf(
                PDJEventMetadataKeyType.UNKNOWN to DTOEventMetadataKeyType.UNKNOWN,
                PDJEventMetadataKeyType.VEHICLE_REF to DTOEventMetadataKeyType.VEHICLE_REF,
                PDJEventMetadataKeyType.ASSIGNMENT_REF to DTOEventMetadataKeyType.ASSIGNMENT_REF,
                PDJEventMetadataKeyType.ASSIGNMENT_CODE to DTOEventMetadataKeyType.ASSIGNMENT_CODE,
                PDJEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME to DTOEventMetadataKeyType.FIRST_DEPARTURE_DATE_TIME,
                PDJEventMetadataKeyType.LAST_ARRIVAL_DATE_TIME to DTOEventMetadataKeyType.LAST_ARRIVAL_DATE_TIME,
            )
    }

    fun mapDatedJourney(input: PDJDatedJourney): DTODatedJourney =
        DTODatedJourney().apply {
            this.ref = input.ref
            this.order = input.order
            this.header = input.header?.let(::mapHeader)
            this.events = mapList(input.events, ::mapEvent)
            this.lifeCycleInfo = input.lifeCycleInfo?.let(::mapLifeCycleInfo)

            this.public = input.public
            this.deleted = input.deleted
            this.omitted = input.omitted ?: false
            this.cancelled = input.cancelled ?: false

            this.name = input.name
            this.type = mapValueType(input.type, JOURNEY_TYPES) { DTOJourneyType.of(it.value) }
            this.line = input.line?.let(::mapLine)
            this.vehicleTask = input.vehicleTask

            this.journeyReferences = input.journeyReferences?.let(::mapJourneyReferences)
            this.operators = mapList(input.operators, ::mapOperator)
            this.operatorContracts = mapList(input.operatorContracts, ::mapOperatorContract)
            this.lineage = mapList(input.lineage, ::mapLineage)
            this.vehicles = mapList(input.vehicles, ::mapVehicle)
            this.operatingDate = input.operatingDate
            this.direction = mapJourneyDirection(input.direction)

            this.plan = input.plan?.let(::mapJourneyPlan)
            this.journeyState = JourneyUtils.createJourneyState(omitted = this.omitted, cancelled = this.cancelled)
        }

    private fun mapJourneyPlan(input: PDJDatedJourneyPlan): DTODatedJourneyPlan =
        DTODatedJourneyPlan().apply {
            this.calls = mapList(input.calls, ::mapCall)
            this.stops = mapList(input.stops, ::mapStopPoint)
            this.links = mapList(input.links, ::mapStopPointLink)
            this.linkRefs = input.linkRefs
            this.lastArrivalDateTime = input.lastArrivalDateTime
            this.firstDepartureDateTime = input.firstDepartureDateTime
        }

    private fun mapCall(input: PDJDatedJourneyCall): DTODatedJourneyCall =
        DTODatedJourneyCall().apply {
            this.ref = input.ref
            this.order = input.order
            this.events = mapList(input.events, ::mapEvent)

            this.omitted = input.omitted ?: false
            this.cancelled = input.cancelled ?: false

            this.destination = input.destination?.let(::mappCallDestination)
            this.stopPointRef = input.stopPointRef
            this.journeyPatternPointRef = input.journeyPatternPointRef
            this.stopPointBehaviourType = mapStopPointBehaviourType(input.stopPointBehaviourType)
            this.originalStopPointBehaviourType = mapStopPointBehaviourType(input.originalStopPointBehaviourType)

            this.plannedArrival = input.plannedArrival
            this.plannedDeparture = input.plannedDeparture
            this.expectedArrival = null // TODO: input.plannedArrival?
            this.expectedDeparture = null // TODO: input.plannedDeparture?

            this.previousCallStopPointLinkRef = input.previousCallStopPointLinkRef
            this.nextCallStopPointLinkRef = input.nextCallStopPointLinkRef
            this.interchange = input.interchange?.let(::mapCallInterchange)
        }

    private fun mapCallInterchange(input: PDJDatedJourneyCallInterchange): DTODatedJourneyCallInterchange =
        DTODatedJourneyCallInterchange().apply {
            this.to = mapList(input.to, ::mapCallInterchangeOrigin)
            this.from = mapList(input.from, ::mapCallInterchangeDestination)
        }

    private fun mapCallInterchangeOrigin(input: PDJDatedJourneyCallInterchangeTo): DTODatedJourneyCallInterchangeTo =
        DTODatedJourneyCallInterchangeTo().apply {
            this.id = input.id
            this.attributes = input.attributes?.let(::mapCallInterchangeAttributes)
            this.references = input.references?.let(::mapCallInterchangeReferences)

            this.guaranteed = input.guaranteed
            this.staySeated = input.staySeated
            this.departureDateTime = input.departureDateTime
        }

    private fun mapCallInterchangeReferences(input: PDJDatedJourneyCallInterchangeReferences): DTODatedJourneyCallInterchangeReferences =
        DTODatedJourneyCallInterchangeReferences().apply {
            this.externalJourneyId = input.externalJourneyId
            this.externalInterchangeRef = input.externalInterchangeRef
            this.entityDatedJourneyV1Ref = input.entityDatedJourneyV1Ref
            this.entityDatedJourneyV2Ref = input.entityDatedJourneyV2Ref
            this.lineRef = input.lineRef
            this.quayRef = input.quayRef
            this.legacyQuayRef = input.legacyQuayRef
            this.entityDatedJourneyV2CallRef = input.entityDatedJourneyV2CallRef
        }

    private fun mapCallInterchangeAttributes(input: PDJDatedJourneyCallInterchangeAttributes): DTODatedJourneyCallInterchangeAttributes =
        DTODatedJourneyCallInterchangeAttributes().apply {
            this.direction = input.direction // TODO: Should we use DTODatedJourneyDirectionCode here?
            this.operatingDate = input.operatingDate
            this.vehicleJourneyId = input.vehicleJourneyId
        }

    private fun mapCallInterchangeDestination(input: PDJDatedJourneyCallInterchangeFrom): DTODatedJourneyCallInterchangeFrom =
        DTODatedJourneyCallInterchangeFrom().apply {
            this.id = input.id
            this.attributes = input.attributes?.let(::mapCallInterchangeAttributes)
            this.references = input.references?.let(::mapCallInterchangeReferences)

            this.guaranteed = input.guaranteed
            this.arrivalDateTime = input.arrivalDateTime
            this.maximumWaitTimeSec = input.maximumWaitTimeSec
        }

    private fun mapVehicle(input: PDJVehicle): DTOVehicle =
        DTOVehicle().apply {
            this.vehicleRef = input.vehicleRef
        }

    private fun mapLineage(input: PDJLineage): DTOLineage =
        DTOLineage().apply {
            this.type = mapValueType(input.type, LINEAGE_TYPES) { DTOLineageType.of(it.value) }
            this.value = input.value
            this.source = mapValueType(input.source, LINEAGE_ORIGINS) { DTOLineageOriginatingFrom.of(it.value) }
        }

    private fun mapOperator(input: PDJOperator): DTOOperator =
        DTOOperator().apply {
            this.name = input.name
            this.operatorRef = input.operatorRef
        }

    private fun mapOperatorContract(input: PDJOperatorContract): DTOOperatorContract =
        DTOOperatorContract().apply {
            this.name = input.name
            this.operator = input.operator?.let(::mapOperator)
            this.operatorContractRef = input.operatorContractRef
        }

    private fun mappCallDestination(input: PDJDestination): DTODestination =
        DTODestination().apply {
            this.text = input.text
            this.subText = input.subText
        }

    private fun mapJourneyDirection(input: String?): DTODatedJourneyDirectionCode =
        input?.let(DTODatedJourneyDirectionCode::of) ?: DTODatedJourneyDirectionCode.NONE // 0 = NONE, 1 = INBOUND, 2 = OUTBOUND

    private fun mapJourneyReferences(input: PDJDatedJourneyReferences): DTODatedJourneyReferences =
        DTODatedJourneyReferences().apply {
            this.vehicleTaskRef = input.vehicleTaskRef
            this.blockRef = input.blockRef
            this.datedBlockRef = input.datedBlockRef
            this.journeyRef = input.journeyRef
            this.vehicleJourneyId = input.vehicleJourneyId
            this.legacyJourneyRef = input.legacyJourneyRef
            this.legacyDatedJourneyRef = input.legacyDatedJourneyRef
            this.journeyPatternRef = input.journeyPatternRef
            this.runtimePatternRef = input.runtimePatternRef
            this.legacyJourneyPatternRefs = input.legacyJourneyPatternRefs
            this.externalJourneyRef = input.externalJourneyRef
            this.externalJourneyRefV2 = input.externalJourneyRefV2
            this.datedServiceJourneyId = input.datedServiceJourneyId
        }

    private fun mapLine(input: PDJLine): DTOLine =
        DTOLine().apply {
            this.lineRef = input.lineRef
            this.transportMode =
                mapValueType(input.transportMode, TRANSPORT_MODES) {
                    DTOTransportMode.of(it.value)
                }
            this.transportModeProperties =
                mapList(input.transportModeProperties) { prop ->
                    mapValueType(prop, TRANSPORT_MODE_PROPERTIES) {
                        DTOTransportModeProperty.of(it.value)
                    }
                }
            this.legacyTransportMode =
                mapValueType(input.legacyTransportMode, LEGACY_TRANSPORT_MODE) {
                    DTOLegacyTransportMode.of(it.value())
                }
            this.legacyTransportSubMode =
                mapValueType(input.legacyTransportSubMode, LEGACY_TRANSPORT_SUB_MODE) {
                    DTOLegacyTransportSubMode.of(it.value())
                }
            this.name = input.name
            this.publicCode = input.publicCode
            this.privateCode = input.privateCode
            this.backgroundColour = input.backgroundColour
            this.textColour = input.textColour
        }

    fun mapStopPoint(input: PDJStopPoint): DTOStopPoint =
        DTOStopPoint().apply {
            this.ref = input.ref
            this.header = input.header?.let(::mapHeader)
            this.events = mapList(input.events, ::mapEvent)
            this.lifeCycleInfo = input.lifeCycleInfo?.let(::mapLifeCycleInfo)
            this.legacyQuayRef = input.legacyQuayRef
            this.quayRef = input.quayRef
            this.legacyStopPlaceRef = input.legacyStopPlaceRef
            this.stopPlaceRef = input.stopPlaceRef
            this.legacyStopAreaRef = input.legacyStopAreaRef
            this.stopAreaRef = input.stopAreaRef
            this.geoPoint = input.geoPoint?.let(::mapGeoPoint)
            this.name = input.name
            this.publicCode = input.publicCode
            this.description = input.description
            this.stopPlaceDescription = input.stopPlaceDescription
            this.tariffZones = input.tariffZones
        }

    fun mapStopPointLink(input: PDJStopPointLink): DTOStopPointLink =
        DTOStopPointLink().apply {
            this.ref = input.ref
            this.header = input.header?.let(::mapHeader)
            this.events = mapList(input.events, ::mapEvent)
            this.lifeCycleInfo = input.lifeCycleInfo?.let(::mapLifeCycleInfo)
            this.serviceLinkRef = input.serviceLinkRef
            this.fromStopPointRef = input.fromStopPointRef
            this.fromQuayRef = input.fromQuayRef
            this.toStopPointRef = input.toStopPointRef
            this.toQuayRef = input.toQuayRef
            this.trackLine = input.trackLine
            this.trackLineType = mapValueType(input.trackLineType, TRACK_LINE_TYPES) { DTOTrackLineType.of(it.value) }
            this.calculatedLength = input.calculatedLength
            this.measuredLength = input.measuredLength
            this.stopPointLinkType = mapValueType(input.stopPointLinkType, LINK_TYPES) { DTOStopPointLinkType.of(it.value) }
            this.trafficPriorityPoints = mapList(input.trafficPriorityPoints, ::mapTrafficPriorityPoint)
        }

    private fun mapStopPointBehaviourType(input: PDJStopPointBehaviourType?): DTOStopPointBehaviourType? =
        mapValueType(input, STOP_POINT_BEHAVIOUR_TYPES) { DTOStopPointBehaviourType.of(it.value) }

    private fun mapTrafficPriorityPoint(input: PDJTrafficPriorityPoint): DTOTrafficPriorityPoint =
        DTOTrafficPriorityPoint().apply {
            this.code = input.code
            this.name = input.name
            this.triggerCode = mapValueType(input.triggerCode, TRIGGER_CODES) { DTOTrafficPriorityTriggerCode.of(it.value) }
            this.geoPoint = input.geoPoint?.let(::mapGeoPoint)
            this.areaRef = input.areaRef
            this.pointRef = input.pointRef
        }

    private fun mapGeoPoint(input: PDJGeoPoint): DTOGeoPoint =
        DTOGeoPoint().apply {
            this.ref = input.ref
            this.detectionPointRef = input.detectionPointRef
            this.location = input.location
            this.circleRadius = input.circleRadius
            this.polygon = input.polygon
            this.altitude = input.altitude
            this.entryDirection = input.entryDirection
            this.entryDirectionWindow = input.entryDirectionWindow
            this.detectionType = input.detectionType
        }

    private fun mapEvent(input: PDJEvent): DTOEvent =
        DTOEvent().apply {
            this.id = input.id
            this.type = mapValueType(input.type, EVENT_TYPES) { DTOEventType.of(it.value) }
            this.source = input.source
            this.traceId = input.traceId
            this.timestamp = input.timestamp
            this.description = input.description
            this.metadata = mapList(input.metadata, ::mapMetadata)
        }

    private fun mapMetadata(input: PDJEventMetadata): DTOEventMetadata =
        DTOEventMetadata().apply {
            this.key = mapValueType(input.key, EVENT_METADATA_KEYS) { DTOEventMetadataKeyType.of(it.value) }
            this.value = input.value
        }

    private fun mapHeader(input: PDJMessageHeader): DTOMessageHeader =
        DTOMessageHeader().apply {
            this.traceId = input.traceId
            this.ownerId = input.ownerId
            this.originId = input.originId
            this.publisherId = input.publisherId
            this.messageTimestamp = input.messageTimestamp
            this.receivedTimestamp = input.receivedTimestamp
            this.publishedTimestamp = input.publishedTimestamp
            this.expiresTimestamp = input.expiresTimestamp
        }

    private fun mapLifeCycleInfo(input: PDJLifeCycleInfo): DTOLifeCycleInfo =
        DTOLifeCycleInfo().apply {
            this.created = input.created
            this.modified = input.modified
            this.revision = input.revision
            this.dataSource = input.dataSource
        }

    private fun <I, O> mapList(
        input: Collection<I>?,
        mapper: (I) -> O?,
    ): List<O>? = input?.mapNotNull { if (it == null) null else mapper(it) }

    private fun <I, O> mapValueType(
        input: I?,
        values: Map<I, O>,
        fallback: (I) -> O,
    ): O? = input?.let { values[input] ?: fallback(it) }
}
