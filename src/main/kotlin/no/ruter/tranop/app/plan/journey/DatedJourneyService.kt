package no.ruter.tranop.app.plan.journey

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchProperties
import no.ruter.tranop.app.common.dataflow.opensearch.OpenSearchService
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.ErrorType
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.db.JourneyEventRepository
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputContext
import no.ruter.tranop.app.plan.journey.input.db.DatedJourneyInputRepository
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaTombstoneService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Isolation
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.LocalDate
import java.time.OffsetDateTime

@Service
class DatedJourneyService(
    val repository: DatedJourneyRepository,
    val inputRepository: DatedJourneyInputRepository,
    val journeyEventRepository: JourneyEventRepository,
    val insightService: InsightService,
    private val timeService: TimeService,
    val journeyTombstoneService: DatedJourneyKafkaTombstoneService,
    val openSearchService: OpenSearchService,
    val openSearchProperties: OpenSearchProperties,
) {
    val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    val defaultChannel = repository.recordType.channels.internal

    companion object {
        private const val BATCH_SIZE_FOR_JOURNEY_REF_FETCH = 256
    }

    fun fetchRefsForTombstoning(
        fromDateTime: OffsetDateTime?,
        toDateTime: OffsetDateTime,
        limit: Int = 65536,
    ) = repository.fetchRefsForTombstoning(fromDateTime = fromDateTime, toDateTime = toDateTime, limit = limit)

    fun fetchDatedJourneyByRef(ref: String?): InternalDatedJourney? =
        try {
            if (ref.isNullOrBlank()) {
                null
            } else {
                repository.fetchDatedJourneys(listOfNotNull(ref)).firstOrNull()
            }
        } catch (e: Exception) {
            insightService.insightError(
                channel = defaultChannel,
                classifier = ErrorType.DB_READ.value,
                msg = e.toString(),
                e = e,
            )
            null
        }

    fun store(journey: DatedJourneyInputContext): DatedJourneyInputContext = repository.store(journey)

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    fun deleteJourneysTransactional(context: DatedJourneyInputContext): DatedJourneyInputContext {
        context.ref?.let { ref ->
            val tombstoningResult = journeyTombstoneService.tombstone(ref)
            tombstoningResult
                .onSuccess {
                    markTombstoned(ref)
                }.onFailure {
                    throw it
                }
        }
        val journeyRefs = listOfNotNull(context.ref)

        val eventsDeleted = journeyEventRepository.deleteByDatedJourneyV2Refs(journeyRefs)
        val journeysDeleted = repository.deleteByRefs(journeyRefs)
        val inputJourneysDeleted = inputRepository.deleteByRefs(journeyRefs, deleteExternalRefs = true)

        if (context.journeyDeleted) {
            context.addSimpleDetail("deleted", journeysDeleted)
            context.addSimpleDetail("deleted.input", inputJourneysDeleted)
        } else if (context.journeyTombstone) {
            context.addSimpleDetail("tombstoned", journeysDeleted)
        }

        context.addSimpleDetail("events.deleted", eventsDeleted)
        context.deleted = journeysDeleted
        context.stored = journeysDeleted
        return context
    }

    fun markKafkaPublished(
        key: String,
        currentRevision: Int,
    ): Int? = repository.markKafkaPublished(key, currentRevision, timeService.now())

    fun markOpenSearchPublished(
        key: String,
        currentRevision: Int,
    ): Int? = repository.markOpenSearchPublished(key, currentRevision, timeService.now())

    fun markTombstoned(key: String): Int? = repository.markTombstoned(key, timeService.now())

    fun findKafkaUnpublished(
        pagination: AbstractQueryBuilder.Pagination,
        seenFailedRefs: MutableSet<String>,
        olderThanDateTime: OffsetDateTime,
    ) = repository.findKafkaUnpublished(pagination, seenFailedRefs, olderThanDateTime)

    fun findOpenSearchUnpublished(
        pagination: AbstractQueryBuilder.Pagination,
        seenFailedRefs: MutableSet<String>,
        olderThanDateTime: OffsetDateTime,
        fromOperatingDate: LocalDate,
        toOperatingDate: LocalDate,
    ) = repository.findOpenSearchUnpublished(
        DatedJourneyRepository.OWNER_OPENSEARCH,
        pagination,
        seenFailedRefs,
        olderThanDateTime,
        fromOperatingDate,
        toOperatingDate,
    )

    fun unPublishOperatingDate(toLocalDate: LocalDate) = repository.unPublishOperatingDate(toLocalDate)

    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    fun deleteTombstoneJourneysTransactional(olderThan: Duration): Int {
        val olderThanDateTime = timeService.now().minus(olderThan)
        val deletedRefs = repository.deleteTombstoned(olderThanDateTime)
        inputRepository.deleteByRefs(deletedRefs, deleteExternalRefs = true)
        return deletedRefs.size
    }

    fun deleteExpiredFromOpenSearch(): Int {
        val refs = getRefsPublishedToOpenSearchButDeletedFromJourneyTable()
        val index = openSearchProperties.journeyIndexName
        var deleted = 0

        refs.chunked(BATCH_SIZE_FOR_JOURNEY_REF_FETCH).forEach {
            val response = openSearchService.bulkDeleteDocumentFromIndex(index, it)
            response.items().forEach { probablyDeletedItem ->
                if (probablyDeletedItem.error() == null) {
                    val result = probablyDeletedItem.id()?.let { it1 -> deleteFromPublished(it1, DatedJourneyRepository.OWNER_OPENSEARCH) }
                    if (result != null) {
                        deleted += result
                    }
                } else {
                    log.warn(
                        "couldn't delete document ${probablyDeletedItem.id()} from $index due to ${
                            probablyDeletedItem.error()?.toJsonString()
                        }",
                    )
                }
            }
        }
        return deleted
    }

    fun getRefsPublishedToOpenSearchButDeletedFromJourneyTable(): List<String> =
        repository.getRefsPublishedToOpenSearchButDeletedFromJourneyTable()

    fun deleteFromPublished(
        ref: String,
        owner: String,
    ) = repository.deletePublishingInformation(ref, owner)
}
