package no.ruter.tranop.app.plan.journey.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputContext
import no.ruter.tranop.assignmentmanager.db.sql.Tables
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.exception.DataChangedException
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.OffsetDateTime

@Component
class DatedJourneyRepository(
    dslContext: DSLContext,
    timeService: TimeService,
    insightService: InsightService,
) : BaseRecordRepository<
        DatedJourneyRecord,
        DatedJourneyTable,
        InternalDatedJourney,
        DatedJourneyRecordMapper,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = DatedJourneyRecordMapper(insightService),
    ) {
    companion object {
        const val METRIC_INSERTED = "inserted"
        const val METRIC_UPDATED = "updated"
        const val METRIC_CANCELLED = "cancelled"
        const val METRIC_PARTIALLY_CANCELLED = "partially_cancelled"

        val TYPE = RecordType.DATED_JOURNEY
        val TABLE = TYPE.table

        const val OWNER_OPENSEARCH = "OPENSEARCH"

        val ORDER_FIELDS =
            listOf(
                TABLE.FIRST_DEPARTURE.asc(),
                TABLE.LAST_ARRIVAL.asc(),
                TABLE.OPERATING_DATE.asc(),
                TABLE.ID.desc(),
            )

        private val published =
            TABLE.PUBLISHED_REVISION.isNotNull
                .and(TABLE.REVISION.isNotNull)
                .and(TABLE.REVISION.eq(TABLE.PUBLISHED_REVISION))

        private val notPublished = published.not()
    }

    fun store(
        context: DatedJourneyInputContext,
        now: OffsetDateTime = timeService.now(),
    ): DatedJourneyInputContext {
        if (context.invalid()) {
            return context
        }

        val ref = context.ref ?: return context

        val datedJourney = fetchByRef(ref)
        val record: DatedJourneyRecord =
            if (datedJourney == null) {
                context.update = false
                dslContext.newRecord(table)
            } else {
                context.update = true
                datedJourney.record
            }

        val externalRefs = ExternalRefs()
        recordMapper.update(record, context, now = now, externalRefs = externalRefs)

        // TODO: Make more robust?
        // TODO: Group in transaction?
        store(context, record)
        externalRefRepo.update(
            createdAt = record.createdAt,
            createdBy = record.createdBy,
            externalRefs = mapOf(ref to externalRefs),
        )
        return context
    }

    private fun store(
        context: DatedJourneyInputContext,
        record: DatedJourneyRecord,
    ): DatedJourneyInputContext {
        val res = record.store()

        if (context.update) {
            context.updated = res
            context.addSimpleDetail(METRIC_UPDATED, res)
        } else {
            context.inserted = res
            context.addSimpleDetail(METRIC_INSERTED, res)
        }
        if (context.cancelled) {
            context.addSimpleDetail(METRIC_CANCELLED, res)
        }
        if (context.partiallyCancelled) {
            context.addSimpleDetail(METRIC_PARTIALLY_CANCELLED, res)
        }

        context.stored = res
        return context
    }

    fun fetchRefsForTombstoning(
        fromDateTime: OffsetDateTime?,
        toDateTime: OffsetDateTime,
        limit: Int? = null,
    ): List<String> {
        val c1 = table.LAST_ARRIVAL.lessOrEqual(toDateTime).and(published)
        val c2 = table.FIRST_DEPARTURE.greaterOrEqual(fromDateTime)
        val cond = fromDateTime?.let { c1.and(c2) } ?: c1

        val query = dslContext.selectFrom(table).where(cond.and(table.TOMBSTONED_AT.isNull)).orderBy(ORDER_FIELDS)
        val limited = limit?.let { query.limit(it) } ?: query
        return limited.fetch(table.REF)
    }

    fun fetchDatedJourneys(journeyIds: Collection<String>): List<InternalDatedJourney> {
        if (journeyIds.isEmpty()) {
            return emptyList()
        }

        val cond = datedJourneyConditions(journeyIds)
        return fetch(cond)
    }

    private fun datedJourneyConditions(journeyIds: Collection<String>): Condition {
        val journeyRefsStripped = journeyIds.map { it.substringAfterLast(":") }

        val datedJourneyV2Ref = table.REF.`in`(journeyIds)
        val datedServiceJourneyId = table.DATED_SERVICE_JOURNEY_ID.`in`(journeyIds)
        val datedServiceJourneyIdStripped = table.DATED_SERVICE_JOURNEY_ID.`in`(journeyRefsStripped)
        val datedJourneyV1 = table.DATED_JOURNEY_V1_REF.`in`(journeyRefsStripped)
        val datedCond = datedJourneyV2Ref.or(datedServiceJourneyId).or(datedServiceJourneyIdStripped).or(datedJourneyV1)
        return datedCond
    }

    fun unPublishOperatingDate(
        operatingDate: LocalDate,
        dsl: DSLContext = dslContext,
    ): Int =
        dslContext
            .update(table)
            .set(table.PUBLISHED_REVISION, 0)
            .where(table.OPERATING_DATE.eq(operatingDate))
            .execute()

    fun findKafkaUnpublished(
        pagination: AbstractQueryBuilder.Pagination,
        excludeRefs: Set<String> = emptySet(),
        olderThanDateTime: OffsetDateTime,
    ): List<InternalDatedJourney> =
        fetch(notPublished.and(table.REF.notIn(excludeRefs)).and(table.MODIFIED_AT.lessThan(olderThanDateTime)), pagination)

    fun findOpenSearchUnpublished(
        owner: String,
        pagination: AbstractQueryBuilder.Pagination,
        excludeRefs: Set<String> = emptySet(),
        olderThanDateTime: OffsetDateTime,
        fromOperatingDate: LocalDate,
        toOperatingDate: LocalDate,
    ): List<InternalDatedJourney> {
        val t2 = Tables.DATED_JOURNEY_PUBLISH_STATUS
        val cond =
            table.REF
                .notIn(excludeRefs)
                .and(
                    t2.REF.isNull.or(table.REVISION.gt(t2.REVISION).and(t2.OWNER.eq(owner))),
                ).and(table.MODIFIED_AT.lessOrEqual(olderThanDateTime))
                .and(table.OPERATING_DATE.between(fromOperatingDate, toOperatingDate))

        val query =
            dslContext
                .select(table.asterisk())
                .from(table.leftJoin(t2).on(table.REF.eq(t2.REF)))
                .where(cond)
                .limit(pagination.limit)
        return fetch(query)
    }

    fun markKafkaPublished(
        key: String,
        revision: Int,
        now: OffsetDateTime,
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .update(table)
                .set(table.PUBLISHED_AT, now)
                .set(table.PUBLISHED_REVISION, revision)
                .where(table.REF.eq(key))
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update published_at timestamp on dated-journey [id=$key]"
            log.warn(message, exception)
            null
        }

    fun markOpenSearchPublished(
        key: String,
        revision: Int,
        now: OffsetDateTime,
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .insertInto(Tables.DATED_JOURNEY_PUBLISH_STATUS)
                .columns(
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.OWNER,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.REF,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.REVISION,
                    Tables.DATED_JOURNEY_PUBLISH_STATUS.PUBLISHED_AT,
                ).values(OWNER_OPENSEARCH, key, revision, now)
                .onDuplicateKeyUpdate()
                .set(Tables.DATED_JOURNEY_PUBLISH_STATUS.PUBLISHED_AT, now)
                .set(Tables.DATED_JOURNEY_PUBLISH_STATUS.REVISION, revision)
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update publish to open search status timestamp on dated-journey [id=$key]"
            log.warn(message, exception)
            null
        }

    fun markTombstoned(
        key: String,
        now: OffsetDateTime,
        dsl: DSLContext = dslContext,
    ): Int? =
        try {
            dsl
                .update(table)
                .set(table.TOMBSTONED_AT, now)
                .where(table.REF.eq(key))
                .execute()
        } catch (exception: DataChangedException) {
            val message =
                "Could not update tombstoned_at timestamp on dated-journey [id=$key]"
            log.warn(message, exception)
            null
        }

    fun deleteTombstoned(
        olderThanDateTime: OffsetDateTime,
        dsl: DSLContext = dslContext,
    ): Set<String> =
        try {
            val yesterday = timeService.now().minusDays(1).toLocalDate()
            val condition =
                table.TOMBSTONED_AT.isNotNull
                    .and(table.MODIFIED_AT.lessOrEqual(olderThanDateTime))
                    .and(table.OPERATING_DATE.lessThan(yesterday))

            val tablaRef = table.REF
            val deleteRefs =
                dsl
                    .select(tablaRef)
                    .from(table)
                    .where(condition)
                    .fetch { it.get(tablaRef) }
                    .toSet()

            val deleteCount = dsl.deleteFrom(table).where(tablaRef.`in`(deleteRefs)).execute()
            if (deleteCount != deleteRefs.size) {
                log.error("delete ref count mismatch: expected=${deleteRefs.size} actual=$deleteCount")
            }
            deleteRefs
        } catch (exception: DataChangedException) {
            val message =
                "Could not delete tombstoned dated journeys due to an exception"
            log.warn(message, exception)
            emptySet()
        }

    fun countPerOperatingDate(): Map<LocalDate, Int> =
        dslContext
            .select(TABLE.OPERATING_DATE, DSL.count())
            .from(TABLE)
            .groupBy(TABLE.OPERATING_DATE)
            .fetchMap(TABLE.OPERATING_DATE, DSL.count())

    fun getRefsPublishedToOpenSearchButDeletedFromJourneyTable(): List<String> {
        val djPublishTable = Tables.DATED_JOURNEY_PUBLISH_STATUS
        return dslContext
            .select(
                djPublishTable.REF,
            ).from(djPublishTable)
            .whereNotExists(dslContext.select(table.REF).from(table).where(table.REF.eq(djPublishTable.REF)))
            .and(djPublishTable.OWNER.eq(OWNER_OPENSEARCH))
            .fetch()
            .mapNotNull { it.value1() }
    }

    fun deletePublishingInformation(
        ref: String,
        owner: String,
    ): Int {
        val djPublishTable = Tables.DATED_JOURNEY_PUBLISH_STATUS
        return dslContext.deleteFrom(djPublishTable).where(djPublishTable.REF.eq(ref).and(djPublishTable.OWNER.eq(owner))).execute()
    }
}
