package no.ruter.tranop.app.plan.stop.input

import no.ruter.plandata.journey.dated.v2.dto.model.common.stop.PDJStopPoint
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.ProcessingContext.NOOPProcessingContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.common.AbstractInputService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputMapper
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import org.springframework.stereotype.Component

@Component
class StopPointInputService(
    val repository: StopPointRepository,
    insightService: InsightService,
) : AbstractInputService(
        defaultChannel = repository.recordType.channels.input,
        insightService = insightService,
    ) {
    private val inputMapper = DatedJourneyInputMapper()

    private fun store(ctx: StopPointInputContext): Boolean = processInput(ctx, repository::store)

    fun process(
        key: String?,
        stop: PDJStopPoint?,
    ): ProcessingContext {
        stop?.let {
            val ctx =
                StopPointInputContext(
                    channel = defaultChannel,
                    stopPoint = inputMapper.mapStopPoint(it),
                    ref = it.ref ?: key,
                )
            store(ctx)
            return ctx
        }
        return NOOPProcessingContext(channel = defaultChannel)
    }
}
