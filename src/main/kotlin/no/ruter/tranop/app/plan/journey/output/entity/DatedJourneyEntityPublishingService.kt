package no.ruter.tranop.app.plan.journey.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import no.ruter.tranop.app.plan.journey.db.InternalDatedJourney
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture

@Service
class DatedJourneyEntityPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    datedJourneyConfigProperties: DatedJourneyConfigProperties,
    val datedJourneyService: DatedJourneyService,
    val datedJourneyOutputMapper: DatedJourneyEntityOutputMapper,
) : AbstractKafkaPublisherService<String?, Serde<String?>, DatedJourneyKeyV2, SpecificAvroSerde<DatedJourneyKeyV2>>(
        kafkaConfigService.datedJourneyOutputProducer,
        datedJourneyConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService,
    ) {
    fun publish(
        datedJourney: DatedJourneyKeyV2,
        currentRevision: Int,
    ): Result<Int> {
        val future = CompletableFuture<Result<Int>>()
        publishToKafka(datedJourney.entityHeader.key, datedJourney) { exception ->
            when (exception) {
                null -> {
                    val nPublished = datedJourneyService.markKafkaPublished(datedJourney.entityHeader.key, currentRevision)
                    future.complete(Result.success(nPublished ?: 0))
                }
                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }

    fun publish(datedJourney: InternalDatedJourney) {
        val journey = datedJourney.journey
        val revision = datedJourney.record.revision
        datedJourneyOutputMapper.createOutputMessage(journey).value?.let { message ->
            publish(message, revision)
        }
    }
}
