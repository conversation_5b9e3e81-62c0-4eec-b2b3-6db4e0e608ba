package no.ruter.tranop.app.plan.link.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointLinkKeyV2
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture

@Service
class StopPointLinkEntityPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    stopPointLinkConfigProperties: StopPointLinkConfigProperties,
    private val repository: StopPointLinkRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DatedJourneyStopPointLinkKeyV2,
        SpecificAvroSerde<DatedJourneyStopPointLinkKeyV2>,
    >(
        kafkaConfigService.stopPointLinkOutputProducer,
        stopPointLinkConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService,
    ) {
    fun publish(
        input: DatedJourneyStopPointLinkKeyV2,
        currentRevision: Int,
    ): Result<Int> {
        val future = CompletableFuture<Result<Int>>()

        publishToKafka(input.entityHeader.key, input) { exception ->
            when (exception) {
                null -> {
                    val nPublished = repository.markPublished(input.entityHeader.key, currentRevision)
                    future.complete(Result.success(nPublished ?: 0))
                }

                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }
}
