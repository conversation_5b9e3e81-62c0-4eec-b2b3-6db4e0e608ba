package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.kafka.PDJDatedJourneyDeserializer
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaConsumer
import no.ruter.tranop.app.common.dataflow.kafka.config.KafkaConsumerConfig
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    name = [
        KafkaConsumerConfig.CONF_KEY_ENABLED,
        "${DatedJourneyInputConsumer.CONF_PREFIX}.enabled",
    ],
    havingValue = "true",
)
class DatedJourneyInputConsumer(
    private val datedJourneyInputService: DatedJourneyInputService,
    private val insightService: InsightService,
) : AbstractKafkaConsumer(
        channel = datedJourneyInputService.defaultChannel,
        insightService = insightService,
    ) {
    companion object {
        const val CONF_PREFIX = "${DatedJourneyConfigProperties.CONF_PREFIX}.input.kafka.dto.consumer"
    }

    private val deserializer = PDJDatedJourneyDeserializer()

    @KafkaListener(
        topics = ["\${$CONF_PREFIX.topic}"],
        groupId = "\${$CONF_PREFIX.group}",
    )
    fun listenGroup(
        consumerRecord: ConsumerRecord<String?, ByteArray?>,
        ack: Acknowledgment,
    ) {
        try {
            process(
                record = consumerRecord,
                deserializer = deserializer,
                handler = datedJourneyInputService::process,
            )
        } catch (e: Exception) {
            logger.error("Not able to process dated journey [${consumerRecord.key()} / ${consumerRecord.headers()}]", e)
        } finally {
            ack.acknowledge()
        }
    }
}
