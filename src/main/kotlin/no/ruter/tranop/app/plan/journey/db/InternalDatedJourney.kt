package no.ruter.tranop.app.plan.journey.db

import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOJourneyType
import java.time.LocalDate
import java.time.OffsetDateTime

class InternalDatedJourney(
    val journey: DTODatedJourney,
    val operatingDate: LocalDate?,
    val firstDeparture: OffsetDateTime?,
    val firstArrival: OffsetDateTime?,
    val lastDeparture: OffsetDateTime?,
    val lastArrival: OffsetDateTime?,
    val vehicleTaskRef: String?,
    val datedBlockRef: String?,
    val cancelled: Boolean?,
    val partiallyCancelled: Boolean?,
    val record: DatedJourneyRecord,
    val lineId: String?,
    val vehicleJourneyId: String?,
    val datedServiceJourneyId: String?,
) {
    // TODO: This should not be needed. Used for printing out status details atm.
    override fun toString(): String {
        val refs = journey.journeyReferences
        return "DatedJourney(" +
            "vehicleTaskRef=${refs.vehicleTaskRef}, " +
            "operatingDate=$operatingDate, " +
            "firstDeparture=$firstDeparture, " +
            "firstArrival=$firstArrival, " +
            "lastDeparture=$lastDeparture, " +
            "lastArrival=$lastArrival, " +
            "type=${journey.type}, " +
            "lineId=${journey.line?.lineRef}"
    }

    val deadRun = DTOJourneyType.DEAD_RUN == journey.type

    val serviceJourney = DTOJourneyType.SERVICE_JOURNEY == journey.type

    val datedJourneyV2Ref = journey.ref ?: null
}
