package no.ruter.tranop.app.plan.stop.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyStopPointKeyV2
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture

@Service
class StopPointEntityPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    stopPointConfigProperties: StopPointConfigProperties,
    val repository: StopPointRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DatedJourneyStopPointKeyV2,
        SpecificAvroSerde<DatedJourneyStopPointKeyV2>,
    >(
        kafkaConfigService.stopPointOutputProducer,
        stopPointConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService,
    ) {
    fun publish(
        input: DatedJourneyStopPointKeyV2,
        currentRevision: Int,
    ): Result<Int> {
        val future = CompletableFuture<Result<Int>>()

        publishToKafka(input.entityHeader.key, input) { exception ->
            when (exception) {
                null -> {
                    val nPublished = repository.markPublished(input.entityHeader.key, currentRevision)
                    future.complete(Result.success(nPublished ?: 0))
                }

                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }
}
