package no.ruter.tranop.app.plan.link.lifecycle

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycle
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.link.output.entity.StopPointLinkEntityPublishRoutine
import no.ruter.tranop.app.plan.link.output.internal.StopPointLinkDTOPublishRoutine
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = StopPointLinkLifeCycleConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class StopPointLinkLifeCycle(
    val timeService: TimeService,
    publishStopPointLinkRoutine: StopPointLinkEntityPublishRoutine,
    publishStopPointLinkDTORoutine: StopPointLinkDTOPublishRoutine,
) : AbstractLifeCycle(
        routines =
            listOf(
                publishStopPointLinkRoutine,
                publishStopPointLinkDTORoutine,
            ),
    ) {
    companion object {
        const val FREQ_PREFIX = "${StopPointLinkLifeCycleConfig.CONF_PREFIX}.frequent"
    }

    @Scheduled(
        fixedRateString = "\${$FREQ_PREFIX.fixedRate}",
        initialDelayString = "\${$FREQ_PREFIX.initialDelay}",
    )
    @SchedulerLock(
        name = "\${$FREQ_PREFIX.schedulerLockName}",
        lockAtMostFor = "\${$FREQ_PREFIX.lockAtMostFor}",
        lockAtLeastFor = "\${$FREQ_PREFIX.lockAtLeastFor}",
    )
    fun runFrequent() {
        execute(
            started = timeService.now(),
            frequent = true,
        )
    }
}
