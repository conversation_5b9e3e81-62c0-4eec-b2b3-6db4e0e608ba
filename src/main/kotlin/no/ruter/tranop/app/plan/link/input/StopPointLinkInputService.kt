package no.ruter.tranop.app.plan.link.input

import no.ruter.plandata.journey.dated.v2.dto.model.common.link.PDJStopPointLink
import no.ruter.tranop.app.common.ProcessingContext
import no.ruter.tranop.app.common.ProcessingContext.NOOPProcessingContext
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.common.AbstractInputService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputMapper
import no.ruter.tranop.app.plan.link.db.StopPointLinkRepository
import org.springframework.stereotype.Component

@Component
class StopPointLinkInputService(
    val repository: StopPointLinkRepository,
    insightService: InsightService,
) : AbstractInputService(
        defaultChannel = repository.recordType.channels.input,
        insightService = insightService,
    ) {
    private val inputMapper = DatedJourneyInputMapper()

    fun store(ctx: StopPointLinkInputContext): Boolean = processInput(ctx, repository::store)

    fun process(
        key: String?,
        link: PDJStopPointLink?,
    ): ProcessingContext {
        link?.let {
            val ctx =
                StopPointLinkInputContext(
                    channel = defaultChannel,
                    link = inputMapper.mapStopPointLink(it),
                    ref = it.ref ?: key,
                )
            store(ctx)
            return ctx
        }
        return NOOPProcessingContext(channel = defaultChannel)
    }
}
