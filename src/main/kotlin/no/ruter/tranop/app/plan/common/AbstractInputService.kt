package no.ruter.tranop.app.plan.common

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.ValidationContext
import no.ruter.tranop.app.common.insight.ErrorType
import no.ruter.tranop.app.common.insight.InsightService
import org.springframework.retry.support.RetryTemplate
import org.springframework.retry.support.RetryTemplateBuilder

abstract class AbstractInputService(
    val defaultChannel: DataChannel,
    protected val insightService: InsightService,
    retryable: List<Class<out Throwable>> = listOf(Exception::class.java),
    maxAttempts: Int = 3,
) {
    protected val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    private val retryTemplate: RetryTemplate =
        RetryTemplateBuilder()
            .retryOn(retryable)
            .fixedBackoff(50)
            .maxAttempts(maxAttempts)
            .build()

    protected fun <C : ValidationContext> processInput(
        context: C,
        processor: (C) -> <PERSON><PERSON><PERSON>,
    ): Boolean =
        try {
            retryTemplate.execute<Boolean, Exception> { _ ->
                processor(context)
            }
        } catch (e: Exception) {
            context.addInternalException(
                e = e,
                log = log,
                type = ErrorType.DB_STORE,
                subject = context.channel.subject,
                error = true,
                metadata = context.metadata,
            )
            false
        }
}
