package no.ruter.tranop.app.plan.stop.output.internal

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.dated.journey.dto.kafka.DTOStopPointSerde
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture

@Service
class StopPointDTOPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    stopPointLinkConfigProperties: StopPointLinkConfigProperties,
    val repository: StopPointRepository,
) : AbstractKafkaPublisherService<
        String?,
        Serde<String?>,
        DTOStopPoint,
        DTOStopPointSerde,
    >(
        kafkaConfigService.stopPointDtoOutputProducer,
        stopPointLinkConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService,
    ) {
    fun publish(
        input: DTOStopPoint,
        currentRevision: Int,
    ): Result<Int> {
        val future = CompletableFuture<Result<Int>>()

        publishToKafka(input.ref, input) { exception ->
            when (exception) {
                null -> {
                    // TODO should this function be responsible for this or it should be moved one lvl up and even made generic
                    val nPublished = repository.markDTOPublished(input.ref, currentRevision)
                    future.complete(Result.success(nPublished ?: 0))
                }

                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }
}
