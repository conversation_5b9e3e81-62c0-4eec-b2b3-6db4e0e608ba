package no.ruter.tranop.app.plan.stop.lifecycle

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleRoutine

abstract class StopPointBaseRoutine(
    appInfoProperties: AppInfoProperties,
    name: String,
    insightService: InsightService,
    config: AbstractLifeCycleConfig,
) : AbstractLifeCycleRoutine(
        appInfoProperties,
        name,
        insightService,
        config,
        RecordType.STOP.channels.internal,
    )
