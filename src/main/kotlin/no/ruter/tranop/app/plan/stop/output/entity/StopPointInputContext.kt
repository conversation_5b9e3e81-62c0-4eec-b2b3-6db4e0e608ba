package no.ruter.tranop.app.plan.stop.output.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.AbstractInputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.output.toInsightEvent
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import no.ruter.tranop.journey.dated.dto.metadata
import java.time.OffsetDateTime

class StopPointInputContext(
    override val channel: DataChannel,
    val stopPoint: DTOStopPoint,
    override val received: OffsetDateTime,
    override val ref: String = stopPoint.ref,
    override val refPath: String = "$.ref",
    override val header: DTOMessageHeader? = stopPoint.header,
    override val events: Collection<InputEvent> =
        stopPoint.events.map {
            it.toInsightEvent()
        },
) : AbstractInputContext() {
    override val metadata: Map<String, Any?>
        get() = stopPoint.metadata()
    override val metrics: Map<String, List<String>>
        get() = emptyMap()
}
