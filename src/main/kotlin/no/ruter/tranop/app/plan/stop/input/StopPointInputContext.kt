package no.ruter.tranop.app.plan.stop.input

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.AbstractDbProcessingContext
import no.ruter.tranop.app.common.mapping.toStatus
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint

class StopPointInputContext(
    val journey: DTODatedJourney? = null,
    val stopPoint: DTOStopPoint,
    val ref: String? = stopPoint.ref,
    override val channel: DataChannel = RecordType.STOP.channels.input,
    override val recordMetadata: Boolean = true,
) : AbstractDbProcessingContext() {
    override val traceId: String?
        get() = stopPoint.header?.traceId ?: journey?.header?.traceId

    override val metadata: Map<String, Any?>
        get() =
            mapOf(
                LogKey.TRACE_ID to traceId,
                LogKey.ENTITY_DATED_JOURNEY_STOP_POINT_KEY_V2_REF to stopPoint.ref,
                LogKey.QUAY_REF to stopPoint.quayRef,
                "insert" to insert,
                "update" to update,
                "duplicate" to duplicate,
                "json_diff" to jsonDiff?.asString(),
            ).filterValues { it != null }

    override val summary: String
        get() =
            listOf(
                stopPoint.ref,
                stopPoint.name,
                stopPoint.quayRef,
                when {
                    insert -> "insert"
                    update -> "update"
                    duplicate -> "duplicate"
                    else -> "none"
                },
                traceId,
            ).joinToString(" / ")
    override val insightKey: String
        get() = "${channel.insightKey}.${statusDetails.toStatus().code?.value ?: DTOStatusCode.UNKNOWN}"
}
