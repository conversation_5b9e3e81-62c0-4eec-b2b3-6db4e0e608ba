package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

/**
 * Life-cycle routine for flagging dated journeys for re-publishing to Kafka.
 *
 * Note that this routine affects whats is published on two different Kafka topics:
 * 1. Entity topic
 * 2. Internal DTO topic
 *
 * TODO: Split re-publishing to different Kafka topics into separate routines?
 */
@Component
class DatedJourneyKafkaRepublishRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig,
    val datedJourneyService: DatedJourneyService,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_REPUBLISH,
        insightService = insightService,
        datedJourneyLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int = datedJourneyService.unPublishOperatingDate(started.plusDays(1).toLocalDate())
}
