package no.ruter.tranop.app.plan.journey.output.internal

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.dated.journey.dto.kafka.DTODatedJourneySerde
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service
import java.util.concurrent.CompletableFuture

@Service
class AssignmentJourneyPublishingService(
    kafkaConfigService: KafkaConfigService,
    insightService: InsightService,
    assignmentJourneyProperties: AssignmentJourneyConfigProperties,
    val datedJourneyService: DatedJourneyService,
) : AbstractKafkaPublisherService<String?, Serde<String?>, DTODatedJourney, DTODatedJourneySerde>(
        kafkaConfigService.assignmentJourneyOutputProducer,
        assignmentJourneyProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService,
    ) {
    fun publish(
        datedJourney: DTODatedJourney,
        currentRevision: Int,
    ): Result<Int> {
        val future = CompletableFuture<Result<Int>>()
        publishToKafka(datedJourney.ref, datedJourney) { exception ->
            when (exception) {
                null -> {
                    val nPublished = datedJourneyService.markKafkaPublished(datedJourney.ref, currentRevision)
                    future.complete(Result.success(nPublished ?: 0))
                }
                else -> {
                    future.complete(Result.failure(exception))
                }
            }
        }

        return future.get()
    }
}
