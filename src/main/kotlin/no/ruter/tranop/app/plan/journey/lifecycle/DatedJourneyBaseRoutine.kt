package no.ruter.tranop.app.plan.journey.lifecycle

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleRoutine

abstract class DatedJourneyBaseRoutine(
    appInfoProperties: AppInfoProperties,
    name: String,
    insightService: InsightService,
    abstractLifeCycleConfig: AbstractLifeCycleConfig,
) : AbstractLifeCycleRoutine(
        appInfoProperties = appInfoProperties,
        name = name,
        insightService = insightService,
        abstractLifeCycleConfig = abstractLifeCycleConfig,
        channel = RecordType.DATED_JOURNEY.channels.internal,
    ) {
    protected fun record(
        meta: Map<String, Any?> = mapOf(),
        type: String,
        journeyRefs: Set<String>,
    ) {
        if (journeyRefs.isEmpty()) {
            return
        }

        val metadata = meta.toMutableMap()
        val subject = "lifecycle-${channel.subject}-$name"

        metadata[type] = journeyRefs.size
        metadata[LogKey.MESSAGE_SUBJECT] = subject
        metadata[LogKey.ENTITY_DATED_JOURNEY_KEY_V2_REF] = journeyRefs

        val msg = "$subject: $type: ${journeyRefs.size}"

        logger.info(msg = msg, metadata = metadata)
    }
}
