package no.ruter.tranop.app.plan.journey

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class DatedJourneyStatisticsRoutine(
    insightService: InsightService,
    appInfoProperties: AppInfoProperties,
    datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig,
    private val datedJourneyStatisticsService: DatedJourneyStatisticsService,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_STATISTICS,
        insightService = insightService,
        datedJourneyLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        datedJourneyStatisticsService.renderStats()
        return 1
    }
}
