package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney

class DatedJourneyInputValidator(
    private val timeService: TimeService,
    private val minOperatingDateOffset: Long? = null,
    private val maxOperatingDateOffset: Long? = null,
) {
    companion object {
        const val KEY_NONE = "-"
    }

    private val inputMapper = DatedJourneyInputMapper()
    private val defaultChannel = RecordType.DATED_JOURNEY.channels.input

    fun validate(
        key: String?,
        input: PDJDatedJourney?,
        channel: DataChannel = defaultChannel,
        recordMetadata: Boolean = false,
    ): DatedJourneyInputContext {
        val internalJourney = input?.let(inputMapper::mapDatedJourney)
        return validate(key, internalJourney, channel, recordMetadata)
    }

    fun validate(
        key: String?,
        input: DTODatedJourney?,
        channel: DataChannel = defaultChannel,
        recordMetadata: Boolean = false,
    ): DatedJourneyInputContext {
        val now = timeService.now()
        if (input == null) {
            return if (key.isNullOrEmpty()) {
                val context = DatedJourneyInputContext(ref = KEY_NONE, input = null, recordMetadata = true)
                context.validateFailed(value = null, field = "kafka-key", error = true, desc = "missing kafka key")
                context
            } else {
                DatedJourneyInputContext(ref = key, input = null, channel = channel, recordMetadata = false)
            }
        }

        val context = DatedJourneyInputContext(input.ref, input, channel = channel, recordMetadata = recordMetadata)

        val header = input.header
        val expiresPath = "header.expiresTimestamp"
        val expiresTimestamp =
            context.validateTimestamp(
                value = header?.expiresTimestamp,
                field = expiresPath,
                required = false,
            )

        if (expiresTimestamp != null && expiresTimestamp.isBefore(now)) {
            context.addStatusDetail(
                code = DTOStatusCode.ERR_VALIDATE,
                reason = "message.expired",
                desc = "dated journey message expired",
                field = expiresPath,
                value = header?.expiresTimestamp,
            )
            return context
        }

        // Validate dated journey ref.
        context.validateNotBlank(
            value = context.ref,
            field = "ref",
            error = true,
        )

        // Validate operating date.
        val today = timeService.today()
        val operatingDate =
            context.validateLocalDate(
                value = input.operatingDate,
                field = "operatingDate",
            )
        context.operatingDate = operatingDate

        val journeyReferences = input.journeyReferences ?: null
        // Validate vehicle task ref.
        context.vehicleTaskRef =
            context.validateNotBlank(
                value = journeyReferences?.vehicleTaskRef,
                field = "journeyReferences.vehicleTaskRef",
            )

        context.datedBlockRef =
            context.validateNotBlank(
                value = input.journeyReferences?.datedBlockRef,
                field = "journeyReferences.datedBlockRef",
            )

        context.externalJourneyRef =
            context.validateNotBlank(
                value = journeyReferences?.externalJourneyRef,
                field = "journeyReferences.externalJourneyRef",
            )

        context.externalJourneyRefV2 =
            context.validateNotBlank(
                value = journeyReferences?.externalJourneyRefV2,
                field = "journeyReferences.externalJourneyRefV2",
            )

        validateCalls(context)

        if (operatingDate != null && minOperatingDateOffset != null && maxOperatingDateOffset != null) {
            val minDate = today.plusDays(minOperatingDateOffset) // Add negative offset.
            val maxDate = today.plusDays(maxOperatingDateOffset)
            if (operatingDate.isBefore(minDate)) {
                return DatedJourneyInputContext(context.ref, input = null).apply {
                    skipReason = "operating.date.before.operational.window"
                }
            } else if (operatingDate.isAfter(maxDate)) {
                return DatedJourneyInputContext(context.ref, input = null).apply {
                    skipReason = "operating.date.after.operational.window"
                }
            }
        }
        return context
    }

    private fun validateCalls(context: DatedJourneyInputContext) {
        val path = "plan.calls"
        val calls =
            context.validateSizeGreaterThan(
                context.input
                    ?.plan
                    ?.calls
                    ?.filterNotNull(),
                size = 1,
                field = path,
                error = true,
            )
        if (!calls.isNullOrEmpty() && calls.size > 1) {
            context.firstDepartureDateTime =
                context.validateTimestamp(
                    value = calls.firstOrNull()?.plannedDeparture,
                    field = "$path.first.plannedDeparture",
                )

            context.lastArrivalDateTime =
                context.validateTimestamp(
                    value = calls.lastOrNull()?.plannedArrival,
                    field = "$path.last.plannedArrival",
                )

            context.lastDepartureDateTime =
                context.validateTimestamp(
                    value = calls[calls.lastIndex - 1].plannedDeparture,
                    field = "$path.last.plannedDeparture",
                )

            context.firstArrivalDateTime =
                context.validateTimestamp(
                    value = calls[1].plannedArrival,
                    field = "$path.first.plannedArrival",
                )
        }
    }
}
