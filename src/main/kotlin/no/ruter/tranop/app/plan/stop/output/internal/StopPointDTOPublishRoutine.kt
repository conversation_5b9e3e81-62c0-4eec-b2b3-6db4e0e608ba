package no.ruter.tranop.app.plan.stop.output.internal

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.stop.db.StopPointRepository
import no.ruter.tranop.app.plan.stop.lifecycle.StopPointBaseRoutine
import no.ruter.tranop.app.plan.stop.lifecycle.StopPointLifeCycleConfig
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class StopPointDTOPublishRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    stopPointLifeCycleConfig: StopPointLifeCycleConfig,
    private val repository: StopPointRepository,
    private val publishingService: StopPointDTOPublishingService,
) : StopPointBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_PUBLISH,
        insightService = insightService,
        stopPointLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        val batchSize = 256
        val skip = 0
        var totalPublished = 0
        val seenFailedRefs = mutableSetOf<String>()
        val olderThanDateTime = started.minus(config.olderThan)

        try {
            generateSequence {
                repository.findDTOUnpublished(AbstractQueryBuilder.Pagination(batchSize, skip), seenFailedRefs, olderThanDateTime)
            }.takeWhile { it.isNotEmpty() }
                .forEach { stopPointBatch ->
                    stopPointBatch.forEach { internal ->
                        val currentRevision = internal.record.revision
                        val result = publishingService.publish(internal.stopPoint, currentRevision)
                        if (result.isFailure) {
                            handleException(e = result.exceptionOrNull() as Exception)
                            seenFailedRefs.add(internal.stopPoint.ref)
                            recordInsight("failed")
                        } else {
                            totalPublished++
                            recordInsight("published")
                        }
                    }

                    if (seenFailedRefs.size >= batchSize) {
                        throw Exception("too many errors occurred while running lifecycle, aborting...")
                    }
                }
        } catch (e: Exception) {
            handleException(e = e)
        }
        if (seenFailedRefs.isNotEmpty()) {
            logger.warn("some refs couldn't be published during lifecycle run: $seenFailedRefs")
        }
        return totalPublished
    }
}
