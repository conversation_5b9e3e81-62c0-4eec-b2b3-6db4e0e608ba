package no.ruter.tranop.app.plan.journey.output.internal

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serdes
import org.springframework.stereotype.Service

@Service
class AssignmentJourneyTombstoneService(
    kafkaConfigService: KafkaConfigService,
    assignmentJourneyConfigProperties: AssignmentJourneyConfigProperties,
    insightService: InsightService,
) : AbstractKafkaPublisherService<String?, Serde<String?>, ByteArray, Serdes.ByteArraySerde>(
        kafkaConfigService.assignmentJourneyTombstoneProducer,
        assignmentJourneyConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO),
        insightService,
    )
