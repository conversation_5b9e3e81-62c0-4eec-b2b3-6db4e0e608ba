package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.event.journey.input.JourneyEventInputService
import no.ruter.tranop.app.plan.common.AbstractInputService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.JourneyUtils
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import no.ruter.tranop.app.plan.journey.input.db.DatedJourneyInputRepository
import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityPublishingService
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.DTOEventType
import org.springframework.stereotype.Service

@Service
class DatedJourneyInputService(
    val datedJourneyService: DatedJourneyService,
    timeService: TimeService,
    insightService: InsightService,
    val journeyInputRepo: DatedJourneyInputRepository,
    val journeyEventService: JourneyEventInputService,
    val datedJourneyEntityPublishingService: DatedJourneyEntityPublishingService,
    private val datedJourneyConfigProperties: DatedJourneyConfigProperties,
) : AbstractInputService(
        defaultChannel = RecordType.DATED_JOURNEY.channels.input,
        insightService = insightService,
        retryable = listOf(org.jooq.exception.DataChangedException::class.java),
        maxAttempts = 5,
    ) {
    private val inputMapper = DatedJourneyInputMapper()
    private final val inputConfig get() = datedJourneyConfigProperties.getKafkaInputConfig(AbstractSectionConfigProperties.CONFIG_KEY_DTO)

    private val validator =
        DatedJourneyInputValidator(
            timeService = timeService,
            minOperatingDateOffset = inputConfig.minOperatingDateOffset,
            maxOperatingDateOffset = inputConfig.maxOperatingDateOffset,
        )

    fun process(
        key: String?,
        datedJourney: PDJDatedJourney?,
    ): DatedJourneyInputContext = processInternal(key = key, internalJourney = datedJourney?.let(inputMapper::mapDatedJourney))

    fun processInternal(
        key: String?,
        internalJourney: DTODatedJourney?,
    ): DatedJourneyInputContext {
        val context = validator.validate(key, internalJourney)
        processInput(context, ::process)
        return context
    }

    fun process(context: DatedJourneyInputContext): Boolean {
        if (context.journeyTombstone) {
            datedJourneyService.deleteJourneysTransactional(context)
        } else {
            // TODO: Wrap these operations in a separate @Transactional?
            journeyInputRepo.store(context) // Store "raw" input before pre-processing.

            // journey where deleted=true must be published as entity too since some consumers (Snowflake) depends on it
            // since only tombstone wouldn't be reflected there
            preProcess(context)
            journeyEventService.process(context)
            datedJourneyService.store(context)
            if (context.journeyDeleted) {
                datedJourneyService
                    .fetchDatedJourneyByRef(context.ref)
                    ?.let { existing ->
                        datedJourneyEntityPublishingService.publish(existing)
                    }
            }
        }
        return context.stored > 0
    }

    val eventTypes =
        listOf(
            DTOEventType.UNKNOWN,
            DTOEventType.IMPORTED,
            DTOEventType.UPDATED,
            DTOEventType.PATCHED,
            DTOEventType.CREATED,
        )

    private fun preProcess(context: DatedJourneyInputContext) {
        val journey = context.input
        journey?.apply {
            this.cancelled = false
            this.omitted = false
            this.journeyState = this.journeyState ?: JourneyUtils.createJourneyState()
            this.events = this.events.filter { it.type != null && eventTypes.contains(it.type) }
            this.plan.apply {
                this.calls.forEach { call ->
                    call.apply {
                        this.cancelled = false
                        this.omitted = false
                        this.originalStopPointBehaviourType?.let {
                            this.stopPointBehaviourType = it
                        }
                    }
                }
            }
        }
    }
}
