package no.ruter.tranop.app.plan.journey.input.db

import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.record.base.BaseRecordRepository
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputContext
import no.ruter.tranop.assignmentmanager.db.sql.tables.DatedJourneyInputTable
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyInputRecord
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class DatedJourneyInputRepository(
    dslContext: DSLContext,
    insightService: InsightService,
    timeService: TimeService,
) : BaseRecordRepository<
        DatedJourneyInputRecord,
        DatedJourneyInputTable,
        DatedJourneyInputData,
        DatedJourneyInputRecordMapper,
    >(
        recordType = TYPE,
        dslContext = dslContext,
        sortFields = ORDER_FIELDS,
        timeService = timeService,
        insightService = insightService,
        recordMapper = DatedJourneyInputRecordMapper(insightService),
    ) {
    companion object {
        const val METRIC_INSERTED = "inserted"
        const val METRIC_UPDATED = "updated"
        const val METRIC_CANCELLED = "cancelled"
        const val METRIC_PARTIALLY_CANCELLED = "partially_cancelled"

        val TYPE = RecordType.DATED_JOURNEY_INPUT
        val TABLE = TYPE.table

        val ORDER_FIELDS =
            listOf(
                TABLE.FIRST_DEPARTURE.asc(),
                TABLE.LAST_ARRIVAL.asc(),
                TABLE.OPERATING_DATE.asc(),
                TABLE.ID.desc(),
            )
    }

    fun store(
        context: DatedJourneyInputContext,
        now: OffsetDateTime = timeService.now(),
    ): DatedJourneyInputContext {
        if (context.invalid()) {
            return context
        }

        val ref = context.ref ?: return context

        val datedJourney = fetchByRef(ref)
        val record: DatedJourneyInputRecord =
            if (datedJourney == null) {
                context.update = false
                dslContext.newRecord(table)
            } else {
                context.update = true
                datedJourney.record
            }
        recordMapper.update(record, context, now = now)
        return store(context, record)
    }

    private fun store(
        context: DatedJourneyInputContext,
        record: DatedJourneyInputRecord,
    ): DatedJourneyInputContext {
        val res = record.store()

        if (context.update) {
            context.updated = res
            context.addSimpleDetail(METRIC_UPDATED, res)
        } else {
            context.inserted = res
            context.addSimpleDetail(METRIC_INSERTED, res)
        }
        if (context.cancelled) {
            context.addSimpleDetail(METRIC_CANCELLED, res)
        }

        if (context.partiallyCancelled) {
            context.addSimpleDetail(METRIC_PARTIALLY_CANCELLED, res)
        }

        context.stored = res
        return context
    }
}
