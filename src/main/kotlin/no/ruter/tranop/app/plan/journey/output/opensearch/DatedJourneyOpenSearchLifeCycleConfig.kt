package no.ruter.tranop.app.plan.journey.output.opensearch

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = DatedJourneyOpenSearchLifeCycleConfig.KEY_PREFIX)
class DatedJourneyOpenSearchLifeCycleConfig : AbstractLifeCycleConfig(KEY_PREFIX) {
    companion object {
        const val KEY_PREFIX = "app.config.datedjourney-opensearch.lifecycle"
    }
}
