package no.ruter.tranop.app.plan.link.input

import no.ruter.rdp.logging.LogKey
import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.RecordType
import no.ruter.tranop.app.common.db.AbstractDbProcessingContext
import no.ruter.tranop.app.common.mapping.toStatus
import no.ruter.tranop.assignment.dto.model.value.DTOStatusCode
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink

class StopPointLinkInputContext(
    val journey: DTODatedJourney? = null,
    val link: DTOStopPointLink,
    val ref: String? = link.ref,
    override val channel: DataChannel = RecordType.LINK.channels.input,
    override val recordMetadata: Boolean = true,
) : AbstractDbProcessingContext() {
    override val traceId: String?
        get() = link.header?.traceId ?: journey?.header?.traceId

    override val metadata: Map<String, Any?>
        get() =
            mapOf(
                LogKey.TRACE_ID to traceId,
                "stopPointLink_ref" to link.ref,
                "stopPointRef_from" to link.fromStopPointRef,
                "stopPointRef_to" to link.toStopPointRef,
                "quayRef_from" to link.fromQuayRef,
                "quayRef_to" to link.toQuayRef,
                "insert" to insert,
                "update" to update,
                "duplicate" to duplicate,
                "json_diff" to jsonDiff?.asString(),
            ).filterValues { it != null }

    override val summary: String
        get() =
            listOf(
                link.ref,
                link.fromStopPointRef,
                link.toStopPointRef,
                link.fromQuayRef,
                link.toQuayRef,
                when {
                    insert -> "insert"
                    update -> "update"
                    duplicate -> "duplicate"
                    else -> "none"
                },
                traceId,
            ).joinToString(" / ")

    override val insightKey: String
        get() = "${channel.insightKey}.${statusDetails.toStatus().code?.value ?: DTOStatusCode.UNKNOWN}"
}
