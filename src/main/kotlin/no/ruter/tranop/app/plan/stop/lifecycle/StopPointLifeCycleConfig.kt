package no.ruter.tranop.app.plan.stop.lifecycle

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.plan.stop.config.StopPointConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = StopPointLifeCycleConfig.CONF_PREFIX)
class StopPointLifeCycleConfig : AbstractLifeCycleConfig(CONF_PREFIX) {
    companion object {
        const val CONF_PREFIX = "${StopPointConfigProperties.CONF_PREFIX}.lifecycle"
    }
}
