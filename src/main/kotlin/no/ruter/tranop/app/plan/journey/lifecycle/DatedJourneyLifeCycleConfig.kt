package no.ruter.tranop.app.plan.journey.lifecycle

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = DatedJourneyLifeCycleConfig.CONF_PREFIX)
class DatedJourneyLifeCycleConfig : AbstractLifeCycleConfig(CONF_PREFIX) {
    companion object {
        const val CONF_PREFIX = "${DatedJourneyConfigProperties.CONF_PREFIX}.lifecycle"
    }
}
