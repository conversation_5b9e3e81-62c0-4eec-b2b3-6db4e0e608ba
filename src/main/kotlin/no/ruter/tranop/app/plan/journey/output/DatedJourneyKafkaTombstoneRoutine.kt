package no.ruter.tranop.app.plan.journey.output

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyLifeCycleConfig
import no.ruter.tranop.app.plan.journey.output.entity.DatedJourneyEntityTombstoneService
import no.ruter.tranop.app.plan.journey.output.internal.AssignmentJourneyTombstoneService
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

/**
 * Life-cycle routine for publishing tombstones for deleted dated journeys to Kafka.
 *
 * Note that this routine publishes to two different Kafka topics:
 * 1. Entity topic
 * 2. Internal DTO topic
 *
 * TODO: Split tombstone publishing to different Kafka topics into separate routines?
 */
@Component
class DatedJourneyKafkaTombstoneRoutine(
    appInfoProperties: AppInfoProperties,
    insightService: InsightService,
    datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig,
    val datedJourneyService: DatedJourneyService,
    val datedJourneyTombstoningService: DatedJourneyEntityTombstoneService,
    val assignmentJourneyTombstoningService: AssignmentJourneyTombstoneService,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_TOMBSTONE,
        insightService = insightService,
        datedJourneyLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        var result = 0
        try {
            val olderThanDateTime = started.minus(config.olderThan)
            val refsToTombstone = datedJourneyService.fetchRefsForTombstoning(fromDateTime = null, toDateTime = olderThanDateTime)

            refsToTombstone.forEach {
                val entityTombstoneResult = datedJourneyTombstoningService.tombstone(it)
                val dtoTombstoneResult = assignmentJourneyTombstoningService.tombstone(it)

                if (entityTombstoneResult.isFailure || dtoTombstoneResult.isFailure) {
                    val exc = entityTombstoneResult.exceptionOrNull()
                    exc?.let { e ->
                        handleException(e = e as Exception)
                    }
                    val exc2 = dtoTombstoneResult.exceptionOrNull()
                    exc2?.let { e ->
                        handleException(e = e as Exception)
                    }
                } else {
                    val nTombstoned = datedJourneyService.markTombstoned(it)
                    result += nTombstoned ?: 0
                }
            }
        } catch (e: Exception) {
            handleException(e = e)
        }
        return result
    }
}
