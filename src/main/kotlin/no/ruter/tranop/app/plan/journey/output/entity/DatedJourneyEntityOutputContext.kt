package no.ruter.tranop.app.plan.journey.output.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.InputMessage
import no.ruter.tranop.app.common.mapping.output.AbstractOutputContext
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.dated.dto.metadata

class DatedJourneyEntityOutputContext(
    input: InputMessage<DTODated<PERSON>ourney, DatedJourneyEntityInputContext>,
) : AbstractOutputContext<DTODatedJourney, DatedJourneyEntityInputContext>(input) {
    override val channel: DataChannel
        get() = input.context.channel

    override val traceId: String?
        get() = input.context.traceId

    override val metadata: Map<String, Any?>
        get() = input.value.metadata()
    override val metrics: Map<String, List<String>>
        get() = emptyMap()
}
