package no.ruter.tranop.app.plan.journey

import no.ruter.rdp.logging.Logger
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.plan.journey.db.DatedJourneyRepository
import org.springframework.stereotype.Component

@Component
class DatedJourneyStatisticsService(
    val datedJourneyRepository: DatedJourneyRepository,
) {
    val log: Logger = LoggerFactory.getLogger(javaClass.canonicalName)

    fun renderStats() {
        val countPerOperatingDate = datedJourneyRepository.countPerOperatingDate()

        countPerOperatingDate.forEach { (operatingDate, count) ->
            log.info(
                "stats.datedjourney: $operatingDate=$count",
                mapOf(
                    "operatingDate" to operatingDate.toString(),
                    "count" to count,
                    "dayOfWeek" to operatingDate.dayOfWeek,
                ),
            )
        }
    }
}
