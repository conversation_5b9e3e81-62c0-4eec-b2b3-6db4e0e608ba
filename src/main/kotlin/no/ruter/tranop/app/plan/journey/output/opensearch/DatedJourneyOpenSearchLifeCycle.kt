package no.ruter.tranop.app.plan.journey.output.opensearch

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycle
import no.ruter.tranop.app.common.time.TimeService
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = DatedJourneyOpenSearchLifeCycleConfig.KEY_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class DatedJourneyOpenSearchLifeCycle(
    val timeService: TimeService,
    deleteRoutine: DatedJourneyOpenSearchDeleteRoutine,
    publishRoutine: DatedJourneyOpenSearchPublishRoutine,
) : AbstractLifeCycle(
        routines =
            listOf(
                publishRoutine,
                deleteRoutine,
            ),
    ) {
    companion object {
        const val FREQ_PREFIX = "${DatedJourneyOpenSearchLifeCycleConfig.KEY_PREFIX}.frequent"
    }

    @Scheduled(
        fixedRateString = "\${${FREQ_PREFIX}.fixedRate}",
        initialDelayString = "\${${FREQ_PREFIX}.initialDelay}",
    )
    @SchedulerLock(
        name = "\${${FREQ_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${FREQ_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${FREQ_PREFIX}.lockAtLeastFor}",
    )
    fun runFrequent() {
        execute(
            started = timeService.now(),
            frequent = true,
        )
    }
}
