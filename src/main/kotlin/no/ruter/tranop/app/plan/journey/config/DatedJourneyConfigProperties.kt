package no.ruter.tranop.app.plan.journey.config

import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = DatedJourneyConfigProperties.CONF_PREFIX)
class DatedJourneyConfigProperties : AbstractSectionConfigProperties() {
    lateinit var toggles: DatedJourneyToggles

    companion object {
        const val CONF_PREFIX = "app.config.datedjourney"
    }
}
