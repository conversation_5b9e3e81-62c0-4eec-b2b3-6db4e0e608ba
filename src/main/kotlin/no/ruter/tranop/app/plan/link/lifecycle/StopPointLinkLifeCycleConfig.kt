package no.ruter.tranop.app.plan.link.lifecycle

import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycleConfig
import no.ruter.tranop.app.plan.link.config.StopPointLinkConfigProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = StopPointLinkLifeCycleConfig.CONF_PREFIX)
class StopPointLinkLifeCycleConfig : AbstractLifeCycleConfig(CONF_PREFIX) {
    companion object {
        const val CONF_PREFIX = "${StopPointLinkConfigProperties.CONF_PREFIX}.lifecycle"
    }
}
