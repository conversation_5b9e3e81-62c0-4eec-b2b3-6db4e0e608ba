package no.ruter.tranop.app.plan.journey.input

import no.ruter.plandata.journey.dated.v2.dto.model.dated.PDJDatedJourney
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.plan.link.DTOLinkUtils
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney

fun PDJDatedJourney?.lastArrival(): String? =
    this
        ?.plan
        ?.calls
        ?.lastOrNull()
        ?.plannedArrival

fun PDJDatedJourney?.firstDeparture(): String? =
    this
        ?.plan
        ?.calls
        ?.firstNotNullOf {
            it?.plannedDeparture
        }

fun DTODatedJourney.getQuayRefs(): Set<String> {
    if (plan == null) return emptySet()

    val res = LinkedHashSet<String>()
    MapperUtils.addRefs(res, plan.stops) { it.quayRef }
    plan.links?.forEach { DTOLinkUtils.addQuayRefs(res, it) }
    return res
}

fun DTODatedJourney.getStopPointRefs(): Set<String> {
    if (plan == null) return emptySet()

    val res = LinkedHashSet<String>()
    MapperUtils.addRefs(res, plan.stops) { it.ref }
    MapperUtils.addRefs(res, plan.calls) { it.stopPointRef }
    plan.links?.forEach { DTOLinkUtils.addStopPointRefs(res, it) }
    return res
}
