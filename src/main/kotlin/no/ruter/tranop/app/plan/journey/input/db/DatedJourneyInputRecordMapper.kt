package no.ruter.tranop.app.plan.journey.input.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputContext
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyInputRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime

class DatedJourneyInputRecordMapper(
    private val insightService: InsightService,
) : RecordMapper<DatedJourneyInputRecord, DatedJourneyInputData> {
    override fun map(record: DatedJourneyInputRecord): DatedJourneyInputData {
        val json = record.jsonData
        val journey = JsonUtils.toObject(json?.data(), DTODatedJourney::class.java)
        return DatedJourneyInputData(
            journey = journey,
            record = record,
        )
    }

    fun update(
        record: DatedJourneyInputRecord,
        context: DatedJourneyInputContext,
        now: OffsetDateTime,
    ) {
        record.ref = context.ref
        record.jsonData = JSON.valueOf(JsonUtils.toJson(context.input))
        record.operatingDate = context.operatingDate

        record.firstArrival = context.firstArrivalDateTime
        record.firstDeparture = context.firstDepartureDateTime

        record.lastArrival = context.lastArrivalDateTime
        record.lastDeparture = context.lastDepartureDateTime

        val creator = insightService.appName
        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
        }
        record.deletedAt = null
        record.deletedBy = null
        record.modifiedAt = now
        record.modifiedBy = creator
    }
}
