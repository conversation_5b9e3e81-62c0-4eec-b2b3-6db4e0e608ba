package no.ruter.tranop.app.plan.journey.lifecycle

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.tranop.app.common.lifecycle.AbstractLifeCycle
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.plan.journey.DatedJourneyStatisticsRoutine
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaPublishRoutine
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaRepublishRoutine
import no.ruter.tranop.app.plan.journey.output.DatedJourneyKafkaTombstoneRoutine
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = DatedJourneyLifeCycleConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class DatedJourneyLifeCycle(
    val timeService: TimeService,
    deleteDatedJourneyRoutine: DatedJourneyDeleteRoutine,
    publishDatedJourneyRoutine: DatedJourneyKafkaPublishRoutine,
    tombstoneDatedJourneyRoutine: DatedJourneyKafkaTombstoneRoutine,
    republishDatedJourneyRoutine: DatedJourneyKafkaRepublishRoutine,
    statisticsDatedJourneyRoutine: DatedJourneyStatisticsRoutine,
) : AbstractLifeCycle(
        routines =
            listOf(
                deleteDatedJourneyRoutine,
                publishDatedJourneyRoutine,
                tombstoneDatedJourneyRoutine,
                republishDatedJourneyRoutine,
                statisticsDatedJourneyRoutine,
            ),
    ) {
    companion object {
        const val DAY_PREFIX = "${DatedJourneyLifeCycleConfig.CONF_PREFIX}.daily"
        const val FREQ_PREFIX = "${DatedJourneyLifeCycleConfig.CONF_PREFIX}.frequent"
    }

    @Scheduled(
        cron = "\${${DAY_PREFIX}.cron}",
    )
    @SchedulerLock(
        name = "\${${DAY_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${DAY_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${DAY_PREFIX}.lockAtLeastFor}",
    )
    fun runDaily() {
        execute(
            started = timeService.now(),
            daily = true,
        )
    }

    @Scheduled(
        fixedRateString = "\${${FREQ_PREFIX}.fixedRate}",
        initialDelayString = "\${${FREQ_PREFIX}.initialDelay}",
    )
    @SchedulerLock(
        name = "\${${FREQ_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${FREQ_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${FREQ_PREFIX}.lockAtLeastFor}",
    )
    fun runFrequent() {
        execute(
            started = timeService.now(),
            frequent = true,
        )
    }
}
