package no.ruter.tranop.app.plan.journey.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.db.xref.ExternalRefType
import no.ruter.tranop.app.common.db.xref.ExternalRefs
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.JourneyUtils
import no.ruter.tranop.app.plan.journey.input.DatedJourneyInputContext
import no.ruter.tranop.app.plan.journey.input.getQuayRefs
import no.ruter.tranop.app.plan.journey.input.getStopPointRefs
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.DatedJourneyRecord
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime
import java.time.ZoneOffset

class DatedJourneyRecordMapper(
    private val insightService: InsightService,
) : RecordMapper<DatedJourneyRecord, InternalDatedJourney> {
    override fun map(record: DatedJourneyRecord): InternalDatedJourney {
        fun OffsetDateTime.normalize(): OffsetDateTime? = this.withOffsetSameInstant(ZoneOffset.UTC)

        val json = record.jsonData
        val journey = JsonUtils.toObject(json?.data(), DTODatedJourney::class.java)
        journey.lifeCycleInfo?.apply {
            this.revision = record.revision
        }

        journey.cancelled = journey.cancelled ?: false
        journey.plan.calls?.forEach {
            it.cancelled = it.cancelled ?: false
        }
        journey.journeyState = journey.journeyState ?: JourneyUtils.createJourneyState()

        val lineId = record.lineId ?: journey?.line?.lineRef
        val vehicleJourneyId = record.vehicleJourneyId ?: journey?.journeyReferences?.vehicleJourneyId

        return InternalDatedJourney(
            journey = journey,
            operatingDate = record.operatingDate,
            firstDeparture = record.firstDeparture?.normalize(),
            lastArrival = record.lastArrival?.normalize(),
            lastDeparture = record.lastDeparture?.normalize(),
            firstArrival = record.firstArrival?.normalize(),
            vehicleTaskRef = record.vehicleTaskRef,
            datedBlockRef = record.datedBlockRef,
            cancelled = record.cancelled,
            partiallyCancelled = record.partiallyCancelled,
            lineId = lineId,
            vehicleJourneyId = vehicleJourneyId,
            datedServiceJourneyId = record.datedServiceJourneyId,
            record = record,
        )
    }

    fun update(
        record: DatedJourneyRecord,
        context: DatedJourneyInputContext,
        now: OffsetDateTime,
        externalRefs: ExternalRefs,
    ) {
        record.ref = context.ref
        record.jsonData = JSON.valueOf(JsonUtils.toJson(context.input))

        record.firstDeparture = context.firstDepartureDateTime
        record.firstArrival = context.firstArrivalDateTime

        record.lastDeparture = context.lastDepartureDateTime
        record.lastArrival = context.lastArrivalDateTime

        record.operatingDate = context.operatingDate
        record.vehicleTaskRef = context.vehicleTaskRef
        record.datedBlockRef = context.datedBlockRef
        record.cancelled = context.cancelled
        record.partiallyCancelled = context.partiallyCancelled

        record.omitted = context.omitted
        record.partiallyOmitted = context.partiallyOmitted

        record.journeyType = context.journeyType?.value()

        record.externalJourneyRef = context.externalJourneyRef
        record.externalJourneyRefV2 = context.externalJourneyRefV2
        record.datedServiceJourneyId = context.datedServiceJourneyId
        record.datedJourneyV1Ref = context.datedJourneyV1Ref

        record.vehicleJourneyId = context.vehicleJourneyId
        record.lineId = context.lineId
        record.operatorContractId = context.operatorContractRef

        val creator = insightService.appName // TODO: Use something more meaningful.
        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
        }
        record.modifiedAt = now
        record.modifiedBy = creator
        record.tombstonedAt = null

        // Update external refs.
        context.input?.let { journey ->
            externalRefs.apply {
                add(ExternalRefType.LINE_ID, journey.line?.lineRef)
                add(ExternalRefType.NSR_QUAY_ID, journey.getQuayRefs())
                add(ExternalRefType.STOP_POINT_REF, journey.getStopPointRefs())
            }
        }
    }
}
