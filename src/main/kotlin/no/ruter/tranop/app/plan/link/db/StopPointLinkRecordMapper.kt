package no.ruter.tranop.app.plan.link.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.app.plan.link.input.StopPointLinkInputContext
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointLinkRecord
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime

class StopPointLinkRecordMapper(
    val insightService: InsightService,
) : RecordMapper<StopPointLinkRecord, InternalStopPointLink> {
    override fun map(record: StopPointLinkRecord): InternalStopPointLink {
        val jsonData = record.jsonData
        val stopPointLinkDto = JsonUtils.toObject(jsonData?.data(), DTOStopPointLink::class.java)
        return InternalStopPointLink(
            record = record,
            link = stopPointLinkDto,
        )
    }

    fun update(
        record: StopPointLinkRecord,
        context: StopPointLinkInputContext,
        now: OffsetDateTime,
    ) {
        val input = context.link

        record.ref = context.ref
        record.jsonData = JSON.valueOf(JsonUtils.toJson(input))
        record.jsonHash = input.hash()

        val creator = insightService.appName
        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
        }
        record.modifiedAt = now
        record.modifiedBy = creator

        record.toStopPointRef = input.toStopPointRef
        record.fromStopPointRef = input.fromStopPointRef
    }
}

fun DTOStopPointLink?.hash(): String =
    this?.let {
        MapperUtils.hash(
            JsonUtils.toJson(
                it
                    .deepCopy()
                    .apply {
                        this?.header = null
                        this?.events = null
                        this?.lifeCycleInfo = null
                    },
            ),
        )
    } ?: "null"
