package no.ruter.tranop.app.plan.journey.output.opensearch

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import no.ruter.tranop.app.plan.journey.lifecycle.DatedJourneyBaseRoutine
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class DatedJourneyOpenSearchDeleteRoutine(
    insightService: InsightService,
    appInfoProperties: AppInfoProperties,
    val datedJourneyService: DatedJourneyService,
    datedJourneyOpenSearchLifeCycleConfig: DatedJourneyOpenSearchLifeCycleConfig,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_DELETE,
        insightService = insightService,
        datedJourneyOpenSearchLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        try {
            val res = datedJourneyService.deleteExpiredFromOpenSearch()
            return res
        } catch (e: Exception) {
            handleException(e = e)
        }
        return 0
    }
}
