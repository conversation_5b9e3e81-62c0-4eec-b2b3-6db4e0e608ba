package no.ruter.tranop.app.plan.journey.output.entity

import io.confluent.kafka.streams.serdes.avro.SpecificAvroSerde
import no.ruter.avro.entity.datedjourney.v2.DatedJourneyKeyV2
import no.ruter.tranop.app.common.config.AbstractSectionConfigProperties
import no.ruter.tranop.app.common.dataflow.kafka.AbstractKafkaPublisherService
import no.ruter.tranop.app.common.dataflow.kafka.KafkaConfigService
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.config.DatedJourneyConfigProperties
import org.apache.kafka.common.serialization.Serde
import org.springframework.stereotype.Service

@Service
class DatedJourneyEntityTombstoneService(
    kafkaConfigService: KafkaConfigService,
    datedJourneyConfigProperties: DatedJourneyConfigProperties,
    insightService: InsightService,
) : AbstractKafkaPublisherService<String?, Serde<String?>, DatedJourneyKeyV2, SpecificAvroSerde<DatedJourneyKeyV2>>(
        kafkaConfigService.datedJourneyOutputProducer,
        datedJourneyConfigProperties.getKafkaOutputConfig(AbstractSectionConfigProperties.CONFIG_KEY_ENTITY),
        insightService,
    )
