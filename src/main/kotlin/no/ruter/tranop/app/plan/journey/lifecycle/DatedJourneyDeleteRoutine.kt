package no.ruter.tranop.app.plan.journey.lifecycle

import no.ruter.tranop.app.common.config.AppInfoProperties
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.plan.journey.DatedJourneyService
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Component
class DatedJourneyDeleteRoutine(
    insightService: InsightService,
    appInfoProperties: AppInfoProperties,
    val datedJourneyService: DatedJourneyService,
    datedJourneyLifeCycleConfig: DatedJourneyLifeCycleConfig,
) : DatedJourneyBaseRoutine(
        appInfoProperties = appInfoProperties,
        name = LC_TYPE_DELETE,
        insightService = insightService,
        datedJourneyLifeCycleConfig,
    ) {
    override fun execute(started: OffsetDateTime): Int {
        try {
            val res = datedJourneyService.deleteTombstoneJourneysTransactional(config.olderThan)
            return res
        } catch (e: Exception) {
            handleException(e = e)
        }
        return 0
    }
}
