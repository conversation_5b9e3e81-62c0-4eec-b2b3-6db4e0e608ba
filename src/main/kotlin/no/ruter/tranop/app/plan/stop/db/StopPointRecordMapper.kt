package no.ruter.tranop.app.plan.stop.db

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.insight.InsightService
import no.ruter.tranop.app.common.mapping.MapperUtils
import no.ruter.tranop.app.common.mapping.deepCopy
import no.ruter.tranop.app.plan.stop.input.StopPointInputContext
import no.ruter.tranop.assignment.util.toUtcIsoString
import no.ruter.tranop.assignmentmanager.db.sql.tables.records.StopPointRecord
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.stop.DTOStopPoint
import org.jooq.JSON
import org.jooq.RecordMapper
import java.time.OffsetDateTime

class StopPointRecordMapper(
    val insightService: InsightService,
) : RecordMapper<StopPointRecord, InternalStopPoint> {
    override fun map(record: StopPointRecord): InternalStopPoint {
        val jsonData = record.jsonData
        val stopPointDto =
            JsonUtils
                .toObject(
                    jsonData?.data(),
                    DTOStopPoint::class.java,
                ).apply {
                    this.header =
                        DTOMessageHeader().apply {
                            this.messageTimestamp = record.createdAt.toUtcIsoString()
                            this.receivedTimestamp = record.modifiedAt.toUtcIsoString()
                        }
                }
        return InternalStopPoint(record = record, stopPoint = stopPointDto)
    }

    fun update(
        record: StopPointRecord,
        context: StopPointInputContext,
        now: OffsetDateTime,
    ) {
        val input = context.stopPoint
        record.ref = context.ref
        record.jsonData = JSON.valueOf(JsonUtils.toJson(input))
        record.jsonHash = input.hash()

        val creator = insightService.appName
        if (record.createdAt == null) {
            record.createdAt = now
            record.createdBy = creator
        }
        record.modifiedAt = now
        record.modifiedBy = creator

        record.quayId = input.quayRef
    }
}

fun DTOStopPoint?.hash(): String =
    this?.let {
        MapperUtils.hash(
            JsonUtils.toJson(
                it
                    .deepCopy()
                    .apply {
                        this?.header = null
                        this?.events = null
                        this?.lifeCycleInfo = null
                    },
            ),
        )
    } ?: "null"
