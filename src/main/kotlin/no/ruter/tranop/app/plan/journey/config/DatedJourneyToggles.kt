package no.ruter.tranop.app.plan.journey.config

data class DatedJourneyToggles(
    var omitEnabled: <PERSON>olean = false,
    var callOmitEnabled: <PERSON>olean = false,
    var cancellationEnabled: Boolean = false,
    var callCancellationEnabled: Boolean = false,
    var callInterchangeEnabled: Boolean = false,
    var useExternalJourneyRefV2: Boolean = false,
    // TODO: `createAdHocDeadRuns` is an input config. Move to a better, more suitable home :)
    var createAdHocDeadRuns: Boolean = true,
)
