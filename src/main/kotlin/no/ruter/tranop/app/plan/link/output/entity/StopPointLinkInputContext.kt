package no.ruter.tranop.app.plan.link.output.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.AbstractInputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.output.toInsightEvent
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.common.link.DTOStopPointLink
import no.ruter.tranop.journey.dated.dto.metadata
import java.time.OffsetDateTime

class StopPointLinkInputContext(
    override val channel: DataChannel,
    val link: DTOStopPointLink,
    override val received: OffsetDateTime,
    override val ref: String = link.ref,
    override val refPath: String = "$.ref",
    override val header: DTOMessageHeader? = link.header,
    override val events: Collection<InputEvent> =
        link.events.map {
            it.toInsightEvent()
        },
) : AbstractInputContext() {
    override val metadata: Map<String, Any?>
        get() = link.metadata()
    override val metrics: Map<String, List<String>>
        get() = emptyMap()
}
