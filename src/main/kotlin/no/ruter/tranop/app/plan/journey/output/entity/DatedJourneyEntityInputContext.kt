package no.ruter.tranop.app.plan.journey.output.entity

import no.ruter.tranop.app.common.DataChannel
import no.ruter.tranop.app.common.mapping.input.AbstractInputContext
import no.ruter.tranop.app.common.mapping.input.InputEvent
import no.ruter.tranop.app.common.mapping.input.InputMetadata
import no.ruter.tranop.app.common.mapping.output.toInsightEvent
import no.ruter.tranop.assignment.util.toOffsetDateTime
import no.ruter.tranop.common.dto.model.DTOMessageHeader
import no.ruter.tranop.dated.journey.dto.model.DTODatedJourney
import no.ruter.tranop.journey.dated.dto.metadata
import java.time.OffsetDateTime

class DatedJourneyEntityInputContext(
    override val channel: DataChannel,
    private val journey: DTODatedJourney,
    override val ref: String = journey.ref,
    override val header: DTOMessageHeader? = journey.header,
    override val received: OffsetDateTime,
    override val events: Collection<InputEvent> =
        journey.events.map {
            it.toInsightEvent()
        },
) : AbstractInputContext() {
    companion object {
        const val REF_PATH = "$.ref"
    }

    override val metrics: Map<String, List<String>>
        get() = emptyMap()
    override val metadata: Map<String, Any?>
        get() = journey.metadata()

    override val inputMetadata =
        InputMetadata().apply {
            this.messageTimestamp = header?.messageTimestamp?.toOffsetDateTime()
            this.receivedTimestamp = header?.receivedTimestamp?.toOffsetDateTime()
            this.expiresTimestamp = header?.expiresTimestamp?.toOffsetDateTime()
            this.traceId = header?.traceId
            this.publisherId = header?.publisherId
            this.ownerId = header?.ownerId
            this.originId = header?.originId
        }

    override val refPath = REF_PATH
}
