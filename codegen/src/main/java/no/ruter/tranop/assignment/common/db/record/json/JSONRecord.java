package no.ruter.tranop.assignment.common.db.record.json;

import no.ruter.tranop.assignment.common.db.record.base.BaseRecord;
import org.jooq.JSON;

/**
 * Common interface implemented by all JOOQ-generated record classes representing records in tables with JSON data
 * represented as PostgreSQL <code>JSONB</code> columns.
 *
 * Note that this interface is intentioanlly implemented in Java, not Kotlin, to be compatible with Java classes
 * generated by JOOQ and default implementations of interface methods.
 **/
public interface JSONRecord extends BaseRecord {
    JSON getJsonData();
    void setJsonData(JSON value);

    default String getJsonHash() { return null; };
    default void setJsonHash(String value) { };
}
