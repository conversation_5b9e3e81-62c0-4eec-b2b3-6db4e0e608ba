package no.ruter.tranop.assignment.jooq;

import no.ruter.tranop.assignment.common.db.record.base.BaseRecord;
import no.ruter.tranop.assignment.common.db.record.json.JSONRecord;
import org.jooq.codegen.DefaultGeneratorStrategy;
import org.jooq.meta.Definition;
import org.jooq.meta.TableDefinition;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Custom JOOQ Generator Strategy. Nothing fancy here, just a work-around for not being able to get the matcher
 * strategy to work properly with JOOQ v3.16.4.
 */
public class JooqGeneratorStrategy extends DefaultGeneratorStrategy {
    private static final Set<String> IGNORED_TABLES = new HashSet<>(List.of(
        "shedlock",
        "external_ref"
    ));

    private static final List<String> BASE_RECORD_INTERFACES = Collections.singletonList(
        BaseRecord.class.getName()
    );

    private static final List<String> JSON_RECORD_INTERFACES = Collections.singletonList(
        JSONRecord.class.getName()
    );

    @Override
    public String getJavaClassName(Definition def, Mode mode) {
        var res = super.getJavaClassName(def, mode);
        var table = mode == Mode.DEFAULT && TableDefinition.class.isAssignableFrom(def.getClass());
        return table ? (res + "Table") : res;
    }

    @Override
    public List<String> getJavaClassImplements(Definition def, Mode mode) {
        var name = def.getName();
        if (mode != Mode.RECORD || IGNORED_TABLES.contains(name)) {
            return Collections.emptyList();
        }

        var table = (TableDefinition) def;
        var jsonRecord = table.getColumn("json_data") != null;
        return jsonRecord ? JSON_RECORD_INTERFACES : BASE_RECORD_INTERFACES;
    }
}
