pluginManagement {
    repositories {
        mavenLocal()
        mavenCentral()
        gradlePluginPortal()
        maven {
            name = "gitLabGroupTransportoppdragMaven"
            setUrl("https://gitlab.com/api/v4/groups/57160432/-/packages/maven")
            credentials(HttpHeaderCredentials::class.java) {
                val jobToken = System.getenv("CI_JOB_TOKEN")
                name = if (jobToken != null) "Job-Token" else "Private-Token"
                value = jobToken ?: providers.gradleProperty("gitLabPrivateToken").orNull ?: error(
                    "Missing required Gradle property (~/.gradle/gradle.properties): gitLabPrivateToken",
                )
            }
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }
}

rootProject.name = "assignment-journey-manager"

include("codegen")
